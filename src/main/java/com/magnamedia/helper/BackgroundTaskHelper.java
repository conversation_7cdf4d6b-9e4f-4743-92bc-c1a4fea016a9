package com.magnamedia.helper;


/*
 * <AUTHOR>
 * @created 02/07/2024 - 9:15 PM
 * ACC-7640
 */

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.service.QueryService;
import org.joda.time.LocalDate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class BackgroundTaskHelper {

    private static final Logger logger = Logger.getLogger(BackgroundTaskHelper.class.getSimpleName());

    public static boolean checkIfFoundAnyBGTParsingStatementUploaded(UploadStatementEntityType relatedEntityType) {

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("module", "=", Setup.getCurrentModule().getCode());
        query.filterBy("targetBean", "=", relatedEntityType.getTargetBean());
        query.filterBy("targetMethod", "=", relatedEntityType.getTargetMethod());
        query.filterBy("status", "not in", Arrays.asList(BackgroundTaskStatus.Finished,
                BackgroundTaskStatus.Failed));

        return !query.execute().isEmpty();
    }

    public static void createBGTParsingStatementUploaded(
            UploadStatementEntityType relatedEntityType, String nameBGT,
            Map<String, Object> payload) {

        if(checkIfFoundAnyBGTParsingStatementUploaded(relatedEntityType)) {
            throw new BusinessException("There’s another file under parsing, please wait …");
        }
        Object entityId =  payload.getOrDefault("entityId", null);
        Long parsedEntityId = null;
        if (entityId != null) {
            if (entityId instanceof String) {
                parsedEntityId = Long.parseLong((String) entityId);
            } else if (entityId instanceof Integer) {
                parsedEntityId = ((Integer) entityId).longValue();
            } else if (entityId instanceof Long) {
                parsedEntityId = (Long) entityId;
            }
        }

        String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_QUEUE_BGT_OF_STATEMENT_PARSING_PROCESSES);
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        nameBGT,
                        "accounting",
                        relatedEntityType.getTargetBean(),
                        relatedEntityType.getTargetMethod())
                        .withRelatedEntity(relatedEntityType.name(), parsedEntityId)
                        .withPreventDuplicateExecution()
                        .withParameters(
                                new Class[] { Map.class },
                                new Object[] { payload })
                        .withQueue(BackgroundTaskQueues.valueOf(queue))
                        .build());
    }

    public static void createBGTSendEmailForMissingTransactionPostingRule(
            Contract contract, PicklistItem typeOfPayment, PaymentMethod methodOfPayment,
            Boolean isInitial, PaymentStatus paymentStatus, Long paymentId) {
        String bgtName = "send_email_for_transaction_posting_rule_" + contract.getContractProspectType().getCode() +
                "_" + typeOfPayment.getCode() + "_" + methodOfPayment.getValue() + "_isInitial:" + isInitial +
                "_" + paymentStatus.getValue() + "_" + new LocalDate().toString("yyyy_MM_dd");
        logger.info("bgtName :  " + bgtName);

        synchronized (bgtName.intern()) {
            if (ConcurrentModificationHelper.isExistInGlobalData(bgtName) ||
                    QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status <> 'Failed'", new Object[]{bgtName})) {
                logger.info("there is a running BGT for payment type : " + typeOfPayment.getCode());
                return;
            }
            logger.info("new bgt will be created with name : " + bgtName);
            ConcurrentModificationHelper.lockGlobalDataNewKey(bgtName, "");
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                            bgtName,
                            "accounting",
                            "officeStaffMessagingService",
                            "sendEmailForTransactionPostingRules")
                            .withPreventDuplicateExecution()
                            .withParameters(
                                    new Class[] { Map.class },
                                    new Object[] { new HashMap<String, String>() {{
                                        put("type_of_payment", typeOfPayment.getName());
                                        put("method", methodOfPayment.getLabel());
                                        put("contract_type", contract.isMaidVisa() ? "Maid Visa" : "Maid CC");
                                        put("is_initial", isInitial? "Yes" : "No");
                                        put("payment_id", String.valueOf(paymentId));
                                        put("contract_id", String.valueOf(contract.getId()));
                                        put("client_id", String.valueOf(contract.getClient().getId()));
                                        put("bgtName", bgtName);
                                    }}})
                            .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                            .build());
        }
    }
}