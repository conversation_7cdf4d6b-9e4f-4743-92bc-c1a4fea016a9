package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.*;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/contract")
public class ContractController extends BaseRepositoryController<Contract> {

    // ACC-1435
    public static final String SIGNATURE_FILE_TAG_NAME = "contract_signature";
    public static final String MAID_VISA_AGREEMENT_FILE_SENT_BY_MAIL = "maid_visa_agreement_file_sent_by_mail";
    public static final String FILE_TAG_MV_CONTRACT_AGREEMENT = "mv_contract_agreement";

    @Autowired
    ContractRepository contractRepository;

    @Autowired
    PaymentRepository paymentRepository;

    @Autowired
    ProjectionFactory projectionFactory;

    @Autowired
    private ContractPaymentTermController cptController;

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;

    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;

    @Autowired
    private ContractPaymentTermRepository cptRepo;

    @Autowired
    private SwitchingNationalityService switchingNationalityService;

    @Autowired
    private ContractPaymentWrapperRepository contractPaymentWrapperRepository;
    @Autowired
    private ContractService contractService;

    @Autowired
    private InterModuleConnector interModuleConnector;

    // Jira ACC-5161
    @Autowired
    private FlowEventConfigRepository flowEventConfigRepository;

    @Autowired
    private  FlowProcessorEntityRepository flowProcessorEntityRepository;

    @Override
    public BaseRepository<Contract> getRepository() {
        return contractRepository;
    }

    @NoPermission
    @RequestMapping("searchContract")
    protected ResponseEntity<?> searchContract(@RequestParam(name = "search", required = false) String searchQuery, Pageable pageable) {

        SelectQuery<Contract> query = new SelectQuery<>(Contract.class);
        if (searchQuery != null && !searchQuery.equals(""))
            query.filterBy("id", "=", Long.parseLong(searchQuery));

        return new ResponseEntity<>(query.execute(pageable).map(maid -> projectionFactory.createProjection(ContractProjection.class,
                maid)), HttpStatus.OK);
    }


    public interface ContractProjection {
        Long getId();

        String getLabel();
    }

    public void terminateContract(Contract c, String terminationReason) {
        logger.log(Level.SEVERE, "ContractController terminateContract contract status: " + c.getStatus());
        logger.log(Level.SEVERE, "ContractController terminateContract terminationReason: " + terminationReason);
        if (c.getStatus() == ContractStatus.CANCELLED)
            return;

        String apiUrl = "/clientmgmt/contractTerminationNew/terminateunscheduledcontract?contractId=" + c.getId();

        Map<String, Object> body = new HashMap<>();
        PicklistItem item = getItem(AccountingModule.PICKLIST_TERMINATION_REASON_LIST, terminationReason);
        body.put("id", item.getId());


        logger.log(Level.SEVERE, "ContractController terminateContract body: " + body.toString());
        interModuleConnector.postJsonAsync(apiUrl, body);
        logger.log(Level.SEVERE, "ContractController terminateContract Done");

    }

    @NoPermission
    @RequestMapping("/getContractTemplate/{contractUUID}")
    public void getContractTemplate(
            @PathVariable("contractUUID") String contractUUID,
            HttpServletResponse response) {

        if (contractUUID == null || contractUUID.isEmpty()) throw new RuntimeException("Contract not found");

        Contract contract = contractRepository.findByUuid(contractUUID);
        if (contract == null) throw new RuntimeException("Contract not found");

        byte[] contractTemplateByteArray = interModuleConnector.postJson(
                "/sales/contract/generatecontractfile/" + contract.getId() + "?withStamp=false",
                new HashMap<>(), byte[].class);

        createDownloadResponse(response,
                "Contract-Template.pdf",
                new ByteArrayInputStream(contractTemplateByteArray));
    }


    @NoPermission
    @RequestMapping("/getPaymentReceipt")
    public void getPaymentReceipt(@RequestParam(name = "contractUUID", required = false) Optional<String> contractUUID,
                                  @RequestParam(name = "contractextend", required = false, defaultValue = "false") boolean contractExtend,
                                  @RequestParam(name = "extendduration", required = false, defaultValue = "0") int extendDuration,
                                  @RequestParam(name = "isCash", required = false, defaultValue = "false") boolean isCash,
                                  HttpServletResponse response) throws Exception {
        if (contractUUID == null || !contractUUID.isPresent()) throw new RuntimeException("Contract not found");

        Contract contract = contractRepository.findByUuid(contractUUID.get());

        if (contract == null) {
            throw new RuntimeException("Contract not found");
        }

        ContractPaymentTerm cpt;
        Map termResult = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (termResult == null)
            cpt = null;
        else
            cpt = (ContractPaymentTerm) termResult.get("contractPaymentTerm");

        if (cpt == null)
            throw new RuntimeException("No active contractPaymentTerm");

        InputStream in = null;

        try {
            in = PaymentReceiptHelper.getPaymentsReceiptStream(
                    cpt, null, null, null, cpt.getPaymentTermsTemplate());

            createDownloadResponse(response, "Payment-Receipt.pdf", in);
        } finally {
            StreamsUtil.closeStream(in);
        }
    }

    @NoPermission
    @GetMapping("/getContractPreviewFiles")
    public ResponseEntity<?> getContractPreviewFiles(String contractUUID, HttpServletResponse response) {

        List<InputStream> files = new ArrayList<>();
        try {

            Contract contract = contractRepository.findByUuid(contractUUID);
            byte[] mvGovernmentContract = null;
            try {
                mvGovernmentContract  = interModuleConnector.postJson(
                        "/sales/contract/generatecontractfilebyuuid/" + contract.getUuid() + "?withStamp=false",
                        new HashMap<>(), byte[].class);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (mvGovernmentContract != null) files.add(new ByteArrayInputStream(mvGovernmentContract));

            InputStream paymentTermsForm = null;
            try {
                ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                paymentTermsForm = PaymentReceiptHelper.getFuturePaymentsReceiptStream(cpt, cpt.getPaymentTermsTemplate());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (paymentTermsForm != null) files.add(paymentTermsForm);

            InputStream taxInvoice = null;
            try {
                taxInvoice = contractService.getTaxInvoice(contract, false, 0, false);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (taxInvoice != null) files.add(taxInvoice);

            if (files.isEmpty()) return notFoundResponse();

            File res = PdfHelper.mergePdfStreams(files, "Contract preview.pdf");
            createDownloadResponse(response, "Contract preview.pdf", new FileInputStream(res));

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            files.forEach(StreamsUtil::closeStream);
        }
        return okResponse();
    }

    @NoPermission
    @RequestMapping("/getTaxInvoice")
    public void getTaxInvoice(
            @RequestParam(name = "contractUUID") Optional<String> contractUUID,
            @RequestParam(name = "contractextend", required = false, defaultValue = "false") boolean contractExtend,
            @RequestParam(name = "extendduration", required = false, defaultValue = "0") int extendDuration,
            @RequestParam(name = "isCash", required = false, defaultValue = "false") boolean isCash,
            HttpServletResponse response) throws Exception {

        InputStream in = null;
        try {
            if (!contractUUID.isPresent()) throw new RuntimeException("Contract not found");
            Contract contract = contractRepository.findByUuid(contractUUID.get());

            if (contract == null) throw new RuntimeException("Contract not found");

            in = contractService.getTaxInvoice(contract, contractExtend, extendDuration, isCash);
            createDownloadResponse(response, "Tax-Invoice.pdf", in);

        } finally {
            StreamsUtil.closeStream(in);
        }
    }

    @Transactional
    public void upgradeNationality(Long contractId) throws Exception {
        logger.info("upgradeNationality, Contract#: " + contractId);

        Contract contract = getRepository().findOne(contractId);
        if (!contract.isActive()) {
            logger.info("Contract is not active -> do nothing");
            return;
        }
        
        Date upgradingNationalityDate = contract.getUpgradingNationalityDate();

        if (upgradingNationalityDate == null) {
            logger.info("Contract Switching Date is NULL -> do nothing");
            return;
        }

        DateTime replacementDate = new DateTime(upgradingNationalityDate);
        Housemaid newHousemaid = contract.getHousemaid();

        if (newHousemaid == null) {
            logger.info("No Maid Linked with the Contract-> do nothing");
            return;
        }

        Housemaid currentHousemaid = contract.getActiveContractPaymentTerm().getHousemaid();

        PicklistItem newNationality = newHousemaid.getNationality();
        PicklistItem currentNationality = currentHousemaid.getNationality();

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType =
                switchingNationalityService.getSwitchingNationalityType(currentHousemaid, newHousemaid);

        Replacement replacement = contract.getReplacements() != null
                && !contract.getReplacements().isEmpty() ? contract.getReplacements().get(0) : null;

        if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.UPGRADING)) {
            // ACC-5498 upgrade to same price -> keep old dd and create new cpt
            if (switchingNationalityService.isSwitchingToSamePrice(contract, newHousemaid, replacementDate)) {
                switchingNationalityService.executeSameGradeFlow(contract, newHousemaid, replacement, replacementDate, true);
            } else {
                logger.info("Contract still upgrading nationality -> do switching");
                Boolean keepCurrentDDs = replacement != null && replacement.getKeepCurrentDDs() != null ? replacement.getKeepCurrentDDs() : false;
//            boolean addVatDDs = replacement != null ? replacement.isAddVatDDs() : false;
//            boolean fromCCApp = replacement != null ? replacement.isFromCCApp() : false;

                logger.info("sending amending DDs email, Contract#" + contract.getId());
                boolean emailSentSuccessfully = cptController.sendSwitchNationalityAmendingDirectDebitFormsEmail(
                        contract.getId(), newHousemaid.getId());
                logger.info("Email Sending Result: " + emailSentSuccessfully);

            ContractPaymentTerm newCPT = switchingNationalityService.switchMaid(contractId, newHousemaid.getId(), replacementDate, false, keepCurrentDDs, false, true);
                newCPT.setReplacement(replacement);
                cptRepo.save(newCPT);
            }
        } else {
            // A new replacement was made -> already handled in BR
            logger.info("Client isn't downgrading nationality anymore -> just remove switching Date");
            logger.log(Level.INFO,"currentHousemaid: {0}; currentNationality: {1}; newHousemaid: {2}; newNationalityName: {3}"
                    , new Object[] {currentHousemaid.getId(), currentNationality.getName(), newNationality.getId(), newNationality.getName()});
        }

        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.UPGRADING_NATIONALITY_DATE, contract);
        if (a != null) {
            a.setIsDeleted(true);
            accountingEntityPropertyRepository.save(a);
        }
    }

    @Transactional
    public void downgradeNationality(Long contractId) throws Exception {
        logger.info("downgradeNationality, Contract#" + contractId);

        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        Contract contract = getRepository().findOne(contractId);
        if (!contract.isActive()) {
            logger.info("Contract is not active -> do nothing");
            return;
        }
        
        Date downgradingNationalityDate = contract.getDowngradingNationalityDate();

        if (downgradingNationalityDate == null) {
            logger.info("Contract Switching Date is NULL -> do nothing");
            return;
        }

        if (contract.getHousemaid() == null || contract.getHousemaid().getId() == null) {
            logger.info("No Maid Linked with the Contract-> do nothing");
            AccountingEntityProperty switchingNationalityJobPassedWhileHasNoMaid = new AccountingEntityProperty();
            switchingNationalityJobPassedWhileHasNoMaid.setOrigin(contract);
            switchingNationalityJobPassedWhileHasNoMaid.setKey(Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID);
            switchingNationalityJobPassedWhileHasNoMaid.setValue(Boolean.TRUE.toString());
            accountingEntityPropertyRepository.save(switchingNationalityJobPassedWhileHasNoMaid);

        } else {
            logger.info("do Switching");

            //The client still has the new nationality
            List<Replacement> replacements = contract.getReplacements();
            Replacement replacement = replacements != null
                    && !replacements.isEmpty() ? replacements.get(0) : null;
            DateTime replacementDate = replacement != null ? new DateTime(replacement.getCreationDate())
                    :  DateTime.now();
            Housemaid newHousemaid = contract.getHousemaid();
            Housemaid currentHousemaid = contract.getActiveContractPaymentTerm().getHousemaid();

            PicklistItem newNationality = newHousemaid.getNationality();
            PicklistItem currentNationality = currentHousemaid.getNationality();

            SwitchingNationalityService.SwitchingNationalityType switchingNationalityType =
                    switchingNationalityService.getSwitchingNationalityType(currentHousemaid, newHousemaid);

            if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING)) {
                // ACC-5498 downgrade to same price -> keep old dd and create new cpt
                if (switchingNationalityService.isSwitchingToSamePrice(contract, newHousemaid, replacementDate)) {
                    switchingNationalityService.executeSameGradeFlow(contract, newHousemaid, replacement, replacementDate, true);

                }else {
                    logger.info("Contract still downgrading nationality -> do switching");

                    Boolean keepCurrentDDs = replacement != null && replacement.getKeepCurrentDDs() != null ? replacement.getKeepCurrentDDs() : false;
//                boolean addVatDDs = replacement != null ? replacement.isAddVatDDs() : false;
//                boolean fromCCApp = replacement != null ? replacement.isFromCCApp() : false;

                    logger.info("sending amending DDs email, Contract#" + contract.getId());
                    boolean emailSentSuccessfully = cptController.sendSwitchNationalityAmendingDirectDebitFormsEmail(
                            contract.getId(), newHousemaid.getId());
                    logger.info("Email Sending Result: " + emailSentSuccessfully);

                ContractPaymentTerm newCPT = switchingNationalityService.switchMaid(contractId, newHousemaid.getId(), replacementDate, false, keepCurrentDDs, false, true);
                    newCPT.setReplacement(replacement);
                    cptRepo.save(newCPT);
                }
            } else {
                // A new replacement was made -> already handled in BR
                logger.info("Client isn't downgrading nationality anymore -> just remove switching Date");
                logger.log(Level.INFO,"currentHousemaid: {0}; currentNationality: {1}; newHousemaid: {2}; newNationalityName: {3}"
                        , new Object[] {currentHousemaid.getId(), currentNationality.getName(), newNationality.getId(), newNationality.getName()});
            }
        }

        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.DOWNGRADING_NATIONALITY_DATE, contract);
        if (a != null) {
            a.setIsDeleted(true);
            accountingEntityPropertyRepository.save(a);
        }
    }

    //ACC-2903
    @PreAuthorize("hasPermission('contract','getIntialandProratedAllowedPayments')")
    @RequestMapping(value = "/getIntialandProratedAllowedPayments/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> getIntialandProratedAllowedPayments(
            @PathVariable("id") Contract contract) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        if (cpt == null)
            throw new RuntimeException("There is no Active Contract Payment Term.");

        Map<String, Integer> result = new HashMap<>();
        if (contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))){
            List<Payment> proPayments = paymentRepository.findByContractAndIsProRatedAndStatusIn(contract, true,
                    Arrays.asList(PaymentStatus.PDC, PaymentStatus.PRE_PDP, PaymentStatus.RECEIVED));
            List<ContractPaymentWrapper> proContractPaymentWrappers =
                    contractPaymentWrapperRepository.
                            findByContractPaymentConfirmationToDo_ContractPaymentTerm_ContractAndProratedAndContractPaymentConfirmationToDo_Confirmed(
                                    contract, true, false);
            result.put("numberOfProrated", ((proPayments != null && !proPayments.isEmpty()) || (proContractPaymentWrappers != null && !proContractPaymentWrappers.isEmpty())) ? 0 : 1);
        }
        else
            result.put("numberOfProrated", (0));

        List<Payment> initialPayments = paymentRepository.findByContractAndIsInitialAndStatusIn(contract, true,
                Arrays.asList(PaymentStatus.PDC, PaymentStatus.PRE_PDP, PaymentStatus.RECEIVED));
        List<ContractPaymentWrapper> initialContractPaymentWrappers =
                contractPaymentWrapperRepository.
                        findByContractPaymentConfirmationToDo_ContractPaymentTerm_ContractAndInitialAndContractPaymentConfirmationToDo_Confirmed(
                                contract, true, false);

        int allowedInitialPayments = cpt.getDiscountEffectiveAfter();
        if (initialPayments != null && !initialPayments.isEmpty())
            allowedInitialPayments -= initialPayments.size();
        if (initialContractPaymentWrappers != null && !initialContractPaymentWrappers.isEmpty())
            allowedInitialPayments -= initialContractPaymentWrappers.size();
        result.put("numberOfInitial", allowedInitialPayments > 0 ? allowedInitialPayments : 0);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    //ACC-2903
    @PreAuthorize("hasPermission('contract','getUnusedDaysForCompensatingContract')")
    @RequestMapping(value = "/getUnusedDaysForCompensatingContract/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> getUnusedDaysForCompensatingContract(
            @PathVariable("id") Contract contract) {

        if (contract != null) {

            HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
            query.filterBy("id", "=", contract.getId());
            query.filterByChanged("housemaid");
            query.sortBy("lastModificationDate", true);
            List<Contract> records = query.execute();

            int numberOfUnusedDays = 0;
            Date tempDate = null;

            for (Contract row : records) {

                if (row.getHousemaid() == null) {
                    tempDate = row.getLastModificationDate();
                } else {
                    if (tempDate != null) {
                        numberOfUnusedDays += DateUtil.getDaysBetween(tempDate, row.getLastModificationDate());
                        tempDate = null;
                    }
                }
            }

            if (records.size() > 0) {
                Contract lastRecord = records.get(records.size() - 1);
                if (lastRecord.getHousemaid() == null) {
                    numberOfUnusedDays += DateUtil.getDaysBetween(lastRecord.getLastModificationDate(), DateUtil.now());
                }
            }

            Map<String, Integer> result = new HashMap<>();
            result.put("numberOfUnusedDays", numberOfUnusedDays);
            return new ResponseEntity<>(result, HttpStatus.OK);
        } else {
            throw new RuntimeException("Contract not found.");
        }
    }

    @PreAuthorize("hasPermission('contract','contractsForInfo')")
    @RequestMapping(value = "/contractsForInfo",
            method = RequestMethod.GET)
    public ResponseEntity<?> contractsForInfo(
            @RequestParam(name = "contractId", required = false,defaultValue = "") String contractId,Pageable pageable) {
        List<Long> ids=contractRepository.findByIdStartWith(contractId,pageable);
        return new ResponseEntity<>(ids, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('contract','contractPaymentPlan')")
    @RequestMapping(value = "/contractPaymentPlan/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> contractPaymentPlan(
            @PathVariable("id") Contract contract) {
        if (contract != null) {
            boolean mvContract = contract.getContractProspectType().getCode().
                    equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));
            if(mvContract){
                Picklist Picklist = Setup.getRepository(PicklistRepository.class)
                        .findByCode(AccountingModule.MV_PAYMENT_PLAN_OPTIONS);
                return new ResponseEntity<>(Picklist.getItems().stream()
                        .filter(item -> !item.hasTag("deprecated"))
                        .collect(Collectors.toList()), HttpStatus.OK);
            }else{
                Picklist Picklist = Setup.getRepository(PicklistRepository.class)
                        .findByCode(AccountingModule.CC_PAYMENT_PLAN_OPTIONS);
                return new ResponseEntity<>(Picklist.getItems().stream()
                        .filter(item -> !item.hasTag("deprecated"))
                        .collect(Collectors.toList()), HttpStatus.OK);
            }

        } else {
            throw new RuntimeException("Contract not found.");
        }
    }

    @PreAuthorize("hasPermission('contract','submitContractInfo')")
    @RequestMapping(value = "/submitContractInfo",
            method = RequestMethod.POST)
    public ResponseEntity submitCotractInfo(@RequestBody ContractInfo contractInfo) {

        if (contractInfo.getContract()!=null && contractInfo.getContract().getId() != null) {
            ContractInfoRepository contractInfoRepository=Setup.getRepository(ContractInfoRepository.class);
            ContractInfo contractInfoClone=contractInfoRepository.findByContract(contractInfo.getContract());
            PicklistItem paymentPlan=contractInfo.getGeorgePaymentPlan();
            if(paymentPlan!=null && paymentPlan.getId() == null && paymentPlan.getName() != null){
                PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
                Contract contract=contractRepository.findOne(contractInfo.getContract().getId());
                if(contract!=null){
                    boolean mvContract = contract.getContractProspectType().getCode().
                            equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));
                    Picklist picklist;
                    if(mvContract){
                        picklist = picklistRepository.findByCode(AccountingModule.MV_PAYMENT_PLAN_OPTIONS);
                    }else{
                        picklist = picklistRepository.findByCode(AccountingModule.CC_PAYMENT_PLAN_OPTIONS);
                    }
                    if (picklist != null) {
                        picklist.addItem(paymentPlan.getName());
                        picklist = picklistRepository.save(picklist);
                        contractInfo.setGeorgePaymentPlan(PicklistHelper.getItem(picklist.getCode(), paymentPlan.getName()));
                    }
                }
            }
            if(contractInfoClone!=null){
                if(contractInfo.getGeorgePaymentPlan() != null)
                        contractInfoClone.setGeorgePaymentPlan(contractInfo.getGeorgePaymentPlan());
                if(contractInfo.getGeorgePaymentNotes() != null)
                    contractInfoClone.setGeorgePaymentNotes(contractInfo.getGeorgePaymentNotes());
                if(contractInfo.getGeorgeDiscountAmount()!=null)
                    contractInfoClone.setGeorgeDiscountAmount(contractInfo.getGeorgeDiscountAmount());
                if(contractInfo.getGeorgeDiscountNote() != null)
                    contractInfoClone.setGeorgeDiscountNote(contractInfo.getGeorgeDiscountNote());
            }else {
                contractInfoClone=contractInfo;
            }
            contractInfoRepository.save(contractInfoClone);
            return okResponse();
        }
        return  badRequestResponse();
    }


    // ACC-5210
    @PostMapping("/migrateAcc5210")
    @Transactional
    public ResponseEntity<?> migrateAcc5210() {
        List<Object[]> cpts = cptRepo.findWithWeeklyPackage(-1L);

        while(!cpts.isEmpty()) {
            Long lastId = Long.valueOf(cpts.get(cpts.size() - 1)[0].toString());

            for (Object[] o : cpts) {
                try {
                    ContractPaymentTerm cpt = cptRepo.findOne(Long.valueOf(o[0].toString()));
                    cpt.setWeeklyAmount(Double.valueOf(o[1].toString()));
                    cpt.setDailyRateAmount(Double.valueOf(o[2].toString()));
                    cptRepo.save(cpt);
                } catch (Exception e) {
                    logger.severe("Error in migrateAcc5210 for CPT: " + o[0]);
                }
            }

            cpts = cptRepo.findWithWeeklyPackage(lastId);
        }

        return okResponse();
    }

    // ACC-5794
    @PreAuthorize("hasPermission('contract','allowEditPaperMode')")
    @GetMapping("/allowEditPaperMode/{id}")
    public ResponseEntity<?> allowEditPaperMode(
            @PathVariable("id") Contract contract) {

        boolean hasPosition = CurrentRequest.getUser().hasPosition(AccountingModule.ALLOW_EDIT_PAPER_MODE_POSITION);
        boolean rejectionFlowRunning = Setup.getApplicationContext()
                .getBean(DirectDebitRejectionToDoRepository.class)
                .existsActiveTodoByCategory(contract.getId(), DirectDebitRejectCategory.Signature);

        return new ResponseEntity<>(
                new HashMap<String, Object>() {{
                    put("signingPaperMode", contractRepository.findOne(contract.getId()).isSigningPaperMode());
                    put("allowEditPaperMode", hasPosition && rejectionFlowRunning);
        }}, HttpStatus.OK);
    }

    // ACC-5794
    @PreAuthorize("hasPermission('contract','allowEditPaperMode')")
    @GetMapping("/updatePaperMode/{id}")
    public ResponseEntity<?> updatePaperMode(
            @PathVariable("id") Contract contract,
            boolean signingPaperMode) {

        if (CurrentRequest.getUser().hasPosition(AccountingModule.ALLOW_EDIT_PAPER_MODE_POSITION))
            contractService.updatePaperModeAsync(
                    contract.getActiveContractPaymentTerm(), signingPaperMode);

        return okResponse();
    }

    @PreAuthorize("hasPermission('contract','getContractDetailsCM3720')")
    @GetMapping(value = "/getContractDetailsCM3720/{id}")
    public ResponseEntity<?> getContractDetailsCM3720(@PathVariable("id") Contract c, @RequestParam String sectionName) {

        return ResponseEntity.ok(contractService.getContractDetailsCM3720(c, sectionName));
    }

    @PreAuthorize("hasPermission('contract', 'updatePayingViaCreditCardFlag')")
    @GetMapping("/updatePayingViaCreditCardFlag/{id}")
    public ResponseEntity<?> updatePayingViaCreditCardFlag(
            @PathVariable(name = "id") Contract contract,
            @RequestParam(name = "payingViaCreditCard", defaultValue = "true", required = false) boolean payingViaCreditCard) {

        contractService.updatePayingViaCreditCardFlag(contract, payingViaCreditCard);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('contract', 'getTheRefundAndPaidEndDateFromContract')")
    @GetMapping("/getTheRefundAndPaidEndDateFromContract/{id}")
    public ResponseEntity<?> getTheRefundAndPaidEndDateFromContract(
            @PathVariable(name = "id") Contract contract) {
        Map<String, Object> r = new HashMap<>();
        int days = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_NUMBER_OF_DAYS_TO_CALCULATE_REFUND_AMOUNT));
        r.put("paidRefunds", Setup.getRepository(ClientRefundTodoRepository.class)
                .findTheAmountByContractAndStatusAndCreationDateGreaterThanOrEqual(
                        contract,
                        ClientRefundStatus.PAID,
                        new LocalDate().minusDays(days).toDate()));
        r.put("pendingRefunds", Setup.getRepository(ClientRefundTodoRepository.class)
                .findTheAmountByContractAndStatusAndCreationDateGreaterThanOrEqual(
                        contract,
                        ClientRefundStatus.PENDING,
                        new LocalDate().minusDays(days).toDate()));
        r.put("paidEndDate", contract.getPaidEndDate());
        return ResponseEntity.ok(r);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('contract','updateReceivePaymentNotifications')")
    @GetMapping(value = "/updateReceivePaymentNotifications/{id}")
    public ResponseEntity<?> updateReceivePaymentNotifications(@PathVariable("id") Contract contract, boolean enabled) {

        contract.setReceivePaymentNotifications(enabled ?
                Contract.ReceivePaymentNotificationsStatus.MANUALLY_ENABLED :
                Contract.ReceivePaymentNotificationsStatus.DISABLED);
        contractRepository.silentSave(contract);

        if (enabled) {
            List<PushNotification> l = Setup.getRepository(DisablePushNotificationRepository.class)
                    .findActiveNotifications(contract.getClient().getId().toString(), contract.getId(),
                            Arrays.asList(CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString(),
                                    MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString()));

            Setup.getApplicationContext()
                    .getBean(PushNotificationHelper.class)
                    .stopDisplaying(l);
        } else {
            Setup.getApplicationContext()
                    .getBean(DisableAccountingNotificationService.class)
                    .moveReceivePaymentNotificationsToInbox(contract);
        }

        return okResponse();
    }

    @JwtSecured
    @PreAuthorize("hasPermission('contract','getReceivePaymentNotificationsStatus')")
    @GetMapping(value = "/getReceivePaymentNotificationsStatus/{id}")
    public ResponseEntity<?> getReceivePaymentNotificationsStatus(@PathVariable("id") Contract contract) {

        return ResponseEntity.ok(new HashMap<String, Object>(){{
            put("enabled", contract.getReceivePaymentNotifications() == null ||
                    Contract.ReceivePaymentNotificationsStatus.AUTOMATICALLY_ENABLED.equals(contract.getReceivePaymentNotifications()) ||
                    Contract.ReceivePaymentNotificationsStatus.MANUALLY_ENABLED.equals(contract.getReceivePaymentNotifications()));
        }});
    }

    // ACC-8069
    @UsedBy(modules = UsedBy.Modules.Client_Management)
    @PreAuthorize("hasPermission('contract','isClientInTrialPeriod')")
    @GetMapping(value = "/isClientInTrialPeriod/{id}")
    public ResponseEntity<?> isClientInTrialPeriod(@PathVariable("id") Long contractId) {

        return ResponseEntity.ok(contractService.isClientInTrialPeriod(contractId));
    }

    @PreAuthorize("hasPermission('contract', 'getContractCreditCardInfo')")
    @GetMapping(value = "/getContractCreditCardInfo/{id}")
    public ResponseEntity<?> getContractCreditCardInfo(@PathVariable("id") Contract contract) {

        return ResponseEntity.ok(contractService.getContractCreditCardInfo(contract));
    }

    @PreAuthorize("hasPermission('contract', 'generateAddNewCreditCardLink')")
    @GetMapping(value = "/generateAddNewCreditCardLink/{id}")
    public ResponseEntity<?> generateAddNewCreditCardLink(@PathVariable("id") Contract contract ) {

        if (!ContractService.isEligibleForTokenizationViaContract(contract)) {
            throw new BusinessException("Tokenization is not allowed for the contract");
        }

        ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService = Setup.getApplicationContext().
                getBean(ContractPaymentConfirmationToDoService.class);

        List<ContractPaymentConfirmationToDo> toDos = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                .findByContractPaymentTerm_ContractAndSourceAndDisabledFalse(
                        contract, ContractPaymentConfirmationToDo.Source.AUTHORIZATION);

        ContractPaymentConfirmationToDo todo = !toDos.isEmpty() ?
                toDos.get(0) :
                contractPaymentConfirmationToDoService
                        .generateConfirmationToDoForAuthorization(contract.getActiveContractPaymentTerm(), null);

        return ResponseEntity.ok(new HashMap<String, Object>(){{
            put("link", todo != null ?
                    contractPaymentConfirmationToDoService.getPayingViaCreditCardLink(todo, true, false) :
                    "");}});

    }

    @PreAuthorize("hasPermission('contract', 'setAllowCreditCardToken')")
    @GetMapping(value = "/setAllowCreditCardToken/{id}/{allowRecurring}")
    public ResponseEntity setAllowCreditCardToken(
            @PathVariable("id") Contract contract,
            @PathVariable("allowRecurring") Boolean allowRecurring) {

        Map<String, Object> m = new HashMap<>();
        m.put("id", contract.getId());
        m.put("allowRecurring", allowRecurring);
        contractService.updateContractFromClientMgt(m);

        if (allowRecurring) return okResponse();

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        if (cpt.getSourceId() != null) {
            contractPaymentTermServiceNew.deleteCreditCardToken(contract.getActiveContractPaymentTerm());
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('contract', 'addNewFlagIsWorkerSalaryVattedOnContracts')")
    @PostMapping("/addNewFlagIsWorkerSalaryVattedOnContracts")
    public ResponseEntity<?> addNewFlagIsWorkerSalaryVattedOnContracts(@RequestBody List<Long> contractIds) {

        int page = 0;
        Page<Contract> contractPage;
        do {
            contractPage = contractRepository.findByIdIn(contractIds, PageRequest.of(page, 100));

            contractPage.getContent()
                    .forEach(contract -> {
                        if (!AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE
                                .equals(contract.getContractProspectType().getCode()) ||
                                contract.isWorkerSalaryVatted()) {
                            return;
                        }

                logger.info("contract Id: " + contract.getId());
                contract.addBaseAdditionalInfo(new BaseAdditionalInfo(
                        "withVatOnSalary",
                        "true",
                        contract.getEntityType(),
                        contract.getId()));
                contractRepository.silentSave(contract);
            });

            page++;
        } while (contractPage.hasNext());

        return okResponse();
    }

    @JwtSecured
    @PreAuthorize("hasPermission('contract', 'confirmUpdateSalaryAndPaymentForMaid')")
    @GetMapping(value = "/confirmUpdateSalaryAndPaymentForMaid/{id}")
    public ResponseEntity<?> confirmUpdateSalaryAndPaymentForMaid(@PathVariable("id") Contract contract) {
        if (!contract.isMaidVisa()) return badRequestResponse();

     contractService.confirmUpdateSalaryAndPaymentForMaid(contract);
     return okResponse();
    }

    @JwtSecured
    @PreAuthorize("hasPermission('contract', 'flagOldCcContractsAsRecurring')")
    @GetMapping(value = "/flagOldCcContractsAsRecurring")
    public ResponseEntity<?> flagOldCcContractsAsRecurring() {
        contractService.flagOldActiveCcAndPayingViaCcContractsAsRecurring();
        return okResponse();
    }

    @PreAuthorize("hasPermission('contract', 'updateExtensionFlowEnabled')")
    @GetMapping("/updateExtensionFlowEnabled/{contractId}")
    public ResponseEntity<?> updateExtensionFlowEnabled(
            @PathVariable("contractId") Contract contract,
            @RequestParam boolean enabled) {

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("isExtensionFlowEnabled", enabled);
        }};

        boolean isDisabled = ContractService.isExtensionFlowDisabled(contract);
        if ((enabled && !isDisabled) || (!enabled && isDisabled)) {
            return ResponseEntity.ok(m);
        }

        if (enabled) {
            contract.removeBaseAdditionalInfo(Contract.ADDITIONAL_INFO_EXTENSION_FLOW_DISABLED);
        } else {
            contract.addBaseAdditionalInfo(new BaseAdditionalInfo(
                    Contract.ADDITIONAL_INFO_EXTENSION_FLOW_DISABLED,
                    "true",
                    contract.getEntityType(),
                    contract.getId()));
            contractRepository.silentSave(contract);
        }

        return ResponseEntity.ok(m);
    }

    @PreAuthorize("hasPermission('contract', 'addAdditionalInfoForExistingIpamFlows')")
    @GetMapping("/addAdditionalInfoForExistingIpamFlows")
    public ResponseEntity<?> addAdditionalInfoForExistingIpamFlows() {

        flowProcessorEntityRepository.findByFlowEventConfig_NameAndStoppedFalseAndCompletedFalse(
                FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED)
                .forEach(flowProcessorEntity -> {
                    contractService.addAdditionalInfoForIpamFlow(flowProcessorEntity.getContract());
                });

        return okResponse();
    }

    @JwtSecured
    @PreAuthorize("hasPermission('contract','getDetailedPaymentInfo')")
    @GetMapping("/getDetailedPaymentInfo/{id}")
    public ResponseEntity<?> getDetailedPaymentInfo(@PathVariable("id") Contract contract) {
        if (contract == null) {
            throw new RuntimeException("Contract not found");
        }

        if(!contract.isMaidVisa()) {
            throw new BusinessException("This operation is only allowed for Maid Visa contracts");
        }

        return ResponseEntity.ok(contractService.getDetailedPaymentInfo(contract));
    }

    @PreAuthorize("hasPermission('collectionFlowLog','getMergedPaymentAndCollectionInfo')")
    @GetMapping("/getMergedPaymentAndCollectionInfo/{id}")
    public ResponseEntity<?> getMergedPaymentAndCollectionInfo(
            @PathVariable("id") Contract contract,
            @RequestParam(required = false) String requiredKeys) {
        if (contract == null) {
            throw new RuntimeException("Contract not found");
        }

        return ResponseEntity.ok(Setup.getApplicationContext()
                .getBean(ContractService.class)
                .getMergedPaymentAndCollectionFlowInfo(contract, requiredKeys, contract.isMaidVisa()));
    }


    @GetMapping("/payment-receipt-terms-forTest/{id}")
    public ResponseEntity<?> getPaymentReceiptTerms( @PathVariable("id") Contract contract) {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        if (cpt == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("No active payment term found for this contract");
        }

        return ResponseEntity.ok(PaymentReceiptHelper.getPaymentsReceiptTermForms(cpt));
    }
}