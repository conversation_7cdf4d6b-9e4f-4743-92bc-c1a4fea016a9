package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.repository.WordTemplateRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.DDClientProjection;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.extra.DTOs.DirectDebitDTO;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PdfHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Abbas
 *
 */
@RestController
@RequestMapping("/directDebit")
public class DirectDebitController extends BaseRepositoryController<DirectDebit> {

    public static final String SHOW_INCOMPLETE_DD_POSITION_CODE = "show_incomplete_dd";

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private SwitchingBankAccountService switchingBankAccountService;

    @Autowired
    private DirectDebitFileController directDebitFileController;

    @Autowired
    private ContractPaymentRepository contractPaymentRep;

    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;

    @Autowired
    private AttachementRepository attachementRepository;

    @Autowired
    private ClientDocumentRepository clientDocumentRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private ContractController contractController;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private Utils utils;

    @Autowired
    private InterModuleConnector moduleConnector;

    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;

    @Autowired
    private OecAmendDDsService oecAmendDDsService;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    @Autowired
    private DirectDebitSignatureRepository directDebitSignatureRepository;

    @Autowired
    private DirectDebitService directDebitService;

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private IncompleteDirectDebitService incompleteDirectDebitService;

    @Autowired
    private AppsServiceDDApprovalTodoRepository appsServiceDDApprovalTodoRepository;

    @Override
    public BaseRepository<DirectDebit> getRepository() {
        return directDebitRepository;
    }


    // ACC-637
    @PreAuthorize("hasPermission('directDebit','getalldirectdebitstatus')")
    @RequestMapping(value = "/getalldirectdebitstatus")
    public ResponseEntity<?> getDirectDebitStatus(
            @RequestParam(name = "search", required = false) String search) {

        List<SearchableEnumProjection> result =
                Arrays.asList(DirectDebitStatus.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x))
                        .filter(x -> search == null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    @Transactional
    public List<DirectDebit> generateDD(
            List<ContractPayment> payments, List<Attachment> signatures,
            ContractPaymentTerm contractPaymentTerm,
            Boolean useOldSignatures, Boolean ignoreDDSignature, boolean savePayments,
            boolean jusApprovedSigns, boolean withIncomplete, boolean ignoreRejectionDDs,
            Boolean forceDataEntry) {

        return generateDD(payments, signatures, contractPaymentTerm,
                useOldSignatures, ignoreDDSignature, savePayments,
                jusApprovedSigns, withIncomplete, ignoreRejectionDDs,
                forceDataEntry, new HashMap<>());
    }

    // ACC-1435 from here
    @Transactional
    public List<DirectDebit> generateDD(
            List<ContractPayment> payments, List<Attachment> signatures,
            ContractPaymentTerm contractPaymentTerm,
            Boolean useOldSignatures, Boolean ignoreDDSignature, boolean savePayments,
            boolean jusApprovedSigns, boolean withIncomplete, boolean ignoreRejectionDDs,
            Boolean forceDataEntry, Map<String, Object> m) {

        logger.info("useOldSignatures: " + useOldSignatures);
        logger.info("ignoreDDSignature: " + ignoreDDSignature);

        List<DirectDebitSignature> currentSignatures = null;

        if (useOldSignatures) {
            Map<String, Object> signatureType = directDebitSignatureService
                    .getLastSignatureType(contractPaymentTerm, true, jusApprovedSigns);

            currentSignatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");
            ignoreDDSignature = false;
        }

        List<DirectDebitSignature> newSignatures = null;
        boolean isNewSignatures = false;
        if (signatures != null && !signatures.isEmpty()) {
            newSignatures = directDebitSignatureService
                    .saveNewDirectDebitFileSignatures(signatures, contractPaymentTerm);
            isNewSignatures = newSignatures != null && newSignatures.stream().anyMatch(DirectDebitSignature::isNewSignature);
        }

        if (!useOldSignatures || (currentSignatures == null && newSignatures != null)) {
            currentSignatures = newSignatures;
            ignoreDDSignature = newSignatures == null || newSignatures.isEmpty();
        }

        if (useOldSignatures && currentSignatures == null)
            throw new RuntimeException("There are no previous approved/pending signatures");

        boolean isCompletedBankInfo = Setup.getApplicationContext().getBean(DirectDebitService.class)
                .isRequiredBankInfoExist(contractPaymentTerm);
        boolean bankInfoAndSignaturesAvailable = !ignoreDDSignature && isCompletedBankInfo;
        logger.info("isCompletedBankInfo: " + isCompletedBankInfo + "; ignoreDDSignature: " + ignoreDDSignature);

        if (bankInfoAndSignaturesAvailable) {
            FlowProcessorEntity ipamFlow = Setup.getApplicationContext()
                    .getBean(FlowProcessorService.class)
                    .getFirstRunningFlow(contractPaymentTerm.getContract(), FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
            if (ipamFlow != null) {
                logger.info( "Stop IPAM Flow id: " + ipamFlow.getId());
                ipamFlow.setCompleted(true);
                Setup.getRepository(FlowProcessorEntityRepository.class).saveAndFlush(ipamFlow);
            }
        }

        List<DirectDebit> directDebits = directDebitService.getDirectDebitsOfPayments(
                payments, contractPaymentTerm, savePayments, m);

        List<Long> ids = directDebits.stream()
                .filter(dd -> dd.getId() != null)
                .map(d -> d.getId())
                .collect(Collectors.toList());
        logger.log(Level.INFO, "ids size: " + ids.size());
        directDebits.forEach(dd -> {
            logger.log(Level.INFO, "ID: " + dd.getId() + "; Status: " + dd.getStatus().getLabel());
        });

        if (ids.size() == 0) ids.add(0, (long) 0);

        if (withIncomplete) {
            directDebits.addAll(directDebitRepository.findByStatusAndIdNotInAndContractPaymentTerm(
                    ids, contractPaymentTerm));
        }

        //  ACC-2745
        if (ignoreRejectionDDs) {
            directDebits = directDebits.stream()
                    .filter(dd -> dd.getDirectDebitRejectionToDo() == null &&
                            dd.getDirectDebitBouncingRejectionToDo() == null)
                    .collect(Collectors.toList());
        }

        logger.log(Level.INFO, "findByStatusAndIdNotInAndContractPaymentTerm");
        long ddBankInfoGroup = new DateTime().getMillis();
        Contract contract = contractRepository.findOne(contractPaymentTerm.getContract().getId());

        // ACC-1588, its related to createDirectDebitFileFromSignatures after a few statements
        if (bankInfoAndSignaturesAvailable) {
            if (currentSignatures == null) throw new RuntimeException("Signatures not found");

            PicklistItem reasonOfTermination = contract.getReasonOfTerminationList();
            logger.info("Contract termination reason: " + (reasonOfTermination != null ?
                    "ID " + reasonOfTermination.getId() + "; CODE " + reasonOfTermination.getCode() :
                    "NULL"));

            // ACC-1915
            if (contract != null && contract.getIsScheduledForTermination()
                    && contract.getScheduledDateOfTermination() != null && contract.getScheduledDateOfTermination().after(new DateTime().minusDays(1).toDate())
                    && reasonOfTermination != null && ContractScheduleForTerminationUtils.isTerminationReasonDueDDRejectionOrInCompleteFlows(reasonOfTermination.getCode())
                    && contract.getStatus().equals(ContractStatus.ACTIVE) &&
                    directDebits.stream().anyMatch(dd ->
                            dd.getDirectDebitFiles() == null || dd.getDirectDebitFiles().isEmpty() ||
                                    dd.getDirectDebitFiles().stream().anyMatch(ddf -> {
                                            HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery(DirectDebitFile.class);
                                            historyQuery.filterBy("id", "=", ddf.getId());
                                            historyQuery.sortBy("lastModificationDate", false, true);
                                            historyQuery.setLimit(1);

                                            DirectDebitFile old = historyQuery.execute().get(0);

                                            return ddf.getSignatureAttachment() == null || old.getEid() == null || old.getEid().isEmpty() ||
                                                    old.getIbanNumber() == null || old.getIbanNumber().isEmpty() ||
                                                    old.getAccountName() == null || old.getAccountName().isEmpty();
                                        }))) {
                Setup.getApplicationContext().getBean(ContractService.class)
                        .retractContractTermination(contract);
            }

            directDebits.forEach(dd -> {
                if ((dd.getId() == null || dd.getNonCompletedInfo()) &&
                        dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) {

                    logger.log(Level.INFO, "incomplete DD ID: " + dd.getId());
                    if (dd.getDirectDebitFiles() != null && !dd.getDirectDebitFiles().isEmpty() &&
                            !dd.getDirectDebitFiles().stream()
                                    .anyMatch(ddf -> ddf.getForBouncingPayment())) { // ACC-1721

                        logger.log(Level.INFO, "incomplete DD ddfs size: " + dd.getDirectDebitFiles().size());
                        dd.getDirectDebitFiles().forEach(ddf -> {
                            directDebitFileRepository.delete(ddf);
                        });

                        dd.getDirectDebitFiles().clear();
                    }
                }
            });
        }

        int ddmIndex = 0;
        boolean newSignature5156 = false;
        boolean acc5798Done = false;
        boolean contractHasOpenMainDdcToDo = directDebitService.contractHasOpenMainDdcToDo(contractPaymentTerm.getContract().getId());
        String dateOfParameter = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

        for (DirectDebit directDebit : directDebits) {

            // ACC-7321
            // If creation date of contract is after date of parameter (Parameter: acc_6003_reference_date) -> apply the feature (Direct Debit as hidden)
            if (contract.getCreationDate().getTime() >= new DateTime(dateOfParameter).toDate().getTime() &&
                    (contractHasOpenMainDdcToDo || contractPaymentTerm.getDdcId() == null ||
                            (directDebit.getDdcId() == null && !directDebit.isHidden() &&
                                    !directDebitService.checkActiveCPTAndIfPaymentMatchedWithPTC(directDebit)))) {
                directDebit.setHidden(true);
            } else if (!directDebit.isHidden() && directDebit.getDdcId() == null) {
                directDebit.setDdcId(m.containsKey("ddcId") && m.get("ddcId") != null ? (Long) m.get("ddcId") : contractPaymentTerm.getDdcId());
                if (directDebit.getDdcId() != null && directDebit.getDataEntryNotes() == null) {
                    logger.info("DD Id: " + (directDebit.getId() == null ? "NULL" : directDebit.getId()) + "; data entry note updated form DDC");
                    AppsServiceDDApprovalTodo ddcTodo = appsServiceDDApprovalTodoRepository.findOne(directDebit.getDdcId());
                    directDebit.setDataEntryNotes(ddcTodo.getDdDataEntryNote());
                }
            }

            // ACC-2024
            if (bankInfoAndSignaturesAvailable && !directDebit.getRejectedBySalesScreen()) {
                directDebit.setInCompleteDDReminder(0);
                directDebit.setInCompleteDDTrials(directDebit.getInCompleteDDTrials() + 1);
            }

            directDebit.setRejectedBySalesScreen(false);

            if ((directDebit.getId() != null && !directDebit.getNonCompletedInfo()) ||
                    !directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE) ||
                    (directDebit.getDirectDebitFiles() != null && !directDebit.getDirectDebitFiles().isEmpty() &&
                            directDebit.getDirectDebitFiles().stream().allMatch(ddf ->
                                    ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment() &&
                                            ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                                            Arrays.asList(DirectDebitType.WEEKLY, DirectDebitType.ONE_TIME, DirectDebitType.DAILY).contains(ddf.getDdFrequency())))) {

                // ACC-1721
                if (directDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE) ||
                        (directDebit.getMStatus().equals(DirectDebitStatus.NOT_APPLICABLE) && directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE))) {
                    directDebit.setNonCompletedInfo(!bankInfoAndSignaturesAvailable);

                    DirectDebit tempDD = new DirectDebit();
                    tempDD.setContractPaymentTerm(contractPaymentTerm);

                    //copy bank info from contract payment term to direct debit
                    directDebitService.copyBankInfo(contractPaymentTerm, tempDD, ddBankInfoGroup, forceDataEntry);

                    directDebit.setMStatus(!bankInfoAndSignaturesAvailable ?
                            DirectDebitStatus.IN_COMPLETE :
                            (tempDD.getConfirmedBankInfo() ?
                                    DirectDebitStatus.PENDING :
                                    DirectDebitStatus.PENDING_DATA_ENTRY));

                    if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE) &&
                            directDebit.getDirectDebitFiles() != null &&
                            directDebit.getDirectDebitFiles().stream()
                                    .anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC))) {

                        directDebit.setStatus(!bankInfoAndSignaturesAvailable ?
                                DirectDebitStatus.IN_COMPLETE :
                                (tempDD.getConfirmedBankInfo() ?
                                        DirectDebitStatus.PENDING :
                                        DirectDebitStatus.PENDING_DATA_ENTRY));
                    }

                    Boolean ddfForBouncingPayment = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(ddf -> ddf.getForBouncingPayment());

                    int ddfIndex = 0;
                    if (directDebit.getDirectDebitFiles() != null) {
                        for (DirectDebitFile ddf : directDebit.getDirectDebitFiles()
                                .stream().filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.IN_COMPLETE))
                                .collect(Collectors.toList())) {

                            ddf.copyDDBankInfo(tempDD, true);
                            ddf.setStatus(DirectDebitFileStatus.NOT_SENT);

                            if (currentSignatures != null) {
                                DirectDebitConfiguration ddConfiguration = directDebit.getDdConfiguration();
                                Integer numOfDDs = Math.min(ddConfiguration.getNumberOfGeneratedDDs(), currentSignatures.size());
                                ddf.setDirectDebitSignature(directDebitSignatureService.selectSignature(currentSignatures, numOfDDs, ddmIndex, ddfIndex));

                                logger.info("newMonthlyDdSignedDone: " + newSignature5156);
                                if (!newSignature5156) { // ACC-5156
                                    newSignature5156 = directDebitSignatureService.newMonthlyDdSigned(
                                            contractPaymentTerm, directDebit, currentSignatures);
                                }
                            }

                            ddfIndex++;
                            directDebitFileRepository.save(ddf);

                            directDebitService.createDirectDebitActivationAttachmentIfNotExist(ddf);
                        }
                    }

                    PaymentService paymentService = Setup.getApplicationContext()
                            .getBean(PaymentService.class);
                    if (ddfForBouncingPayment) {
                        SelectQuery<Payment> ddBouncedPaymentQuery = new SelectQuery(Payment.class);
                        ddBouncedPaymentQuery.filterBy("directDebitId", "=", directDebit.getId());
                        ddBouncedPaymentQuery.filterBy("status", "=", PaymentStatus.BOUNCED);
                        ddBouncedPaymentQuery.filterBy("replaced", "=", false);
                        List<Payment> ddBouncedPayments = ddBouncedPaymentQuery.execute();
                        ddBouncedPayments.forEach(payment -> {
                            payment.setReminder(0);
                            paymentService.forceUpdatePayment(payment);
                        });
                    }
                }

                directDebitRepository.save(directDebit);

                ddmIndex++;
                continue;
            }

            // ACC-5798
            if (!acc5798Done && directDebit.getDdBankInfoGroup() == null) {
                acc5798Done = directDebitService.closeMainDDCTodo(contract);
            }

            logger.log(Level.INFO, "bankInfoAndSignaturesAvailable: " + bankInfoAndSignaturesAvailable);

            // ACC-1588
            directDebit.setNonCompletedInfo(!bankInfoAndSignaturesAvailable);
            directDebit.setStatus(!bankInfoAndSignaturesAvailable ? DirectDebitStatus.IN_COMPLETE : DirectDebitStatus.PENDING_DATA_ENTRY);
            directDebit.setMStatus(!bankInfoAndSignaturesAvailable ? DirectDebitStatus.IN_COMPLETE : DirectDebitStatus.PENDING_DATA_ENTRY);

            logger.log(Level.INFO, "directDebit status: " + directDebit.getStatus());

            //copy bank info from contract payment term to direct debit
            directDebitService.copyBankInfo(contractPaymentTerm, directDebit, ddBankInfoGroup, forceDataEntry);
            directDebitRepository.save(directDebit);

            logger.log(Level.INFO, "directDebit status: " + directDebit.getStatus());
            
            if (!ignoreDDSignature) {
                if(!newSignature5156) { // ACC-5156
                    newSignature5156 = directDebitSignatureService.newMonthlyDdSigned(
                        contractPaymentTerm, directDebit, currentSignatures);
                }

                directDebitService.deleteNonCompleteFiles(directDebit);

                if(directDebit.getId() != null) directDebit = directDebitRepository.findOne(directDebit.getId());

                directDebitService.createDirectDebitFileFromSignatures(directDebit, currentSignatures, isCompletedBankInfo, ddmIndex);
                directDebitRepository.save(directDebit);

                logger.log(Level.INFO, "directDebit status: " + directDebit.getStatus());
            }
            ddmIndex++;
        }
        
        logger.log(Level.INFO, "isCompletedBankInfo" + isCompletedBankInfo);
        // ACC-3854 // ACC-6251 #2 // ACC-6344 // ACC-6804
        // Whenever a DD is generated without bank info trigger the incomplete flow
        if (!(boolean) m.getOrDefault("ignoreMissingBakKInfoFlow", false) &&
                !directDebits.isEmpty() && !bankInfoAndSignaturesAvailable &&
                directDebits.stream().anyMatch(d ->
                        d.getDirectDebitRejectionToDo() == null &&
                                d.getDirectDebitBouncingRejectionToDo() == null && !d.isHidden())) {

            logger.info("there are incomplete dds -> start incomplete flow");
            incompleteDirectDebitService.startIncompleteFlowMissingBankInfo(contractPaymentTerm);
        } else if (bankInfoAndSignaturesAvailable) {
            incompleteDirectDebitService.stopMissingBankInfoFlow(contract, true);
        }

        if (isCompletedBankInfo) {
            logger.log(Level.INFO, "getNotCompletedDirectDebits");
            List<DirectDebit> notCompletedDd = directDebitRepository.getNotCompletedDirectDebits(contractPaymentTerm);
            logger.log(Level.INFO, "notCompletedDd size: " + notCompletedDd.size());

            ddmIndex = 0;
            for (DirectDebit dd : notCompletedDd) {
                logger.log(Level.INFO, "dd ID: " + dd.getId() + "; directDebit status: " + dd.getStatus());

                directDebitService.copyBankInfo(contractPaymentTerm, dd, ddBankInfoGroup, forceDataEntry);

                if (currentSignatures != null) {
                    logger.log(Level.INFO, "notCompletedDd DD ID: " + dd.getId());

                    directDebitService.deleteNonCompleteFiles(dd);

                    dd = directDebitRepository.findOne(dd.getId());
                    directDebitService.createDirectDebitFileFromSignatures(
                            dd, currentSignatures, true, ddmIndex);
                } else {
                    DirectDebit ddTemp = dd;

                    if (dd.getDirectDebitFiles() != null) {
                        dd.getDirectDebitFiles().forEach(ddf -> {
                            ddf.copyDDBankInfo(ddTemp, true); // ACC-1588

                            if (ddf.getStatus().equals(DirectDebitFileStatus.NOT_COMPLETED)) {
                                ddf.setStatus(DirectDebitFileStatus.NOT_SENT);
                                directDebitFileRepository.save(ddf);
                            }
                        });
                    }
                }

                ddmIndex++;
                directDebitRepository.save(dd);

                logger.log(Level.INFO, "dd ID: " + dd.getId() + "; directDebit status: " + dd.getStatus());
            }
        }

        return directDebits;
    }

    @PreAuthorize("hasPermission('directDebit','confirmDdBankInfo')")
    @Transactional
    @RequestMapping(value = "/confirmddbankinfo/{directDebitFileId}", method = RequestMethod.GET)
    public ResponseEntity<?> confirmDdBankInfo(
            @PathVariable("directDebitFileId") DirectDebitFile directDebitFile,
            @RequestParam("iban") String iban,
            @RequestParam("bankName") String bankName,
            @RequestParam("bank") PicklistItem bank,
            @RequestParam("eid") String eid,
            @RequestParam("accountName") String accountName,
            @RequestParam(value = "clientName", required = false) String clientName,
            @RequestParam(value = "clientTitleId", required = false) PicklistItem clientTitle,
            @RequestParam(value = "nationalityId", required = false) PicklistItem nationality) {

        //check if there is a matching client who is fired or blocked
        Map<String, String> validateClientResult = directDebitFileController.ddDataEntryValidateClient(
                directDebitFile, eid, iban);
        if (validateClientResult != null && validateClientResult.containsKey("status") &&
                !validateClientResult.getOrDefault("status", "active")
                        .equalsIgnoreCase("active")){

            return ResponseEntity.ok(validateClientResult);
        }

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING) ||
                directDebitFile.getConfirmedBankInfo())
            throw new RuntimeException("This direct debit was confirmed before");

        if (!directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
            throw new RuntimeException("Invalid DD Status: " + directDebitFile.getDdStatus().getLabel() + ", please try to refresh DD-Data-Entry Page");


        DirectDebit ddfDD = directDebitFile.getDirectDebit();
        ContractPaymentTerm cpt = ddfDD.getContractPaymentTerm();

        List<DirectDebit> dds = ddfDD.getDdBankInfoGroup() == null ? Collections.singletonList(ddfDD) :
                directDebitRepository.getByContractPaymentTermAnd_StatusOrMStatus_AndDdBankInfoGroup(
                        cpt, DirectDebitStatus.PENDING_DATA_ENTRY, ddfDD.getDdBankInfoGroup());

        logger.log(Level.INFO, "Data entry confirmation for CPT: " + ddfDD.getContractPaymentTerm().getId());
                directDebitRepository.getByContractPaymentTermAnd_StatusOrMStatus_AndDdBankInfoGroup(cpt, DirectDebitStatus.PENDING_DATA_ENTRY,
                        ddfDD.getDdBankInfoGroup());

        logger.log(Level.INFO, "eid" + eid);

        List<DirectDebitSignature> directDebitSignatures =
                (List<DirectDebitSignature>) directDebitSignatureService.getLastSignatureTypeByEid(
                        cpt, true, false, eid).get("currentSignatures");

        // ACC-6604
        if (directDebitSignatures == null || directDebitSignatures.isEmpty())
            throw new BusinessException("Error: No signature found to be used");

        //ACC-3989
        if (directDebitSignatures != null) {
            directDebitSignatures.forEach(sign -> {
                if (sign.getEid() == null) {
                    sign.setEid(eid);
                    logger.log(Level.INFO, "directDebitSignature id " + sign.getId());
                    directDebitSignatureRepository.save(sign);
                }
            });
        }
        //ACC-4657
        directDebitFile.getAttachments().stream()
                .filter(att -> DirectDebitService.BANK_INFO_TAGS.contains(att.getTag()))
                .forEach(att -> {
                    att.setUniqueTag(true);
                    attachementRepository.saveAndFlush(att);
                });

        DirectDebitConfigurationRepository directDebitConfigurationRepository =
                Setup.getRepository(DirectDebitConfigurationRepository.class);
        DirectDebitConfiguration oldConfig = cpt.getBank() == null ?
                DirectDebitConfiguration.newInstance(null) :
                directDebitConfigurationRepository.findFirstByBank(cpt.getBank());

        DirectDebitConfiguration newConfig = directDebitConfigurationRepository.findFirstByBank(bank);
        // ACC-6278
        logger.info("old config: " + (oldConfig == null ? "NULL" : oldConfig.getId()) + "; new config: " + (newConfig.getId()));

        List<DirectDebit> switchingBankAccountDDs = new ArrayList<>();
        for (int i =0; i < dds.size(); i++) {
            DirectDebit dd = dds.get(i);
            logger.info("dd id: " + dd.getId());
            switchingBankAccountDDs.add(dd);

            // set bank info
            directDebitService.setDirectDebitBankInfo(dd, iban, bankName, bank, eid, accountName,
                    dd.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) ? DirectDebitStatus.PENDING : null,
                    dd.getMStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) ? DirectDebitStatus.PENDING : null,
                    directDebitFile);

            boolean validateRecreate = !dd.getDirectDebitFiles()
                    .stream()
                    .anyMatch(ddf -> !ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY));

            // ACC-6189
            if (validateRecreate && directDebitService.validateRecreateFiles(
                    dd, oldConfig, newConfig, directDebitSignatures, eid, i)) continue;

            directDebitRepository.save(dd);

            dd.getDirectDebitFiles()
                    .stream()
                    .filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                    .forEach(ddf -> { // ACC-1588
                        ddf.copyDDBankInfo(dd, false);
                        ddf.setDdaExpiryDate(dd);
                        ddf.setDdDataEntryNotificationSent(false);
                        directDebitFileRepository.save(ddf);
                    });
        }

        // Switching Bank Account flow
        processSwitchingBankAccountDDsAfterConfirmation(switchingBankAccountDDs);

        updateCPTInfoAfterDDConfirmation(cpt, ddfDD, accountName, iban, eid, bankName, bank);
        updateClientInfoAfterDDConfirmation(ddfDD, clientName, eid, clientTitle, nationality);

        //ACC-3989 activation file generated inside
        directDebitSignatureService.redistributionOfSignatures(dds, directDebitSignatures);

        return okResponse();
    }

    private void updateCPTInfoAfterDDConfirmation(
            ContractPaymentTerm cpt,
            DirectDebit dd,
            String accountName, String iban, String eid,
            String bankName, PicklistItem bank) {

        // update CPT bank info by dd
        cpt.setAccountName(accountName);
        cpt.setIbanNumber(iban);
        cpt.setEid(eid);
        cpt.setBankName(bankName);
        cpt.setBank(bank);

        //ACC-4657
        cpt.getAttachments().stream()
            .filter(att -> att.getTag().equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR))
            .forEach(att -> attachementRepository.delete(att));

        DirectDebitService.BANK_INFO_TAGS.forEach(tag -> {
            Attachment cptAtt = cpt.getAttachment(tag);
            Attachment directDebitFileAtt = dd.getAttachment(tag);
            if (directDebitFileAtt != null) {
                if (cptAtt != null)
                    attachementRepository.delete(cptAtt);
                cpt.addAttachment(directDebitFileAtt);
            }
        });
        contractPaymentTermRepository.save(cpt);
    }

    // ACC-1579
    private void updateClientInfoAfterDDConfirmation(
            DirectDebit dd,
            String clientName,
            String eid,
            PicklistItem clientTitle,
            PicklistItem nationality) {

        Client client = clientRepository.findOne(dd.getContractPaymentTerm().getContract().getClient().getId());
        dd = directDebitRepository.findOne(dd.getId());

        // ACC-3388
        if (!paymentRepository.existsByMethodOfPaymentAndOnlineAndContract_ClientAndStatusNotIn(
                PaymentMethod.CARD, true, client,
                Arrays.asList(PaymentStatus.DELETED, PaymentStatus.CANCELLED))) {

            client.setName(clientName);
        }

        // ACC-2779 ACC-5055
        if (directDebitService.isFirstDataEntryConfirmed(dd)) {
            client.setEid(eid != null && !eid.isEmpty() ? eid : null);
        }

        client.setTitle(clientTitle);
        client.setNationality(nationality);
        clientRepository.save(client);

        // ACC-1579
        Attachment att = dd.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
        if (att == null) return;

        ClientDocument cDoc = new ClientDocument();
        cDoc.setAttachments(Arrays.asList(att));
        cDoc.setName("EMIRATES_ID_FRONT_SIDE");
        cDoc.setClient(client);
        cDoc.setUploadDate(new Date(System.currentTimeMillis()));
        cDoc.setType(Setup.getItem("ClientDocumentType", "EMIRATES_ID_FRONT_SIDE"));
        clientDocumentRepository.save(cDoc);
    }

    // ACC-2428
    private void processSwitchingBankAccountDDsAfterConfirmation(List<DirectDebit> directDebits) {
        List<Payment> bouncedPaymentsToSwap = new ArrayList();
        for (DirectDebit oldDD : directDebits.stream().filter(dd -> dd.getImageForDD() != null).map(dd -> dd.getImageForDD()).collect(Collectors.toList())) {
            logger.log(Level.INFO, "Checking Old DD:" + oldDD.getId());
            List<Payment> ddBouncedPayments = paymentRepository.findByDirectDebitIdAndStatusAndReplaced(oldDD.getId(), PaymentStatus.BOUNCED, false);
            logger.log(Level.INFO, "ddBouncedPayments Size:" + ddBouncedPayments.size());
            if (ddBouncedPayments != null && !ddBouncedPayments.isEmpty() &&
                    oldDD.getManualDdfFile() == null &&
                    oldDD.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())) {
                logger.log(Level.INFO, "Checked" + oldDD.getId());
                bouncedPaymentsToSwap.addAll(ddBouncedPayments);
            }
        }
        for (Payment payment : bouncedPaymentsToSwap) {
            Map requestBody = new HashMap();
            requestBody.put("bouncedPaymentId", payment.getId());
            moduleConnector.postJsonAsync("accounting/contractpaymentterm/switchingBankAccount/swapBouncedPayment", requestBody);
        }
    }

    // ACC-3136
    @PreAuthorize("hasPermission('directDebit','updateDataEntryNotes')")
    @Transactional
    @PostMapping(value = "/updateDataEntryNotes/{directDebitId}")
    public ResponseEntity updateDataEntryNotes(
            @PathVariable("directDebitId") DirectDebit directDebit,
            @RequestBody String newNotes) {

        List<DirectDebit> ddsToBeUpdated = new ArrayList();
        ddsToBeUpdated.add(directDebit);

        if (directDebit.getDdBankInfoGroup() != null) {
            List<DirectDebit> dDsWithSameBankInfoGroup = directDebitRepository.getByContractPaymentTermAndStatusAndDdBankInfoGroup(
                    directDebit.getContractPaymentTerm(),
                    DirectDebitStatus.PENDING_DATA_ENTRY, directDebit.getDdBankInfoGroup());

            List<DirectDebit> otherDDsWithSameBankInfoGroup = dDsWithSameBankInfoGroup
                    .stream()
                    .filter(dd -> !dd.getId().equals(directDebit.getId()))
                    .collect(Collectors.toList());

            ddsToBeUpdated.addAll(otherDDsWithSameBankInfoGroup);
        }

        for (DirectDebit dd : ddsToBeUpdated) {
            dd.setDataEntryNotes(newNotes);
        }

        getRepository().save(ddsToBeUpdated);
        return okResponse();
    }


    @PreAuthorize("hasPermission('directDebit','removeDdBankPhoto')")
    @Transactional
    @GetMapping(value = "/removeddbankphoto/{directDebitFileId}")
    public ResponseEntity<?> removeDdBankPhoto(
            @PathVariable("directDebitFileId") DirectDebitFile directDebitFile,
            @RequestParam("photoTags") List<String> tags,
            @RequestParam(value = "ibanRejectionReasonId", required = false) PicklistItem ibanRejectionReason,
            @RequestParam(value = "eidRejectionReasonId", required = false) PicklistItem eidRejectionReason,
            @RequestParam(value = "accountNameRejectionReasonId", required = false) PicklistItem accountNameRejectionReason,
            @RequestParam(value = "rejectedBySalesScreen", required = false, defaultValue = "false") boolean rejectedBySalesScreen) {

        if (tags == null) throw new RuntimeException("You should choose at Least one Photo");

        if (tags == null) throw new RuntimeException("You should choose at Least one Photo");

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.IN_COMPLETE))
            throw new RuntimeException("This direct debit is not completed yet");

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING) ||
                directDebitFile.getConfirmedBankInfo())
            throw new RuntimeException("This direct debit was confirmed before");

        directDebitService.removeDdBankPhoto(directDebitFile, tags,
                ibanRejectionReason, eidRejectionReason,accountNameRejectionReason,
                rejectedBySalesScreen);

        return okResponse();
    }

    public Boolean isBankInfoConfirmedBefore(DirectDebit directDebit) {
        return !directDebit.getNonCompletedInfo() && isBankInfoConfirmedBefore(directDebit.getContractPaymentTerm().getContract().getClient(),
                directDebit.getIbanNumber(), directDebit.getEid(), directDebit.getAccountName());
    }

    public Boolean isBankInfoConfirmedBefore(Client client, String iban, String eid, String accountName) {
        return iban != null && !iban.isEmpty()
                && eid != null && !eid.isEmpty()
                && accountName != null && !accountName.isEmpty()
                && directDebitRepository.existsByIbanNumberAndEidAndAccountNameAndConfirmedBankInfoAndContractPaymentTerm_Contract_Client(
                iban, eid, accountName, true, client);
    }

    @PreAuthorize("hasPermission('directDebit','regenerateDDActivationAttachments')")
    @PostMapping("/regenerateDDActivationAttachments")
    public ResponseEntity<?> regenerateDDActivationAttachments() throws IOException {
        
        LocalDateTime startDate = new LocalDateTime(2022, 5, 20, 8, 0, 0);
        LocalDateTime endDate = new LocalDateTime(2022, 5, 21, 15, 10, 0);
        DirectDebitCancelationToDoController cancellationController = 
                Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        
        SelectQuery<Attachment> attachmentQuery = new SelectQuery(Attachment.class);
        attachmentQuery.filterBy("tag", "IN", Arrays.asList("direct_debit_activation", "DD_CANCELLATION_FORM"));
        attachmentQuery.filterBy("ownerType", "=", "DirectDebitFile");
        attachmentQuery.filterBy("creationDate", ">=", startDate.toDate());
        attachmentQuery.filterBy("creationDate", "<=", endDate.toDate());
        
        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        query.filterBy("id", "in", attachmentQuery.execute().stream().map(Attachment::getOwnerId).collect(Collectors.toSet()));
        query.filterBy("lastDataCorrectionDate", "is null", null);
        query.setLimit(20);
        
        List<DirectDebitFile> p = query.execute();
        
        InputStream activationStream;
        InputStream cancellationStream;
        Boolean resume = true;
        
        while(resume) {
            for(DirectDebitFile f : p) {
                Attachment activation = f.getAttachment("direct_debit_activation");
                Attachment cancellation = f.getAttachment("DD_CANCELLATION_FORM");
                
                if(activation != null && activation.getCreationDate().getTime() >= startDate.toDate().getTime()
                        && activation.getCreationDate().getTime() <= endDate.toDate().getTime()) {
                    
                    activationStream = directDebitService.generateDDForm(f, null, true);
                    try {
                        Attachment activationFile = Storage.storeTemporary(
                                "Direct Debit Activation.pdf", activationStream, "direct_debit_activation", true);
                        f.addAttachment(activationFile);
                    } finally {
                        StreamsUtil.closeStream(activationStream);
                    }
                }
                
                if(cancellation != null && cancellation.getCreationDate().getTime() >= startDate.toDate().getTime()
                        && cancellation.getCreationDate().getTime() <= endDate.toDate().getTime()) {
                    
                    cancellationStream = cancellationController.generateDDCancellationDocument(
                            f.getDirectDebit().getContractPaymentTerm().getContract(), 
                            f, new LocalDate(cancellation.getCreationDate().getTime()));
                    try {
                        Attachment cancellationFile = Storage.storeTemporary(
                                "Direct_Debit_Cancellation_Form_DD_no_" + f.getId() + ".pdf", cancellationStream, "DD_CANCELLATION_FORM", true);
                        f.addAttachment(cancellationFile);
                    } finally {
                        StreamsUtil.closeStream(cancellationStream);
                    }
                }
                
                f.setLastDataCorrectionDate(new Date());
                directDebitFileRepository.save(f);
            }
            
            
            query.filterBy("id", ">", p.get(p.size() - 1).getId());
            p = query.execute();
            
            resume = !p.isEmpty();
        }
        
        return okResponse();
    }

    @PreAuthorize("hasPermission('directDebit','fetchrejectedandapprovedsignature')")
    @RequestMapping("/fetchrejectedandapprovedsignature")
    public ResponseEntity<?> fetchRejectedAndApprovedSignature(Pageable pageable) {

        String fromQuery = " from Client client where exists (" + getHasApprovedOrRejectedSignatureQuery("client.id") + ")";
        String clientsQueryString = "select client " + fromQuery;
        String clientsCountQueryString = "select count(client.id) " + fromQuery;
        SelectQuery<Client> clientSelectQuery = new SelectQuery<>(clientsQueryString, clientsCountQueryString, Client.class, new HashMap<>());
        Page<Client> clientPage = clientSelectQuery.execute(pageable);
        List<Map> result = new ArrayList<>();

        for (Client client : clientPage.getContent()) {
            String queryStr = getHasApprovedOrRejectedSignatureQuery(client.getId().toString());
            SelectQuery<Map> query = new SelectQuery<>(queryStr, "", Map.class, new HashMap<>());
            Map clientInfo = new HashMap();
            clientInfo.put("client_mobile", client.getMobileNumber());
            clientInfo.put("signatures", query.execute());
            result.add(clientInfo);
        }

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    private String getHasApprovedOrRejectedSignatureQuery(String clientId) {
        return " SELECT new Map(ddf.status as status, ddf.creationDate as date,dd.accountName as account, dd.ibanNumber as iban, att.uuid as uuid )"
                + " FROM DirectDebitFile ddf inner join DirectDebitSignature dds on dds = ddf.directDebitSignature"
                + " inner join Attachment att on dds.id = att.ownerId left join ddf.directDebit dd left join dd.contractPaymentTerm CPT"
                + " WHERE CPT.contract.client = " + clientId + " AND "
                + "(ddf.status = 'REJECTED' OR ddf.status = 'APPROVED') AND "
                + " att.ownerType = 'DirectDebitFile' AND att.tag = 'direct_debit_signature' ";
    }
    // ACC-1435 to here

    // ACC-2136
    @PreAuthorize("hasPermission('directDebit','getContractDDs')")
    @GetMapping(value = "/getcontractdds/{contractId}")
    public ResponseEntity getContractDDs(
            @PathVariable Long contractId,
            @RequestParam(name = "active_cpt_only", required = false, defaultValue = "true") Boolean activeCptOnly,
            @RequestParam(name = "aStatus", required = false) DirectDebitStatus aStatus,
            @RequestParam(name = "mStatus", required = false) DirectDebitStatus MStatus) {

        Contract contract = contractRepository.findOne(contractId);

        // ACC-6003
        String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

        boolean userCanShowIncompleteDds = CurrentRequest.getUser() != null &&
                CurrentRequest.getUser().hasPosition(SHOW_INCOMPLETE_DD_POSITION_CODE);

        List<DirectDebit> directDebits = directDebitRepository.findByContractAndActiveCPT(contractId, activeCptOnly);
        List<DirectDebit> filteredDDs = new ArrayList();

        for (DirectDebit dd : directDebits) {
            // ACC-2495
            //ACC-2886
            if (dd.getCategory().equals(DirectDebitCategory.A)) {
                if (((aStatus != null) || MStatus != null && !dd.getMStatus().equals(MStatus)))
                    continue;
            } else {
                if ((aStatus != null && !dd.getStatus().equals(aStatus)) || (MStatus != null && !dd.getMStatus().equals(MStatus)))
                    continue;
            }

            // ACC-6935
            if (shouldHideDd(dd, userCanShowIncompleteDds, startDate)) continue;

            for (DirectDebitFile ddf : dd.getDirectDebitFiles()) {
                ddf.setRecords(null);
                ddf.setDirectDebit(null);

                //ACC-3829
                if (ddf.getWhoRequestedToCancellDD() != null) {
                    User user = Setup.getRepository(UserRepository.class)
                            .findOne(ddf.getWhoRequestedToCancellDD()) ;
                    if (user != null)
                        ddf.setCancelingRequester(user.getName());
                }

                HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery<>(DirectDebitFile.class);
                historyQuery.filterBy("id", "=", ddf.getId());
                historyQuery.filterByChanged("ddStatus");
                historyQuery.sortBy("lastModificationDate", false, true);
                historyQuery.setLimit(1);
                List<DirectDebitFile> oldDirectDebitFiles = historyQuery.execute();
                if (!oldDirectDebitFiles.isEmpty())
                    ddf.setLastStatusModificationDate(oldDirectDebitFiles.get(0).getLastModificationDate());
                else  ddf.setLastStatusModificationDate(ddf.getCreationDate());

            }

            filteredDDs.add(dd);
        }

        return new ResponseEntity(filteredDDs.stream().map(dd -> projectionFactory.createProjection(
                DDClientProjection.class, dd)).collect(Collectors.toList()), HttpStatus.OK);
    }

    //ACC-8897
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT)
    @GetMapping("/getDDsInfoForGPT")
    public ResponseEntity<?> getDDsInfoForGPT(@RequestParam Long contractId) {
        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) throw new BusinessException("Contract not found");

        SelectQuery<DirectDebit> q = new SelectQuery<>(DirectDebit.class);
        q.filterBy("contractPaymentTerm.contract.id", "=", contract.getId());
        q.filterBy("contractPaymentTerm.isActive", "=", true);
        q.filterBy(new SelectFilter("status", "in", Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.CONFIRMED))
                 .or("MStatus", "in", Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.CONFIRMED)));

        List<DirectDebit> directDebits = q.execute();
        StringBuilder responseBuilder = new StringBuilder();
        for (int i = 0; i < directDebits.size(); i++) {
            DirectDebit dd = directDebits.get(i);

            responseBuilder.append(String.format("Total Direct debit payments for %s to %s :\n " +
                            "Amount: %s\n IBAN: %s\n Bank Name: %s\n (m)Status: %s\n (a)Status: %s\n\n",
                    dd.getStartDate(), dd.getExpiryDate(), ((long) dd.getAmount()), dd.getIbanNumber() != null ? dd.getIbanNumber() : "",
                    dd.getBankName() != null ? dd.getBankName() : "", dd.getMStatus(), dd.getStatus()));

            for (int j = 0; j < dd.getDirectDebitFiles().size(); j++) {
                if (j == 0) responseBuilder.append("Individual payment details:\n\n");

                DirectDebitFile ddf = dd.getDirectDebitFiles().get(j);
                responseBuilder.append(String.format("[DD Application Number: %s\n DD Type: %s\n Account Name: %s\n Rejection Reason: %s]\n\n",
                        ddf.getApplicationId() != null ? ddf.getApplicationId() : "", ddf.getDdFrequency().getLabel(),
                        ddf.getAccountName() != null ? ddf.getAccountName() : "",
                        ddf.getRejectionReason() != null ? ddf.getRejectionReason() : ""));
            }

            if (i < directDebits.size() - 1) responseBuilder.append("######\n");
        }

        return ResponseEntity.ok(new HashMap<String, String>() {{
            put("AllDDPaymentDetails", responseBuilder.toString());
        }});
    }

    private boolean shouldHideDd(DirectDebit d, boolean userCanShowIncompleteDds, String startDate) {
        if (Setup.getApplicationContext()
                .getBean(AppsServiceDDApprovalTodoService.class)
                .existsApprovalAttachmentsByDDC(d.getDdcId())) return false;

        if (d.getContractPaymentTerm().getContract().getCreationDate().getTime()
                < new DateTime(startDate).toDate().getTime()) return false;

        d.setHidden((d.getCategory().equals(DirectDebitCategory.A) &&
                        !d.getMStatus().equals(DirectDebitStatus.CONFIRMED)) ||
                (d.getCategory().equals(DirectDebitCategory.B) &&
                        !d.getStatus().equals(DirectDebitStatus.CONFIRMED)));

        return !userCanShowIncompleteDds && d.isHidden();
    }


    // ACC-2299
    @PreAuthorize("hasPermission('directDebit','mark-manual-ddfs/for-bouncing')")
    @PostMapping(value = "/mark-manual-ddfs/for-bouncing")
    @Transactional
    public ResponseEntity markDDFsAsForBouncing(@RequestBody List<Long> masterDDsIDs) {
        List<DirectDebit> masterDDs = directDebitRepository.findAll(masterDDsIDs);

        for (DirectDebit masterDD : masterDDs) {
            List<DirectDebit> ddImages = switchingBankAccountService.getDDImages(masterDD);
            for (DirectDebit dd : ddImages) {
                List<DirectDebitFile> ddfs = directDebitFileRepository.findByDirectDebitAndDdMethod(dd, DirectDebitMethod.MANUAL);
                logger.log(Level.INFO, "ddfs Count: " + ddfs.size());
                for (DirectDebitFile ddf : ddfs) {
                    logger.log(Level.INFO, "Marking DDF#" + ddf.getId() + ", As For Bounced Payment");
                    ddf.setForBouncingPayment(true);
                    directDebitFileRepository.save(ddf);
                }
            }
        }
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    // ACC-2299
    @PreAuthorize("hasPermission('directDebit','manual-oic/data-correction')")
    @PostMapping(value = "/manual-oic/data-correction")
    @Transactional
    public ResponseEntity dataCorrection(@RequestBody List<Long> masterDDsIDs,
                                         @RequestParam(value = "signaturesIDs", required = false) List<Long> signatureIDs) {
        
        List<DirectDebit> masterDDs = directDebitRepository.findAll(masterDDsIDs);

        int ddmIndex = 0;
        Map<String, Object> signatureType = directDebitSignatureService
                .getLastSignatureType(masterDDs.get(0).getContractPaymentTerm(), true, false);
        List<DirectDebitSignature> currentSignatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");
        for (DirectDebit masterDD : masterDDs) {
            if (masterDD.getDirectDebitFiles() == null)
                throw new RuntimeException("no DDs under this master: " + masterDD.getId());


            logger.log(Level.SEVERE, "delete Manual DDFs");
            List<Long> manualDDFsToBeRemovedIDs = masterDD.getDirectDebitFiles().stream()
                    .filter(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                            ((ddf.getDdStatus().equals(DirectDebitStatus.PENDING) && ddf.getStatus().equals(DirectDebitFileStatus.NOT_SENT)) ||
                                    (ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) || (ddf.getDdStatus().equals(DirectDebitStatus.IN_COMPLETE))))
                    .map(ddf -> ddf.getId())
                    .collect(Collectors.toList());

            if (currentSignatures == null)
                throw new RuntimeException("no signature for this contract: " + masterDD.getContractPaymentTerm().getContract().getId());


            DirectDebitConfiguration ddConfiguration = masterDD.getDdConfiguration();
            if (!masterDD.getDdConfiguration().isCreateManualForDDB()) {
                throw new RuntimeException("Can't create Manual DDs for DD3" + masterDD.getId() + " with Configuration#" + ddConfiguration.getId());
            }

            Integer numOfDDs = Math.min(ddConfiguration.getNumberOfGeneratedDDs(), currentSignatures.size());
            logger.info("numOfDDs: " + numOfDDs);

            logger.log(Level.SEVERE, "generating new Manual Weekly DDFs");
            for (int i = 0; i < numOfDDs; i++) {

                DirectDebitSignature signature = directDebitSignatureService.selectSignature(currentSignatures, numOfDDs, ddmIndex, i);
                DirectDebitFile ddf = directDebitService.createDDFile(masterDD, signature, true, true, DirectDebitMethod.MANUAL,
                        masterDD.getCategory().equals(DirectDebitCategory.B) ? DirectDebitType.DAILY : DirectDebitType.ONE_TIME);

                logger.log(Level.SEVERE, "Manual Weekly DDF generated successfully: " + ddf.getId());
            }

            masterDD.setIsSigned(false);
            masterDD.setMStatus(DirectDebitStatus.PENDING);

            masterDD.setStatus(masterDD.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) &&
                    ddf.getDdStatus().equals(DirectDebitStatus.CONFIRMED)) ? DirectDebitStatus.CONFIRMED :
                    masterDD.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) &&
                            ddf.getDdStatus().equals(DirectDebitStatus.PENDING)) ? DirectDebitStatus.PENDING : masterDD.getStatus());
            directDebitRepository.save(masterDD);

            for (Long manualDDFsToBeRemovedID : manualDDFsToBeRemovedIDs) {
                directDebitFileRepository.delete(directDebitFileRepository.getOne(manualDDFsToBeRemovedID));
            }

            logger.log(Level.SEVERE, "Manual Weekly DDFs deleted successfully");
        }
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directDebit','getDDFormExample')")
    @RequestMapping(value = "/getDDFormExample", method = RequestMethod.GET)
    public void getDDFFormExample(HttpServletResponse response) throws IOException {
        WordTemplateRepository templateRepository = Setup.getRepository(WordTemplateRepository.class);
        WordTemplateService wordTemplateService = Setup.getApplicationContext().getBean(WordTemplateService.class);

        WordTemplate template = templateRepository.findByCodeIgnoreCase("dd_form_example");

        InputStream inputStream = Storage.getStream(template.getAttachment("template"));
        InputStream in = wordTemplateService.generateDocument(inputStream, new HashMap());
        byte[] contents = PdfHelper.getPdfPages(in, "firstImage",
                PdfHelper.getDefaultDPIResolution(), PdfHelper.getDefaultDDImagesFormat(), PdfHelper.getDefaultQuality());

        response.setContentType(getMimeType("pdf"));
        response.addHeader("Content-Disposition",
                "attachment; filename=\"" + "Direct Debit File Form" + ".pdf\"");

        response.getOutputStream().write(contents);
        response.getOutputStream().flush();
    }

//    private List<Attachment> getSignaturesByContract(Contract contract) {
//        List<Attachment> result = new ArrayList();
//        List<DirectDebitFile> ddfs = directDebitFileRepository.getDDFsByContract(contract);
//        List<String> base64Signatures = new ArrayList();
//
//        for (DirectDebitFile ddf : ddfs) {
//            if (ddf.getDdStatus().equals(DirectDebitStatus.CONFIRMED) &&
//                    ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE) != null && !base64Signatures.contains(
//                    Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))))) {
//                base64Signatures.add(Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))));
//                result.add(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE));
//            }
//        }
//        if (result.size() >= 3) return result;
//
//        for (DirectDebitFile ddf : ddfs) {
//            if (ddf.getDdStatus().equals(DirectDebitStatus.PENDING) &&
//                    ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE) != null && !base64Signatures.contains(
//                    Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))))) {
//                base64Signatures.add(Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))));
//                result.add(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE));
//            }
//        }
//        if (result.size() >= 3) return result;
//
//        for (DirectDebitFile ddf : ddfs) {
//            if (ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) &&
//                    ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE) != null && !base64Signatures.contains(
//                    Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))))) {
//                base64Signatures.add(Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))));
//                result.add(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE));
//            }
//        }
//        if (result.size() >= 3) return result;
//
//        for (DirectDebitFile ddf : ddfs) {
//            if (ddf.getDdStatus().equals(DirectDebitStatus.REJECTED) &&
//                    ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE) != null && !base64Signatures.contains(
//                    Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))))) {
//                base64Signatures.add(Base64.getEncoder().encodeToString(getImageByteArray(Storage.getStream(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))));
//                result.add(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE));
//            }
//        }
//
//        return result;
//    }

    @Transactional
    public void runIncompleteDDInfoSJ(Long ddId) {
        DirectDebit dd = directDebitRepository.findOne(ddId);
        Integer inCompleteDDsInfoMaxReminder = Integer.valueOf(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_IN_COMPLETE_DD_MAX_REMINDER));

        if (dd.getInCompleteDDReminder() >= inCompleteDDsInfoMaxReminder) {

            dd.setContractScheduleDateOfTermination(
                    Setup.getApplicationContext()
                            .getBean(ContractService.class)
                            .setContractForTermination(dd.getContractPaymentTerm().getContract(), "client_did_not_sign_dd_after_x_days"));
        }

        dd.setSendIncompleteDDInfoMessages(true);
        directDebitRepository.save(dd);
    }

    @Transactional
    public void runDDExpiredJob(Long directDebitId) {
        DirectDebit directDebit = directDebitRepository.findOne(directDebitId);
        directDebit.getDirectDebitFiles()
                .stream()
                .filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.CONFIRMED) ||
                        (ddf.getDdStatus().equals(DirectDebitStatus.PENDING) &&
                                ddf.getStatus().equals(DirectDebitFileStatus.SENT)))
                .forEach(ddf -> {
                    DirectDebitFile f = directDebitFileRepository.findOne(ddf.getId());
                    f.setDdStatus(DirectDebitStatus.EXPIRED);
                    directDebitFileRepository.save(f);
                });

        directDebit.setStatus(DirectDebitStatus.EXPIRED);
        directDebit.setMStatus(DirectDebitStatus.EXPIRED);
        directDebit = directDebitRepository.save(directDebit);

        directDebitCancellationService.cancelWholeDD(directDebit, false, DirectDebitCancellationToDoReason.EXPIRED_DDS_CANCELLATION, true);
    }
     
    @Transactional
    public void oecAmendDDsFlow(Long contractId) {
        logger.info("start OEC amend dd flow function with contract "+contractId);
        boolean canceledSuccessfully = false;
        boolean getIntoFlow = false;
        Contract contract = contractRepository.findOne(contractId);
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        
        logger.info("start OEC amend dd flow function cpt ID is "+cpt.getId());
        if (cpt.getAmendedDate() == null)
            throw new RuntimeException("Cpt is changed!");
        
        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTermAndAddedByOecFlowOrderByCreationDate(cpt, true);
         logger.info("start OEC amend dd flow function dds count is "+dds.size());
       
        if (dds.isEmpty())
            throw new RuntimeException("DDs is changed!");
        
        DirectDebit firstDirectDebit = dds.get(0);
        logger.info("start OEC amend dd flow function firstDirectDebit "+firstDirectDebit.getId());
        boolean confirmed = dds.stream().allMatch(dd ->
            dd.getStatus().equals(DirectDebitStatus.CONFIRMED)
                || dd.getMStatus().equals(DirectDebitStatus.CONFIRMED));

        if(firstDirectDebit.getDirectDebitRejectionToDo() == null){
            getIntoFlow = (firstDirectDebit.getStatus().equals(DirectDebitStatus.CONFIRMED)||firstDirectDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) || 
                    (firstDirectDebit.getStatus().equals(DirectDebitStatus.REJECTED)&& firstDirectDebit.getMStatus().equals(DirectDebitStatus.REJECTED));
            if(getIntoFlow){
                logger.info("start OEC amend dd flow function firstDirectDebit is confirmed  "+firstDirectDebit.getId());
                oecAmendDDsService.closeOecAmendDDsFlow(contractId, confirmed);
                canceledSuccessfully =  oecAmendCancelOldDDs(contract, confirmed);
            } else if(firstDirectDebit.getStatus().equals(DirectDebitStatus.PENDING)&& firstDirectDebit.getMStatus().equals(DirectDebitStatus.PENDING)){
                logger.info("start OEC amend dd flow function firstDirectDebit is pending  "+firstDirectDebit.getId());
                
                if(firstDirectDebit.getDirectDebitFiles() != null && firstDirectDebit.getDirectDebitFiles().size() > 0 ){
                    boolean proceedSent = firstDirectDebit.getDirectDebitFiles() != null && 
                            firstDirectDebit.getDirectDebitFiles().stream().anyMatch(f -> f.getStatus().equals(DirectDebitFileStatus.SENT));
                    boolean pendingNotSent = firstDirectDebit.getDirectDebitFiles() != null &&
                            !firstDirectDebit.getDirectDebitFiles().isEmpty() &&
                            firstDirectDebit.getDirectDebitFiles().stream().allMatch(f -> f.getStatus().equals(DirectDebitFileStatus.NOT_SENT));
                    
                    logger.info("start OEC amend dd flow function firstDirectDebit is pending proceed value is "+proceedSent);
                    if(proceedSent){
                        getIntoFlow = true;
                        AccountingEntityProperty accountingEntityProperty = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.OEC_AMEND_DDS, firstDirectDebit.getContractPaymentTerm().getContract());
                        if(accountingEntityProperty != null){
                            accountingEntityProperty.setJobRunAndDDPending(true);
                            logger.info("start OEC amend dd flow function firstDirectDebit is pending after set value to 1 ");
                        } else {
                           throw new RuntimeException("start OEC amend dd flow function No property created for this contract "+firstDirectDebit.getContractPaymentTerm().getContract().getId());
                        }
                    } else if(pendingNotSent){
                        oecAmendDDsService.closeOecAmendDDsFlow(contractId);
                        canceledSuccessfully = oecAmendCancelOldDDs(contract);
                    } 
                } 
            }
        } else {
            DirectDebitRejectionToDo todo = firstDirectDebit.getDirectDebitRejectionToDo();
            DirectDebit lastDirectDebit = todo.getLastDirectDebit();
            
            logger.info("OEC amend dd flow function lastDirectDebit "+lastDirectDebit.getId());
            getIntoFlow = (lastDirectDebit.getStatus().equals(DirectDebitStatus.CONFIRMED)||lastDirectDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) || 
                    (lastDirectDebit.getStatus().equals(DirectDebitStatus.REJECTED)&& lastDirectDebit.getMStatus().equals(DirectDebitStatus.REJECTED))
                    ||(lastDirectDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)&& lastDirectDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE));
            
            if(getIntoFlow){
                logger.info("start OEC amend dd flow function firstDirectDebit is confirmed  "+firstDirectDebit.getId());
                oecAmendDDsService.closeOecAmendDDsFlow(contractId, confirmed);
                canceledSuccessfully =  oecAmendCancelOldDDs(contract, confirmed);
            } else if(lastDirectDebit.getStatus().equals(DirectDebitStatus.PENDING)&& lastDirectDebit.getMStatus().equals(DirectDebitStatus.PENDING)){
                logger.info("start OEC amend dd flow function rejection flow lastDirectDebit is pending  "+lastDirectDebit.getId());
                
                if(lastDirectDebit.getDirectDebitFiles() != null && lastDirectDebit.getDirectDebitFiles().size() > 0 ){        
                    boolean proceedSent = lastDirectDebit.getDirectDebitFiles() != null && 
                            lastDirectDebit.getDirectDebitFiles().stream().anyMatch(f -> f.getStatus().equals(DirectDebitFileStatus.SENT));
                    boolean pendingNotSent = lastDirectDebit.getDirectDebitFiles() != null &&
                            !lastDirectDebit.getDirectDebitFiles().isEmpty() &&
                            lastDirectDebit.getDirectDebitFiles().stream().allMatch(f -> f.getStatus().equals(DirectDebitFileStatus.NOT_SENT));
                        
                    getIntoFlow = true;
                    if(proceedSent){
                        AccountingEntityProperty accountingEntityProperty = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.OEC_AMEND_DDS, firstDirectDebit.getContractPaymentTerm().getContract());
                        if(accountingEntityProperty != null){
                            accountingEntityProperty.setJobRunAndDDPending(true);
                        } else {
                               throw new RuntimeException("start OEC amend dd flow function No property created for this contract "+firstDirectDebit.getContractPaymentTerm().getContract().getId());
                        }
                    }else if(pendingNotSent){
                        oecAmendDDsService.closeOecAmendDDsFlow(contractId);
                        canceledSuccessfully = oecAmendCancelOldDDs(contract);
                    }    
                } 
            }
        }
        if((canceledSuccessfully &&  getIntoFlow)|| !getIntoFlow){
            accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.OEC_AMEND_DDS, contract);
        }
    }
    public boolean oecAmendCancelOldDDs(Contract contract) {
         return oecAmendCancelOldDDs(contract, false);
    }

    @Transactional
    public boolean oecAmendCancelOldDDs(Contract contract, boolean confirmed) {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        if(cpt.getAmendedDate() == null) return false;
        
        List<DirectDebit> ddsToCancel;
        List<Payment> paymentsWithNoDDAs = new ArrayList<>();
        List<Payment> bouncedPayments;
        
        java.util.Date date = new LocalDate().toDate();
        logger.info("Confirmed: " + confirmed + "; contract: " + contract.getId());
        
        Map<String, Object> contractObject = new HashMap<>();
        contractObject.put("id", contract.getId());

        if(confirmed){
            //ACC-3597
            ddsToCancel = directDebitRepository.findByContractPaymentTermAndStartDateLessThan(cpt , DateUtil.getDateFromDateTime(cpt.getAmendedDate()));
            logger.info("OldDDs size: " + ddsToCancel.size());
            PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");

            //check if there is bounce payment
            bouncedPayments = paymentRepository.findByContractAndMethodOfPaymentAndStatusAndTypeOfPaymentAndReplaced(
                    contract, PaymentMethod.DIRECT_DEBIT, PaymentStatus.BOUNCED, monthlyPaymentType , false);
            logger.info("oldDDs before: " + ddsToCancel);
            
            if(!bouncedPayments.isEmpty()){
                for(Payment bouncedPayment : bouncedPayments){
                    logger.info("bouncedPayment: " +bouncedPayment.getId());
                    DirectDebit dd = oecAmendDDsService.getPaymentDD(bouncedPayment , cpt);
                    if(dd != null && ddsToCancel.contains(dd)){
                        logger.info("isDDsCoverPayment: " + dd.getId());
                        ddsToCancel.remove(dd);
                    }else if(dd == null && !bouncedPayment.getSentToBankByMDD()){
                        paymentsWithNoDDAs.add(bouncedPayment);
                    }                   
                }
            }
            
            logger.info("ddaList: " + paymentsWithNoDDAs);
            logger.info("oldDDs after: " +ddsToCancel);
            contractObject.put("workerSalary", contract.getTempWorkerSalary());
            cpt.setReason(ContractPaymentTermReason.OEC_AMEND_DDS);

            //ACC-4742
            ddsToCancel.forEach(directDebit -> logger.log(Level.SEVERE, "directDebit : {0}", directDebit.getId()));
            Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class)
                    .cancelAllMonthlyPlans(contract.getId());
        } else {
            ddsToCancel = directDebitRepository.findByContractPaymentTermAndStartDateGreaterThanEqual(
                    cpt, DateUtil.getDateFromDateTime(cpt.getAmendedDate()));
            logger.info("start oecAmendCancelOldDDs function in rejection flow oldDDs size is  " + ddsToCancel.size());

            contractObject.put("terminateContractDueRejection", true);
            contractObject.put("tempWorkerSalary", null);
            cpt.setAmendedDate(null);
        }
        
        for (DirectDebit dd : ddsToCancel) {
            directDebitCancellationService.cancelWholeDD(dd,
                    DirectDebitCancellationToDoReason.OEC_Flow);
        }
        
        contractPaymentTermRepository.save(cpt);
        Setup.getApplicationContext().getBean(ContractService.class)
                .updateContractFromClientMgt(contractObject);
        
        if(!paymentsWithNoDDAs.isEmpty()){
           logger.info("ddaList size: "+paymentsWithNoDDAs.size());
           for(Payment bouncedPayment : paymentsWithNoDDAs){
                DirectDebit relatedDD = directDebitRepository.findOne(bouncedPayment.getDirectDebit().getId());
                logger.info("relatedDD is : "+relatedDD.getId());
                
                try {
                    oecAmendDDsService.addNewOneTimeDD(contract.getId(), date,
                            bouncedPayment.getAmountOfPayment(), relatedDD, bouncedPayment);
                } catch(Exception ex) {
                    logger.log(Level.SEVERE, "Exception in oecAmendCancelOldDDs for contract# " + contract.getId(), ex);
                    throw new RuntimeException(ex.getMessage());
                }
            }
        }
        
        return true; 
    }

    @PreAuthorize("hasPermission('directDebit','getActiveCptInfo')")
    @RequestMapping(value = "/getActiveCptInfo/{contractId}", method = RequestMethod.GET)
    public ResponseEntity getActiveCptInfo(@PathVariable("contractId") Contract contract, Pageable pageable) {
        Map<String, Object> response = new HashMap<>();
        ContractPaymentTerm cpt = null;
        try {
            cpt = contract.getActiveContractPaymentTerm();
            response.put("cptName", cpt.getPaymentTermConfig() != null ? cpt.getPaymentTermConfig().getName() : "N\\A");
            response.put("nationality", cpt.getHousemaid().getNationality().getName());
            response.put("contract", cpt.getContractProspectType().getName());
            response.put("package", cpt.getPaymentTermConfig() != null && cpt.getPaymentTermConfig().getPackageType() != null ?
                    cpt.getPaymentTermConfig().getPackageType().getLabel() : "N\\A");
            response.put("type", cpt.getPaymentTermConfig() != null && cpt.getPaymentTermConfig().getType() != null ?
                    cpt.getPaymentTermConfig().getType().getLabel() : "N\\A");
            response.put("agencyFee", cpt.getAgencyFee());
            response.put("Default", cpt.getPaymentTermConfig() != null ? cpt.getPaymentTermConfig().isDefault() : "N\\A");
            List<ContractPaymentType> contractPaymentTypes = cpt.getContractPaymentTypes();
            if (contractPaymentTypes != null) {
                if (contract.isMaidVisa()) {
                    contractPaymentTypes.stream()
                            .filter(contractPaymentType -> PaymentHelper.isMonthlyPayment(contractPaymentType.getType()) &&
                                    contractPaymentType.getDiscount() != null)
                            .findFirst()
                            .ifPresent(contractPaymentType -> contractPaymentType.setDiscount(contract.getWorkerSalaryNew()));
                }
            }
            response.put("cptPaymentTypes", cpt.getContractPaymentTypes());
            response.put("dailyRate", cpt.getDailyRateAmount() != null ? cpt.getDailyRateAmount() : "N\\A");
            if (cpt.getWeeklyAmount() > 0.0)
                response.put("weeklyRate", cpt.getWeeklyAmount());
        } catch (Exception e) {
            e.printStackTrace();
        }
        response.put("cancelledDds", directDebitRepository.getDdfForInactiveCpt(contract.getId(), cpt != null ? cpt.getId() : -1L, pageable));

        return new ResponseEntity(response, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directDebit','generateDDActivationFile')")
    @PostMapping("/generateDDActivationFile")
    public ResponseEntity<?> generateDDActivationFile(
            @RequestBody List<Long> contractIds) {

        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        query.filterBy("ddStatus", "=", DirectDebitStatus.PENDING);
        query.filterBy("directDebit.contractPaymentTerm.contract.id", "in", contractIds);
        List<DirectDebitFile> p = query.execute();

        for(DirectDebitFile f : p) {
            directDebitService.createDirectDebitActivationAttachmentIfNotExist(f);
        }

        return okResponse();
    }
    //ACC-5227
    @PreAuthorize("hasPermission('directDebit','amendClientsDdsACC5227')")
    @PostMapping("/amendClientsDdsACC5227")
    public ResponseEntity<?> amendClientsDdsACC5227(
        MultipartFile file,
        @RequestParam("ddaStartDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date ddaStartDate) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) return new ResponseEntity<>("No sheet found", HttpStatus.BAD_REQUEST);

        amendClientsDds(sheet, ddaStartDate);

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }
    //ACC-5227
    @Transactional
    private void amendClientsDds(
        Sheet sheet, Date ddaStartDate) {

        logger.log(Level.SEVERE, "Sheet Name = {0}", sheet.getSheetName());
        logger.log(Level.SEVERE, "last row number = {0}", sheet.getLastRowNum());

        for (Row row : sheet) {
            try {
                if (row.getRowNum() == 0) continue;
                if (row.getRowNum() > 100) break;

                logger.log(Level.SEVERE, "Row Num = {0}", row.getRowNum());
                Long contractId = (long) row.getCell(0).getNumericCellValue();
                logger.log(Level.SEVERE, "contract id: {0}", contractId);

                Double oldAmount = (Double) row.getCell(1).getNumericCellValue();
                Double newDdaAmount = (Double) row.getCell(2).getNumericCellValue();
                Double newDdbAmount = (Double) row.getCell(3).getNumericCellValue();
                logger.log(Level.SEVERE, "oldAmount: {0}; newDdaAmount: {1}; newDdbAmount: {1}",
                    new Object[]{oldAmount, newDdaAmount, newDdbAmount});

                Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                        "amendClientsDdsBGT_" + contractId + "_" + new java.util.Date().getTime(),
                        "accounting",
                        "directDebitController",
                        "amendClientsDdsBGT")
                        .withRelatedEntity("Contract", contractId)
                        .withParameters(
                            new Class[] {String.class, String.class,
                                    String.class, String.class,
                                    String.class},
                            new Object[] { contractId.toString(), oldAmount.toString(),
                                    newDdaAmount.toString(), newDdbAmount.toString(),
                                    DateUtil.formatDateDashed(ddaStartDate) })
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // ACC-5227
    @Transactional
    public void amendClientsDdsBGT (
            String contractIdStr,
            String oldAmountStr,
            String newDdaAmountStr,
            String newDdbAmountStr,
            String ddaStartDateStr) throws Exception {

        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        ContractPaymentTermController contractPaymentTermController =
                Setup.getApplicationContext().getBean(ContractPaymentTermController.class);
        DirectDebitCancelationToDoController cancelationToDoController =
                Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        ContractPaymentTypeRepository contractPaymentTypeRepository =
                Setup.getRepository(ContractPaymentTypeRepository.class);

        PicklistItem monthlyPL = getItem("TypeOfPayment", "monthly_payment");

        Long contractId = Long.valueOf(contractIdStr);
        Double oldAmount = Double.valueOf(oldAmountStr);
        Double newDdaAmount = Double.valueOf(newDdaAmountStr);
        Double newDdbAmount = Double.valueOf(newDdbAmountStr);

        logger.log(Level.SEVERE, "contract id: {0}", contractId);
        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) {
            logger.log(Level.SEVERE, "contract id: {0} not found", contractId);
            return;
        }

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        logger.log(Level.SEVERE, "oldAmount: {0}; newDdaAmount: {1}; newDdbAmount: {2}; ddaStartDateStr: {3}",
                new Object[]{ oldAmount, newDdaAmount, newDdbAmount, ddaStartDateStr });

        List<DirectDebitStatus> inactiveStatuses = Arrays.asList(DirectDebitStatus.EXPIRED,
                DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION);

        SelectQuery<DirectDebit> query = new SelectQuery<>(DirectDebit.class);
        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
        query.filterBy("amount", "=", oldAmount);
        query.filterBy(new SelectFilter("status", "not in", inactiveStatuses)
                .or("MStatus", "not in", inactiveStatuses));
        query.sortBy("id", false);
        List<DirectDebit> ddList = query.execute();
        if (ddList.isEmpty()) {
            logger.log(Level.SEVERE, "contract id: {0} not dd found", contractId);
            return;
        }

        DirectDebit oldDd = ddList.stream()
                .filter(dd -> dd.getCategory().equals(DirectDebitCategory.B))
                .collect(Collectors.toList())
                .get(0);
        logger.log(Level.SEVERE, "oldDd id: {0}", oldDd.getId());

        //cancel old dd
        ddList.forEach(directDebit ->
                directDebitCancellationService.cancelWholeDD(
                        directDebit, null));

        //update monthly amount
        cpt.getContractPaymentTypes().stream()
                .filter(c -> c.getType().getCode().equals("monthly_payment"))
                .findFirst().ifPresent(c -> {
                        c.setAmount(newDdbAmount);
                        c.setDiscount(0.0);
                        contractPaymentTypeRepository.save(c);
                });
        DateTime ddaStartDate = new DateTime(DateUtil.parseDateDashed(ddaStartDateStr));

        //generate ddb
        List<DirectDebitDTO> dtoDdList = new ArrayList<>();
        dtoDdList.add(new DirectDebitDTO(
                newDdbAmount,
                ddaStartDate.plusMonths(1).toDate(),
                oldDd.getExpiryDate(),
                DirectDebitType.MONTHLY,
                null,
                null));

        //generate dda
        dtoDdList.add(new DirectDebitDTO(
            newDdaAmount,
            ddaStartDate.toDate(),
            ddaStartDate.plusMonths(ontTimeDDMonthDuration).toDate(),
            DirectDebitType.ONE_TIME,
            monthlyPL,
            null));

        List<String> paymentIDs = contractPaymentTermController.addNewManualDD(contract,
                contract.getClientPaidVat(), true,
                dtoDdList, false , 0, new HashMap<>())
            .stream()
            .map(Object::toString)
            .collect(Collectors.toList());
        logger.info("paymentIDs: "+ paymentIDs);

        // ACC-3597
        contractPaymentTermController.createBGTToUpdateContractPaymentTermWithPayments(
                cpt, true, true, paymentIDs,
                new ArrayList<>(), false, true);
    }

    // ACC-6197
    @PreAuthorize("hasPermission('directDebit','ddfDataCorrectionAcc6197')")
    @GetMapping("/ddfDataCorrectionAcc6197/{id}")
    @Transactional
    public ResponseEntity<?> ddfDataCorrectionAcc6197(@PathVariable("id") ContractPaymentTerm cpt) {
        List<DirectDebit> dds = directDebitRepository.findForAcc6197(cpt);

        if (dds.isEmpty())
            return ResponseEntity.ok("There are no dd with status pending or pending data entry");

        List<DirectDebitSignature> directDebitSignatures =
                (List<DirectDebitSignature>) directDebitSignatureService.getLastSignatureType(
                        cpt, true, false).get("currentSignatures");
        StringBuilder r = new StringBuilder();

        for (int i = 0; i < dds.size(); i++) {
            DirectDebit d = dds.get(i);
            Integer nbrOfShouldGenerateDDFs = d.getDdConfiguration().getNumberOfGeneratedDDs();
            logger.info("dd id: " + d.getId() + "; nbrOfShouldGenerateDDFs: " + nbrOfShouldGenerateDDFs);

            switch (d.getCategory()) {
                case A:
                    if (d.getDirectDebitFiles().size() == nbrOfShouldGenerateDDFs) continue;

                    r.append("Master dd id: ")
                            .append(d.getId())
                            .append(" has number of files didn't match the bank configuration count -> we regenerated the files. ");
                    logger.info("start regenerate files for dd: " + d.getId());

                    d.setDirectDebitFiles(null);
                    directDebitService.createDirectDebitFileFromSignatures(d, directDebitSignatures, true, i);
                    directDebitRepository.save(d);

                    break;
                case B:
                    if (d.getDirectDebitFiles().stream()
                            .filter(ddf -> ddf.getDdMethod()
                                    .equals(DirectDebitMethod.AUTOMATIC)).count() == nbrOfShouldGenerateDDFs &&
                            (!d.isGenerateManualDDFsFromConfig() || (d.getDirectDebitFiles().stream()
                                    .filter(ddf -> ddf.getDdMethod()
                                            .equals(DirectDebitMethod.MANUAL)).count() == nbrOfShouldGenerateDDFs))) continue;

                    r.append("Master dd id: ")
                            .append(d.getId())
                            .append(" has number of files didn't match the bank configuration count -> we regenerated the files. ");
                    logger.info("start regenerate files for dd: " + d.getId());
                    List<DirectDebitFile> l = new ArrayList<>();

                    if (Arrays.asList(DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING)
                            .contains(d.getStatus())) {

                        logger.info("dd status is pending : " + d.getId());
                        l.addAll(directDebitService.regenerateFilesForDdb(d, i, DirectDebitMethod.AUTOMATIC, directDebitSignatures));
                    }

                    if (Arrays.asList(DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING)
                            .contains(d.getMStatus()) && d.isGenerateManualDDFsFromConfig()) {

                        logger.info("dd mStatus is pending : " + d.getId());
                        l.addAll(directDebitService.regenerateFilesForDdb(d, i, DirectDebitMethod.MANUAL, directDebitSignatures));
                    }

                    if (l.isEmpty()) continue;

                    directDebitFileRepository.save(l);
                    break;
            }
        }

        if (r.toString().isEmpty())
            r.append("There are no dd with status pending or pending data entry have a number of files didn't match the bank configuration count");

        return ResponseEntity.ok(r.toString());
    }

    @PreAuthorize("hasPermission('directDebit','migrateEidDocument')")
    @PostMapping("/migrateEidDocument")
    public ResponseEntity<?> migrateEidDocument() {
        PicklistItem t = Setup.getItem("ClientDocumentType", "EMIRATES_ID_FRONT_SIDE");

        List<Long[]> ids = directDebitRepository.migrateEidDocumentQuery(-1L);

        while(!ids.isEmpty()) {
            for(Long[] a : ids) {
                ClientDocument cDoc = new ClientDocument();
                cDoc.setAttachments(Arrays.asList(attachementRepository.findOne(a[1])));
                cDoc.setName("EMIRATES_ID_FRONT_SIDE");
                cDoc.setClient(clientRepository.findOne(a[0]));
                cDoc.setUploadDate(new Date(System.currentTimeMillis()));
                cDoc.setType(t);
                clientDocumentRepository.save(cDoc);
            }

            ids = directDebitRepository.migrateEidDocumentQuery(ids.get(ids.size() - 1)[0]);
        }



        return okResponse();
    }

    @PreAuthorize("hasPermission('directDebit','addDdsToContractAcc8101')")
    @PostMapping("/addDdsToContractAcc8101")
    public ResponseEntity<?> addDdsToContractAcc8101(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) return new ResponseEntity<>("No sheet found", HttpStatus.BAD_REQUEST);

        directDebitService.addDdsToContractAcc8101(sheet);
        return okResponse();
    }

    @PreAuthorize("hasPermission('directDebit','cancelDDList')")
    @PostMapping("/cancelDDList")
    public ResponseEntity<?> cancelDDList(MultipartFile file) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) return new ResponseEntity<>("No sheet found", HttpStatus.BAD_REQUEST);

        directDebitService.cancelDDList(sheet);
        return ResponseEntity.ok("done");
    }

    @PreAuthorize("hasPermission('directDebit','confirmPendingDdsAcc9048')")
    @PostMapping(value = "/confirmPendingDdsAcc9048")
    public ResponseEntity<?> confirmPendingDdsAcc9048(@RequestBody List<Long> ddfIds) {
        DirectDebitStatusChangeService directDebitStatusChangeService =
                Setup.getApplicationContext().getBean(DirectDebitStatusChangeService.class);
        ddfIds.forEach(id -> {
            try {
                directDebitStatusChangeService.ddFileApproved(directDebitFileRepository.findOne(id));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return ResponseEntity.ok("Done");
    }
}