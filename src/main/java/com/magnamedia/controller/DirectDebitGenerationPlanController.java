package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.DirectDebitGenerationPlanProjection;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitGenerationPlanRepository;
import com.magnamedia.service.DirectDebitGenerationPlanService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.stream.Collectors;

//ACC-3741
@RestController
@RequestMapping("/DirectDebitGenerationPlan")
public class DirectDebitGenerationPlanController extends BaseRepositoryController<DirectDebitGenerationPlan> {

    @Autowired
    DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;

    @Autowired
    DirectDebitGenerationPlanService directDebitGenerationPlanService;

    @Override
    public BaseRepository<DirectDebitGenerationPlan> getRepository() {
        return directDebitGenerationPlanRepository;
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan', 'getfailedddsgeneration')")
    @RequestMapping(value = "/getfailedddsgeneration", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getFailedDDsGeneration(Pageable pageable) {

        SelectQuery<DirectDebitGenerationPlan> query = new SelectQuery<>(DirectDebitGenerationPlan.class);
        SelectFilter filter = new SelectFilter("ddGenerationDate", "<",
                new Date(new LocalDate().toDate().getTime()))
                .and("ddGenerationPlanStatus", "=", DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
        query.filterBy(filter);
        return new ResponseEntity(project(query.execute(pageable), DirectDebitGenerationPlanProjection.class)
                , HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan', 'getpostponedds')")
    @GetMapping(value = "/getpostponedds/{contractId}")
    @ResponseBody
    public ResponseEntity<?> getPostponeDDs(@PathVariable("contractId") Contract contract) {
        List<DirectDebitGenerationPlan> plans = directDebitGenerationPlanRepository
                .findByContractAndDdGenerationPlanStatusIn(contract,
                        Collections.singletonList(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING));

        return new ResponseEntity(
                project(plans, DirectDebitGenerationPlanProjection.class),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan', 'generatepostponedds')")
    @RequestMapping(value = "/generatepostponedds", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> generatePostponeDDs(@RequestBody List<Long> DirectDebitPlanIds) {

        for (Long id : DirectDebitPlanIds) {
            directDebitGenerationPlanService.generateDDFromGenerationPlan(id);
        }
        return okResponse();
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan','dataMigrationAcc4496')")
    @PostMapping("/dataMigrationAcc4496")
    public ResponseEntity<?> dataMigrationAcc4496(MultipartFile file) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null)
            return new ResponseEntity("No sheet found", HttpStatus.BAD_REQUEST);

        generatePlansForNonMonthlyPayment(sheet, AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan','dataMigrationAcc4766')")
    @PostMapping("/dataMigrationAcc4766")
    public ResponseEntity<?> dataMigrationAcc4766(MultipartFile file) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null)
            return new ResponseEntity("No sheet found", HttpStatus.BAD_REQUEST);

        generatePlansForNonMonthlyPayment(sheet, AbstractPaymentTypeConfig.INSURANCE_TYPE_CODE);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @Transactional
    private void generatePlansForNonMonthlyPayment(Sheet sheet, String contractPaymentTypeCode) {
        logger.info("Sheet Name: " + sheet.getSheetName());
        logger.info( "last row number: " + sheet.getLastRowNum());

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);

        for(Row row : sheet) {
            try {
                if (row.getRowNum() == 0) continue;
                logger.info("Row Num: " + row.getRowNum());
                Long contractId = (long) row.getCell(0).getNumericCellValue();
                logger.info("contract id: " + contractId);
                Contract contract = contractRepository.findOne(contractId);
                if (contract == null) {
                    logger.log(Level.SEVERE, "contract id: {0} not found", contractId);
                    continue;
                }

                if (contract.getContractProspectType().getCode()
                        .equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
                    logger.log(Level.SEVERE, "contract id: {0} CC was ignore", contractId);
                    continue;
                }

                ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();
                if(contractPaymentTerm.getContractPaymentTypes() == null ||
                        contractPaymentTerm.getContractPaymentTypes().isEmpty()) {

                    logger.log(Level.SEVERE, "no contract payment types found");
                    continue;
                }

                List<ContractPaymentType> contractPaymentTypeSdr =
                        contractPaymentTerm.getContractPaymentTypes().stream()
                                .filter(t -> t.getType().getCode().equals(contractPaymentTypeCode) &&
                                        t.getRecurrence() != null && t.getRecurrence() > 0)
                                .collect(Collectors.toList());

                if (contractPaymentTypeSdr.isEmpty()) {
                    logger.log(Level.SEVERE, "no contract payment type \"same day recruitment fee\" found");
                    continue;
                }

                for(ContractPaymentType t : contractPaymentTypeSdr) {
                    logger.log(Level.SEVERE, "Generate postpone DD contractPaymentType id " + t.getId());

                    DateTime fromDate = new DateTime(contract.getStartOfContract())
                            .withTimeAtStartOfDay();
                    DateTime toDate = fromDate.plusMonths(
                            contractPaymentTerm.isIsProRated() ?
                                    contract.getPaymentsDuration() :
                                    contract.getPaymentsDuration() - 1)
                            .withDayOfMonth(1);

                    Map<String, Object> plansMap = directDebitGenerationPlanService.buildDirectDebitGenerationPlans(t, fromDate, toDate);

                    ((List<DirectDebitGenerationPlan>) plansMap.get("plans"))
                            .forEach(directDebitGenerationPlanService::saveGenerationPlan);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan','updateTemplatesAcc6444')")
    @PostMapping("/updateTemplatesAcc6444")
    public ResponseEntity<?> updateTemplatesAcc6444(MultipartFile file) throws IOException {

        directDebitGenerationPlanService.updateMessagesAcc6444(file);
        return ResponseEntity.ok("Done ^_^");
    }

    @PreAuthorize("hasPermission('DirectDebitGenerationPlan','dateCorrectionAcc6444')")
    @GetMapping("/dateCorrectionAcc6444")
    public ResponseEntity<?> dateCorrectionAcc6444(
            @RequestParam(value = "email" , required = false, defaultValue = "") String email,
            @RequestParam(value = "withSave", required = false, defaultValue = "false") boolean withSave) throws IOException {

        directDebitGenerationPlanService.dateCorrectionAcc6444(email, withSave);
        return ResponseEntity.ok("Done ^_^");
    }


    @PreAuthorize("hasPermission('DirectDebitGenerationPlan', 'migrateFutureDdsAcc8623')")
    @GetMapping("/migrateFutureDdsAcc8623")
    public ResponseEntity<?> migrateFutureDdsAcc8623() {
        Long lastId = -1L;
        Page<DirectDebitGenerationPlan> page;
        Date start = new Date(new LocalDate().toDate().getTime());

        int postponePeriod = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.DD_GEN_PRE_POSTPONE_PERIOD));
        int startDayPeriod  = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY));
        do {
            page = directDebitGenerationPlanRepository
                    .findDirectDebitGenerationPlansByDDGenerationDateAfter(lastId, start,
                            PageRequest.of(0, 200));

            logger.info("last id: " + lastId);

            page.getContent().forEach(d -> {
                logger.info("DirectDebitGenerationPlan id: " + d.getId() +
                                 "; DDGenerationDate: " + d.getDDGenerationDate());


                d.setDDGenerationDate(java.sql.Date.valueOf(
                        new LocalDate(d.getDDSendDate()).minusDays(startDayPeriod + postponePeriod).toString("yyyy-MM-dd")));
                directDebitGenerationPlanRepository.silentSave(d);
            });

            if (!page.getContent().isEmpty()) {
                lastId = page.getContent().get(page.getContent().size() - 1).getId();
            }

        } while (!page.getContent().isEmpty());

        return ResponseEntity.ok("Done ^_^");
    }
}
