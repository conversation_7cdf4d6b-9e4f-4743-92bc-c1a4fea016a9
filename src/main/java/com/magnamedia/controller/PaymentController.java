package com.magnamedia.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Iterables;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ccapp.CcAppTracking.CcAppAction;
import com.magnamedia.entity.dto.ClientPaymentSearchApiDto;
import com.magnamedia.entity.projection.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.*;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PdfHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.NewVisaRequestExpenseRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.*;
import com.magnamedia.service.CCAppContentService.CcPaymentSectionButton;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kanaan <<EMAIL>>
 *         Created on Jan 17, 2018
 */
@RequestMapping("/payments")
@RestController
public class PaymentController extends BaseRepositoryController<Payment> {

    private static final Logger logger = Logger.getLogger(PaymentController.class.getName());
    private static final String prefix = "MMM ";

    //Jirra ACC-1196
    public static final Integer csvMaxLimit = 2000;
    private int getCsvMailMaxLimit() {

        return Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENTS_PAGE_CSV_ROWS_LIMIT));
    }

    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private InterModuleConnector interModuleConnector;
    @Autowired
    private EmailHelper emailHelper;
    @Autowired
    private CCAppService ccAppService;
    @Autowired
    private CCAppContentService ccAppContentService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private BankStatementFileService bankStatementFileService;
    @Autowired
    private PicklistItemRepository picklistitemRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private QueryService queryService;
    @Autowired
    private UserRepository userRepository;
    @PersistenceContext
    private EntityManager entityManger;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService;

    @Override
    public BaseRepository<Payment> getRepository() {
        return this.paymentRepository;
    }

    // Moved from CM ACC-6103
    @Override
    public ResponseEntity<?> createEntity(Payment payment) {

        payment = paymentService.createPayment(payment);

        if (payment != null) {
            return new ResponseEntity<>(project(payment, PaymentProjection.class), HttpStatus.OK);
        }

        return new ResponseEntity<>("payment is locked or you don't have the right position", HttpStatus.BAD_REQUEST);
    }

    @UsedBy(modules = UsedBy.Modules.Client_Management)
    @Transactional
    public Map createFromJson(Map properties) throws IOException {
        logger.log(Level.SEVERE, "payment properties keys: " + properties.keySet());
        logger.log(Level.SEVERE, "payment properties values: " + properties.values());

        ObjectMapper objectMapper = Setup.getApplicationContext().getBean(ObjectMapper.class);
        Payment payment = (Payment) createFromJson((ObjectNode) objectMapper.valueToTree(properties)).getBody();

        Map result = new HashMap();
        result.put("id", payment.getId());
        result.put("amountOfPayment" , payment.getAmountOfPayment());
        result.put("vat", payment.getVat());

        return result;
    }

    @UsedBy(modules = UsedBy.Modules.Client_Management)
    @Transactional
    public ResponseEntity<?> createFromJson( @RequestBody ObjectNode objectNode) throws IOException {

        Payment payment = parse(objectNode);
        createEntity(payment);
        return new ResponseEntity<>(payment, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<?> updateEntity(Payment payment) {

        entityManger.detach(payment);
        List<Attachment> tempAttachments = payment.getAttachments().stream()
                .filter(e -> e.isTemporary()).collect(Collectors.toList());

        Payment finalPayment = payment;
        tempAttachments.forEach(attachment -> {
            paymentService.addNewAttachmentToTransaction(finalPayment, attachment);
        });

        payment = paymentService.updatePayment(payment);
        if (payment != null) {
            return new ResponseEntity<>(payment, HttpStatus.OK);
        }

        return new ResponseEntity<>("payment is locked or you don't have the right position", HttpStatus.BAD_REQUEST);
    }

    @PreAuthorize("hasPermission('Payments','getPayments')")
    @RequestMapping("getPayments/{id}")
    @Searchable(
            fieldName = "status",
            label = "Status",
            entity = Payment.class,
            apiKey = "payments_list",
            valuesApi = "/accounting/payments/getPaymentsStatus"
    )
    @Searchable(
            fieldName = "typeOfPayment",
            label = "Type Of Payment",
            entity = Payment.class,
            apiKey = "payments_list",
            valuesApi = "/accounting/payments/getPaymentsTypes"
    )
    @Searchable(
            fieldName = "dateOfPayment",
            label = "Date Of Payment",
            entity = Payment.class,
            apiKey = "payments_list"
    )
    @Searchable(
            fieldName = "amount",
            label = "Amount",
            entity = Payment.class,
            apiKey = "payments_list"
    )
    @Searchable(
            fieldName = "vat",
            label = "Vat",
            entity = Payment.class,
            apiKey = "payments_list"
    )
    @Searchable(
            fieldName = "discount",
            label = "Discount",
            entity = Payment.class,
            apiKey = "payments_list"
    )
    @ResponseBody
    public ResponseEntity<?> getPayments(@PathVariable Long id,
             @RequestParam(value = "includeDeleted", required = false) Boolean includeDeleted,
             @RequestParam(value = "tab" , required = false, defaultValue = "ALL") PaymentTab paymentTab,
             @RequestParam(value = "firstLoad", required = false, defaultValue = "false") Boolean firstLoad,
             Pageable pageable) throws Exception {
        Contract contract = contractRepository.findOne(id);

        if (contract == null) {
            return new ResponseEntity<>("Please check the passed contract id", HttpStatus.BAD_REQUEST);
        }

        Page<ClientPaymentSearchApiDto> payments = queryService.getPaymentsSearchApi(contract, includeDeleted, paymentTab, firstLoad, pageable);

        if (payments.getContent().isEmpty()) {
            return okResponse();
        }

        ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService =
                Setup.getApplicationContext().getBean(ContractPaymentConfirmationToDoService.class);
        for (ClientPaymentSearchApiDto payment : payments.getContent()) {
            if (payment.getTodo() == null) {
                logger.info("todo is null for payment :" + payment.getId());
                continue;
            }
            String paymentLink = contractPaymentConfirmationToDoService.getPayingViaCreditCardLink(payment.getTodo());
            if (paymentLink == null || paymentLink.isEmpty()) continue;

            List<ContractPaymentWrapper> contractPayments = payment.getTodo().getContractPaymentList();
            logger.info("contractPayments size : " + contractPayments.size());
            List<String> includedPaymentsDetails = payment.getTodo().getContractPaymentList()
                    .stream()
                    .map(p -> "AED " + p.getAmount())
                    .collect(Collectors.toList());

            String note = " Payment link: <a target=\"_blank\" class=\"read-only-ignored\" href=\"" + paymentLink + "\">" + paymentLink + "</a>";
            if (!includedPaymentsDetails.isEmpty()) {
                note += " {For " + String.join(", ", includedPaymentsDetails) + "}";
            }
            payment.setNote(note);
        }
        return ResponseEntity.ok(project(payments, ClientPaymentsSearchProjection.class));
    }

    @PreAuthorize("hasPermission('Payments','refundAll')")
    @RequestMapping("/refundAll")
    @ResponseBody
    public ResponseEntity<?> refundAllPayments(@RequestParam(
            value = "contractId") long contractId) {
        Contract contract = contractRepository.findOne(contractId);
        List<Payment> payments = paymentRepository.findByContractAndPrepareToRefund(
                contract,
                true);
        if (payments.size() > 0) {
            for (Payment temp : payments) {
                temp.setStatus(PaymentStatus.RETURNED_TO_CLIENT);
                temp.setPrepareToRefund(false);
                paymentService.forceUpdatePayment(temp);
            }
            return new ResponseEntity<>(
                    "All the requested to update payments were refunded",
                    HttpStatus.OK);
        }
        return new ResponseEntity<>("Nothing to update",
                HttpStatus.BAD_REQUEST);
    }

    @PreAuthorize("hasPermission('Payments','getTodayPayments')")
    @RequestMapping("/getTodayPayments")
    public ResponseEntity<?> getTodayPayments(
            @RequestParam(value = "contractId") long contractId) {
        Contract contract = contractRepository.findOne(contractId);
        Calendar calendar = Calendar.getInstance();
        java.util.Date currentDate = calendar.getTime();
        List<Payment> payments = paymentRepository.findByContractAndDateOfPayment(
                contract,
                new java.sql.Date(currentDate.getTime()));

        return new ResponseEntity<>(payments,
                HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('Payments','getAllPayments')")
    @RequestMapping("/getAllPayments")
    public ResponseEntity getAllPayments(
            @RequestParam(value = "contractId") long contractId) {
        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        query.filterBy("contract.id",
                "=",
                contractId);
        query.filterBy(
                new SelectFilter()
                        .and("status",
                                "!=",
                                PaymentStatus.BOUNCED)
                        .and("status",
                                "!=",
                                PaymentStatus.RETURNED_TO_CLIENT)
                        .and("status",
                                "!=",
                                PaymentStatus.UNCOLLECTED)
                        .and("status",
                                "!=",
                                PaymentStatus.TEARED_UP)
        );
        List<Payment> payments = query.execute();
        return new ResponseEntity(payments,
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('Payments','generateInvoiceForPendingPayments')")
    @RequestMapping("/generateInvoiceForPendingPayments")
    public ResponseEntity<?> generateInvoiceForPendingPayments(@RequestParam(
            value = "contractId") long contractId,
                                                               @RequestParam(value = "ungivenVacations") int ungivenVacations,
                                                               @RequestParam(
                                                                       value = "paidVacations") int paidVacations) {

        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) {
            return new ResponseEntity("please check contract id",
                    HttpStatus.BAD_REQUEST);
        }
        if (contract.getAdjustedEndDate() == null) {
            return new ResponseEntity("contract adjusted end date is not set",
                    HttpStatus.BAD_REQUEST);
        }
        //Get "Monthly Payment" picklist item
        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");

        //Get the date of last payment
        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        query.filterBy("contract.id",
                "=",
                contractId);
        query.filterBy(
                new SelectFilter()
                        .or("status",
                                "=",
                                PaymentStatus.PDC)
                        .or("status",
                                "=",
                                PaymentStatus.RECEIVED)
        );

        query.filterBy("typeOfPayment",
                "=",
                monthlyPayment);
        query.sortBy("dateOfPayment",
                false);
        List<Payment> payments = query.execute();
//        for (Payment payment : payments) {
//
//
//        }
        Date dateOfLastPayment;
        if (payments.size() > 0) {
            dateOfLastPayment = payments.get(0)
                    .getDateOfPayment();
        } else {
            dateOfLastPayment = contract.getAdjustedEndDate();
        }

        //new Adjusted end date
        //Calculate date between 2 dates
        Date adjustedEndDate = contract.getAdjustedEndDate();
        Date endDate = contract.getEndOfContract();

        if (endDate.before(adjustedEndDate)) {
            return new ResponseEntity<>(Arrays.asList(),
                    HttpStatus.OK);
        } else {
            //Find the difference between End Date and AdjustedEndDate
            Calendar startCalendar = Calendar.getInstance();
            startCalendar.setTime(adjustedEndDate);
            startCalendar.add(Calendar.DAY_OF_YEAR,
                    ungivenVacations + paidVacations);

            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(endDate);

            int monthCount = 0;
            int firstDayInFirstMonth = startCalendar.get(Calendar.DAY_OF_MONTH);
            startCalendar.set(Calendar.DAY_OF_MONTH,
                    1);
            endCalendar.add(Calendar.DAY_OF_YEAR,
                    -firstDayInFirstMonth + 1);

            while (!startCalendar.after(endCalendar)) {
                startCalendar.add(Calendar.MONTH,
                        1);
                ++monthCount;
            }

            startCalendar.add(Calendar.MONTH,
                    -1);
            --monthCount;
            int remainingDays = 0;
            while (!startCalendar.after(endCalendar)) {
                startCalendar.add(Calendar.DAY_OF_YEAR,
                        1);
                ++remainingDays;
            }

            startCalendar.add(Calendar.DAY_OF_YEAR,
                    -1);
            --remainingDays;

            int lastMonthMaxDays = endCalendar.getActualMaximum(
                    Calendar.DAY_OF_MONTH);
            if (remainingDays >= lastMonthMaxDays) {
                ++monthCount;
                remainingDays -= lastMonthMaxDays;
            }

            int diffMonth = monthCount;
            int diffDay = remainingDays;

            //Calculating total payments
            double currentMonthlyPaymentfee = PaymentUtil.getCurrentMonthlyFee(
                    contract);
            double extraDaysAmount = (currentMonthlyPaymentfee / 30.4) * diffDay;
            extraDaysAmount = Math.round((extraDaysAmount) * 100.0) / 100.0;

            double paymentForVacation = PaymentUtil.VACATION_DAY_COST * paidVacations;
            paymentForVacation = Math.round((paymentForVacation) * 100.0) / 100.0;

            double totalPayments = (currentMonthlyPaymentfee * diffMonth) + extraDaysAmount + paymentForVacation;
            totalPayments = Math.round((totalPayments) * 100.0) / 100.0;

            //Building response
            Calendar lastDate = Calendar.getInstance();
            lastDate.setTime(dateOfLastPayment);
            List<PaymentResponse> allPayments = new ArrayList<PaymentResponse>();
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");

            for (int i = 0; i < diffMonth; i++) {
                lastDate.add(Calendar.MONTH,
                        1);
                String lastdateTime = df.format(lastDate.getTime());
//                String description = "Montly Payment " + lastDate.getTime()
//                        .toString();
                String description = "Montly Payment " + lastdateTime;
                PaymentResponse temp = new PaymentResponse(
                        currentMonthlyPaymentfee,
                        description);
                allPayments.add(temp);
            }
            if (extraDaysAmount > 0) {
                lastDate.add(Calendar.MONTH,
                        1);
                String lastdateTime = df.format(lastDate.getTime());

                String description = "Montly Payment " + lastdateTime;
                PaymentResponse temp = new PaymentResponse(extraDaysAmount,
                        description);
                allPayments.add(temp);
            }
            if (paymentForVacation > 0) {
                String description = "Payment for vacation not taken ";
                PaymentResponse temp = new PaymentResponse(paymentForVacation,
                        description);
                allPayments.add(temp);
            }
            PaymentInvoicesResponse helper = new PaymentInvoicesResponse(allPayments,
                    totalPayments,
                    ungivenVacations,
                    contract);
            return new ResponseEntity<>(helper,
                    HttpStatus.OK);

        }
    }

    // CM-494
    @PreAuthorize("hasPermission('Payments','sendpaymentdetails')")
    @RequestMapping(path = "/sendpaymentdetails/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> sendPaymentDetails(
            @PathVariable("id") Payment p,
            @RequestParam(value = "attachmentId") long attachmentId) {
        Attachment attachment = Setup.getRepository(AttachementRepository.class).findOne(attachmentId);
        if (attachment != null) {
            if (p.getContract().getClient().getEmail() != null
                    && !p.getContract().getClient().getEmail().isEmpty()) {
                //ACC-6103 no Email found stopped by Cm
               /* String msg = SmsFormatter.formatClientName(p.getContract().getClient())
                        +"\n"
                        + SmsFormatter.clientGreeting()
                        +"\n"
                        + " Please find the attached your payment details for your contract.";
                ClientEmailService es = Setup.getApplicationContext().getBean(ClientEmailService.class);
                es.sendEmail(p.getContract().getClient(), "Payment Details", msg, Arrays.asList(attachment));*/
            } else {
                /* //ACC-6103 no template id = 89  found stopped by Cm
                TemplatesSmsUtil templatesSmsUtil = Setup.getApplicationContext().getBean(TemplatesSmsUtil.class);
                //SmsResponse response = templatesSmsUtil.sendMessage(p.getContract().getClient(),null, TemplateSmsCode.Payment_Details.toString(),p.getPaymentReceiptParameters(attachment),null,false);
                if(p.getContract() != null && p.getContract().getClient() != null) {
                    Contract contract = p.getContract();
                    messagesUtils.sendMessageForClient(MessagesUtils.getSmsTemplate(TemplateSmsCode.Payment_Details)
                            , contract.getClient(), contract, null, p.getPaymentReceiptParameters(attachment),
                            contract.getClient() != null ? contract.getClient().getId() : null,
                            contract.getClient() != null ? contract.getClient().getEntityType() : null);
                }*/

                Client client = p.getContract().getClient();
                Setup.getApplicationContext().getBean(MessagingService.class)
                        .sendClientSms(p.getContract(),
                                TemplateUtil.getTemplate("CM_Payment_Details"),
                                p.getPaymentReceiptParameters(attachment),
                                new HashMap<>(),
                                client.getNormalizedMobileNumber(),
                                client.getNormalizedWhatsappNumber(),
                                client.getId(),
                                client.getEntityType());
            }

            return new ResponseEntity<>("Send successfuly.", HttpStatus.OK);
        }
        return new ResponseEntity<>("No attachment was found.", HttpStatus.BAD_REQUEST);
    }

    @RequestMapping("/getPaymentsStatus")
    @ResponseBody
    public ResponseEntity<?> getPaymentsStatus(@RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(PaymentStatus.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x))
                        .filter(x -> search==null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    @RequestMapping("/getPaymentsTypes")
    @ResponseBody
    public ResponseEntity<?> getPaymentsTypes(@RequestParam(required = false, defaultValue = "") String search, Pageable pageable) {
        Picklist pl = Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment");
        return  ResponseEntity.ok(search.isEmpty() ?
                picklistitemRepository.findByListOrderByNameAsc(pl, pageable) :
                picklistitemRepository.findByListAndNameLike(pl, search + "%", pageable));
    }

    @PreAuthorize("hasPermission('Payments','getUnreplacedBouncedPayments')")
    @RequestMapping(path = "/getUnreplacedBouncedPayments", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getUnreplacedBouncedPayments(@RequestParam(value = "contractId", required = false) Long contractId
            ,@RequestParam(value = "amount", required = false) Double amount)
    {
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
        List<Payment> unreplacedBouncedPayments = new ArrayList<Payment>();
        if (contract!=null)
            unreplacedBouncedPayments = paymentRepository.findUnReplacedBouncedPaymentByContractAndAmount(contract,amount);
        return new ResponseEntity<>(
                project(
                        unreplacedBouncedPayments,
                        PaymentProjection.class)
                , HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('Payments','bulkCreate')")
    @RequestMapping(value = "/bulkCreate",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> bulkCreate(@RequestBody List<Payment> payments) {
        Payment payment = payments.get(0);
        List<PaymentStatus> allowedStatusWithDirectDepit = Arrays.asList(PaymentStatus.BOUNCED,
                PaymentStatus.DELETED, PaymentStatus.PDC, PaymentStatus.RECEIVED);
        if (payment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT) && !allowedStatusWithDirectDepit.contains(payment.getStatus())) {
            throw new BusinessException(payment.getStatus() + " is not allowed with type Direct Depit");
        }

        for (Payment temp : payments) {
            super.create(temp);
        }
//        paymentRep.save(payments);
        //fix Adjusted end date
        Contract c = null;
        if (payments != null && payments.size() > 0) {
            Payment firstPayment = payments.get(0);
            if (firstPayment != null) {
                c = contractRepository.findOne(firstPayment.getContract().getId());
            }
        }
        /*if (c != null && c.getContractFeesType() != null && !c.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {
            AdjustedEndDate adjEndDate = PaymentUtil.setAdjustedEndDate(c);
            c.setAdjustedEndDate(adjEndDate.getAdjustedEndDate() != null ? adjEndDate.getAdjustedEndDate().toDate() : null);
            c.setInitialAdjustedEndDate(adjEndDate.getInitialAdjustedEndDate() != null ? adjEndDate.getInitialAdjustedEndDate().toDate() : null);
            c.setPaidEndDate(adjEndDate.getPaidEndDate() != null ? adjEndDate.getPaidEndDate().toDate() : null);
            c.setAccruedVacationDays(adjEndDate.getAccruedVacationDays());
            contractRepository.save(c);
        }*/

        for (Payment temp : payments) {
            if (temp.getTypeOfPayment()
                    .toString() == "Discount") {
                paymentService.sendNotification(temp);
            }
        }

        return new ResponseEntity<>(
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('Payments','getPaymentAttachments')")
    @RequestMapping(path = "/getPaymentAttachments/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getPaymentAttachments(
            @PathVariable("id") Contract c) {

        List<Payment> toInclude = c.getPayments()
                .stream()
                .filter(p -> p.hastaxInvoice() || p.hasPaymentReceipt())
                .collect(Collectors.toList());

        return new ResponseEntity<>(project(toInclude, PaymentAttachmentsProjection.class), HttpStatus.OK);
    }
    // End moved from CM ACC-6103


    public Long getPaymentID(Map newPayment){
        if (newPayment==null)
            return null;
        if (newPayment.get("id") == null)
            return null;
        return Long.parseLong(newPayment.get("id").toString());
    }

    //Jirra ACC-3168
    @PreAuthorize("hasPermission('payments','search')")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseEntity<?> search(@RequestBody List<FilterItem> filters) {

        SelectQuery<Payment> query = new SelectQuery(Payment.class);
        // Process Filters
        SelectFilter selectFilter = new SelectFilter();

        for (FilterItem filter : filters) {
            SelectFilter newFilter = PaymentHelper.getSelectFilter(filter);

            if (newFilter != null) {
                selectFilter.and(newFilter);
            }
        }

        //Jirra ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("dateOfPayment", ">=",
                java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));
        query.filterBy(selectFilter);
        query.sortBy("dateOfPayment", true);

        return new ResponseEntity(query.execute().stream()
                .map(payment -> projectionFactory.createProjection(PaymentSearchProjection.class, payment)).collect(
                        Collectors.toList()), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('payments','advancesearch')")
    @RequestMapping(value = "/page/advancesearch", method = RequestMethod.POST)
    public ResponseEntity<?> advanceSearch(
            Pageable pageable,
            @RequestBody List<FilterItem> filters) {

        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            SelectFilter newFilter;
            if (filter.getProperty() != null && filter.getProperty().equalsIgnoreCase("contract.housemaid.nationality.name")) {
                newFilter = new SelectFilter("contract.housemaid.nationality.name",filter.getOperation(),filter.getValue());
            } else {
                newFilter = PaymentHelper.getSelectFilter(filter);
            }

            if (newFilter != null) {
                selectFilter.and(newFilter);
            }
        }

        // ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("dateOfPayment", ">=",
                java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));
        query.filterBy(selectFilter);
        // Sorting
        if (pageable.getSort() == null
                || Iterables.isEmpty(pageable.getSort())) {
            query.sortBy("dateOfPayment", false);
        }

        PageImpl s = (PageImpl) query.execute(pageable)
                .map(obj -> projectionFactory.createProjection(PaymentProjection.class, obj));

        AccountingPage accountingPageResult =
                new AccountingPage(s.getContent(), pageable, s.getTotalElements(), 0.0, 0.0);

        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('payments','calculatePaymentsSumAndVatByFiltered')")
    @PostMapping("/calculatePaymentsSumAndVatByFiltered")
    public ResponseEntity<?>  calculatePaymentsSumAndVatByFiltered(@RequestBody List<FilterItem> filters) {

        SelectQuery<Payment> sumQuery = new SelectQuery<>(Payment.class);
        SelectQuery<Payment> vatQuery = new SelectQuery<>(Payment.class);
        SelectFilter selectFilter = new SelectFilter();
        // ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("dateOfPayment", ">=",
                java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));

        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(Payment.class));
        }

        sumQuery.filterBy(selectFilter);
        vatQuery.filterBy(selectFilter);
        return ResponseEntity.ok(new HashMap<String, Object>(){{
            put("totalSum", PaymentHelper.df_two_decimal.format(
                    (double) Math.round(new AggregateQuery(sumQuery, Aggregate.Sum, "amountOfPayment").execute().doubleValue() * 100) / 100));
            put("totalVat", PaymentHelper.df_two_decimal.format(
                    (double) Math.round(new AggregateQuery(vatQuery, Aggregate.Sum, "vat").execute().doubleValue() * 100) / 100));
        }});
    }

    @PreAuthorize("hasPermission('payments','changepaymentstatus')")
    @RequestMapping(value = "/changepaymentstatus", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> changePaymentsStatus(@RequestBody ChangePaymentsStatusRequest request) {
        Long canUpdatePayments = 0L, cannotUpdatePayments = 0L;
        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "Payment");
        query.filterBy("status", "IN", Arrays.asList(BackgroundTaskStatus.Pending, BackgroundTaskStatus.Running));
        List<BackgroundTask> result = query.execute();
        if (result == null || result.isEmpty()) {
            List<Payment> payments = paymentRepository.findAll(request.getPaymentsIds());
            Long now = new Date().getTime();
            for (Payment payment : payments) {

                //Jirra ACC-2282
                if (paymentService.canUpdatePayment(null, payment, request.getStatus())) {
                    canUpdatePayments++;
                    backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                            "PaymentChangeStatus:" + now,
                            "paymentController",
                            "accounting",
                            "changePaymentStatus",
                            "Payment",
                            payment.getId(),
                            true,
                            false,
                            new Class<?>[]{Long.class, Boolean.class, String.class},
                            new Object[]{payment.getId(), request.getChequeWithTheBank(), request.getStatus().getValue()});
                } else {
                    cannotUpdatePayments++;
                }
            }
        } else {
            throw new RuntimeException("This feature is requested before and still processing.");
        }
        return new ResponseEntity("start processing,\n Total Payments: " + request.getPaymentsIds().size() + "\n Payments would be updated: "
                + canUpdatePayments + ", Payments wouldn't be updated due to Lack of Permission: " + cannotUpdatePayments, HttpStatus.OK);
    }

    // ACC-2113
    @Transactional
    public void changePaymentStatus(Long paymentId, Boolean chequeWithTheBank, String paymentStatusAsString) throws Exception {
        if (chequeWithTheBank == null && (paymentStatusAsString == null || paymentStatusAsString.isEmpty()))
            throw new RuntimeException("at least one of (chequeWithTheBank, paymentStatus) must be specified");

        Payment p = paymentRepository.findOne(paymentId);

        // ACC-355
        if (chequeWithTheBank != null) {
            p.setChequeWithTheBank(chequeWithTheBank);
        }

        PaymentStatus paymentStatus = PaymentStatus.valueOf(paymentStatusAsString);
        if (paymentStatus != null) {
            p.setStatus(paymentStatus);
        }

        paymentService.forceUpdatePayment(p);
        logger.info("payment update with id: " + paymentId);
    }

    @PreAuthorize("hasPermission('payments','advancesearchcsv')")
    @RequestMapping(value = "/csv/advancesearch", method = RequestMethod.POST)
    public void advanceSearchCsv(
            Sort sort, @RequestBody List<FilterItem> filters,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {

        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            SelectFilter newFilter = PaymentHelper.getSelectFilter(filter);

            if (newFilter != null) {
                selectFilter.and(newFilter);
            }
        }

        //Jirra ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("dateOfPayment", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));

        query.filterBy(selectFilter);
        // Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("dateOfPayment", false);
        }
        //Jirra ACC-1099
        if (limit == null || limit > csvMaxLimit) {
            limit = csvMaxLimit;
        }

        InputStream is = null;
        try {
            String[] namesOrdared = {"contractName", "paymentName", "clientName", "contractType", "contractProspectType",
                    "contractStartDate", "typeOfPayment", "methodOfPayment", "amountOfPayment", "dateOfPayment", "vatPaidByClient",
                    "bankName", "chequeName", "chequeNumber", "status", "creationDate", "chequeWithTheBank",
                    "replaced", "applicationId", "note", "isInitial", "vat", "dateChangedToPDP", "dateChangedToReceived"};
            is = generateCsv(query, PaymentCsvProjection.class, namesOrdared, limit);
            createDownloadResponse(response, "Payments.csv", is);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('payments','advanceSearchNEW')")
    @PostMapping(value = "/csv/advanceSearchNEW")
    public void advanceSearchNEW(
            Sort sort,
            @RequestBody Map<String, Object> body,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) throws IOException {

        List<LinkedHashMap> filtersHashMap = (List<LinkedHashMap>) body.get("filters");
        List<FilterItem> filters = new ArrayList<>();
        Utils utils = Setup.getApplicationContext().getBean(Utils.class);
        for (LinkedHashMap hashMap : filtersHashMap) {
            FilterItem cp = utils.readObjectFromLinkedHasMap(hashMap, FilterItem.class);
            filters.add(cp);
        }

        List<String> columns = (List<String>) body.get("columns");
        List<String> headers = (List<String>) body.get("headers");

        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            SelectFilter newFilter;
            if (filter.getProperty() != null && filter.getProperty().equalsIgnoreCase("contract.housemaid.nationality.name")) {
                newFilter = new SelectFilter("contract.housemaid.nationality.name",filter.getOperation(),filter.getValue());
            } else {
                newFilter = PaymentHelper.getSelectFilter(filter);
            }

            if (newFilter != null) {
                selectFilter.and(newFilter);
            }
        }

        //Jirra ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("dateOfPayment", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));

        query.filterBy(selectFilter);
        // Sorting
        if (sort != null && !sort.isEmpty()) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("dateOfPayment", false);
        }
        //Jirra ACC-1099
        if (limit == null || limit > csvMaxLimit) {
            limit = csvMaxLimit;
        }

        InputStream is = null;
        try {
            Page<Payment> p = query.execute(PageRequest.of(0, limit));
            if (p.getContent().size() > 0) {
                is = new FileInputStream(
                        CsvHelper.generateCsv(p.getContent(), PaymentCsvProjection.class, headers.toArray(new String[0]), columns.toArray(new String[0]), "Payments.csv"));

            }
            createDownloadResponse(response, "Payments.csv", is);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }


    //Jirra ACC-1144
    @PreAuthorize("hasPermission('payments','advancesearchcsv')")
    @RequestMapping(value = "/csv/advancesearch/mail",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> advanceSearchCsv(
            Sort sort, @RequestBody List<FilterItem> filters,
            @RequestParam(name = "pageName", required = false) String pageName,
            @RequestParam(name = "limit", required = false) Integer limit) {

        //Jirra ACC-1099
        int csvMailMaxLimit = getCsvMailMaxLimit();
        if (limit == null || limit > csvMailMaxLimit) {
            limit = csvMailMaxLimit;
        }

        String[] namesOrdered = {"contractName", "paymentName", "clientName", "contractType", "contractProspectType",
                "contractStartDate", "typeOfPayment", "methodOfPayment", "amountOfPayment", "dateOfPayment", "vatPaidByClient",
                "bankName", "chequeName", "chequeNumber", "status", "creationDate", "chequeWithTheBank",
                "replaced", "applicationId", "note", "isInitial", "vat", "dateChangedToPDP", "dateChangedToReceived"};
        String[] headers = {"Contract Name", "Payment Name", "Client Name", "Contract Type", "Contract Prospect Type",
                "Contract Start Date", "Type Of Payment", "Method Of Payment", "Amount Of Payment", "Date Of Payment", "Vat Paid By Client",
                "Bank Name", "Cheque Name", "Cheque Number", "Status", "Creation Date", "Cheque With The Bank",
                "Replaced", "Application Id", "Note", "Is Initial", "Vat", "Date Changed to PDP", "Date Changed to Received"};

        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            SelectFilter newFilter = PaymentHelper.getSelectFilter(filter);

            if (newFilter != null) {
                selectFilter.and(newFilter);
            }
        }

        SelectQuery<Payment> query = new SelectQuery(Payment.class);
        query.filterBy(selectFilter);

        query.filterBy(new SelectFilter("dateOfPayment", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));

        // Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("dateOfPayment", false);
        }

        if (limit != null) {
            query.setLimit(limit);
        }

        String emails = CurrentRequest.getUser().getEmail();

        try {
            emailHelper.generateCSVFileAndSendViaMail(emails, pageName, "PAYMENT_REPORT_MAIL",
                    headers, namesOrdered,
                    PaymentCsvProjection.class,
                    pageName, query);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
        }

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    //Jirra ACC-1144
    @PreAuthorize("hasPermission('payments','advancesearchcsv')")
    @RequestMapping(value = "/csv/advancesearchNEW/mail",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> advanceSearchCsvNew(
            Sort sort, @RequestBody Map<String, Object> body,
            @RequestParam(name = "pageName", required = false) String pageName,
            @RequestParam(name = "limit", required = false) Integer limit) throws IOException {

        List<LinkedHashMap> filtersHashMap = (List<LinkedHashMap>) body.get("filters");
        List<FilterItem> filters = new ArrayList<>();
        Utils utils = Setup.getApplicationContext().getBean(Utils.class);
        for (LinkedHashMap hashMap : filtersHashMap) {
            FilterItem cp = utils.readObjectFromLinkedHasMap(hashMap, FilterItem.class);
            filters.add(cp);
        }
        List<String> columns = (List<String>) body.get("columns");
        List<String> headers = (List<String>) body.get("headers");

        //Jirra ACC-1099
        int csvMailMaxLimit = getCsvMailMaxLimit();
        if (limit == null || limit > csvMailMaxLimit) {
            limit = csvMailMaxLimit;
        }

        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            SelectFilter newFilter = PaymentHelper.getSelectFilter(filter);

            if (newFilter != null) {
                selectFilter.and(newFilter);
            }
        }

        SelectQuery<Payment> query = new SelectQuery(Payment.class);
        query.filterBy(selectFilter);

        query.filterBy(new SelectFilter("dateOfPayment", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE)));

        // Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("dateOfPayment", false);
        }

        if (limit != null) {
            query.setLimit(limit);
        }

        String emails = CurrentRequest.getUser().getEmail();

        try {
            emailHelper.generateCSVFileAndSendViaMail(emails, pageName, "PAYMENT_REPORT_MAIL",
                    headers.toArray(new String[0]), columns.toArray(new String[0]),
                    PaymentCsvProjection.class,
                    pageName, query);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
        }

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    //Jirra ACC-1435
    @PreAuthorize("hasPermission('payments','getPaymentInfoForReplacement')")
    @RequestMapping(value = "/getpaymentinfoforreplacement/{payment_id}", method = RequestMethod.GET)
    public ResponseEntity<?> getPaymentInfoForReplacement(@PathVariable("payment_id") Payment payment) {
        Map paymentProjection = new HashMap();
        paymentProjection.put("id", payment.getId());
        paymentProjection.put("dateOfPayment", payment.getDateOfPayment());
        paymentProjection.put("amountOfPayment", payment.getAmountOfPayment());
        paymentProjection.put("typeOfPayment", payment.getTypeOfPayment());
        if (!payment.getVatPaidByClient()) {
            paymentProjection.put("amountOfPayment", Math.ceil(DiscountsWithVatHelper.getAmountPlusVat((double) paymentProjection.get("amountOfPayment"))));
        }
        return new ResponseEntity(paymentProjection, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('payments','getAllPaymentMethods')")
    @RequestMapping(value = "/getallpaymentmethods")
    public PaymentMethod[] getAllPaymentMethods() {
        return PaymentMethod.class.getEnumConstants();
    }

    @PreAuthorize("hasPermission('payments','unPausePayment')")
    @RequestMapping(value = "/unPausePayment/{payment_id}")
    @Transactional
    public ResponseEntity unPausePayment(
            @PathVariable("payment_id") Payment payment) {

        unPausePaymentMethod(payment);

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @Transactional
    public void unPausePaymentMethod(Payment payment) {

        payment.setBouncedFlowPausedForReplacement(Boolean.FALSE);

        //Jirra CMA-622
        if (payment.getBouncedAgainDuringPausingPeriod() != null && payment.getBouncedAgainDuringPausingPeriod() &&
                (payment.getReplaced() == null || !payment.getReplaced())) {
            payment = bankStatementFileService.processDirectDebitBouncedAgainPayment(payment);
            payment.setBouncedAgainDuringPausingPeriod(Boolean.FALSE);
        }

        paymentService.forceUpdatePayment(payment);

        DirectDebit directDebit = null;
        if (payment.getDirectDebit() != null) {
            directDebit = payment.getDirectDebit();
        } else if (payment.getDirectDebitFile() != null && payment.getDirectDebitFile().getDirectDebit() != null) {
            directDebit = payment.getDirectDebitFile().getDirectDebit();
        }

        if (directDebit != null) {

            logger.log(Level.SEVERE, "ExpextedWireTransferForReplacementJob directDebit:" + directDebit.getId());

            DirectDebitRejectionToDo rejectionTodo = directDebit.getDirectDebitBouncingRejectionToDo();
            if (rejectionTodo != null && !rejectionTodo.isCompleted() && !rejectionTodo.isStopped()) {

                logger.log(Level.SEVERE, "ExpextedWireTransferForReplacementJob rejectionTodo:" + rejectionTodo.getId());

                List<String> currentTasks = rejectionTodo.getCurrentTasks();
                if (!currentTasks.isEmpty()) {
                    DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                    if (step == DirectDebitRejectionToDoType.WAITING_FLOW_PAUSE_B_BOUNCED) {
                        rejectionTodo.setLastDirectDebit(directDebit);
                        DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep pausedFlowDoneStep = Setup.getApplicationContext().getBean(DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep.class);
                        pausedFlowDoneStep.onDone(rejectionTodo);
                    }
                }
            }
        }
    }

    //ACC-8743
    @UsedBy(others = UsedBy.Others.New_GPT)
    @JwtSecured
    @GetMapping("/getPaymentsForGPT")
    public ResponseEntity<?> getPaymentsForGPT(@RequestParam("contractId") Contract contract) {
        return ResponseEntity.ok(queryService.getPaymentsForGPT(contract));
    }

    @PreAuthorize("hasPermission('clients','pdcByContract')")
    @RequestMapping(value = "/pdcByContract/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> getPDCPaymentByContract(@PathVariable("id") Contract contract) {

        List<PaymentStatus> statuses = new ArrayList<>();

        statuses.add(PaymentStatus.PDC);
        statuses.add(PaymentStatus.BOUNCED);

        List<Payment> byContractAndStatus = paymentRepository.findByContractAndStatusIn(contract, statuses);

        return new ResponseEntity(byContractAndStatus.stream().map(dd -> projectionFactory.createProjection(
                PaymentProjection.class, dd)).collect(Collectors.toList()), HttpStatus.OK);
    }

    //CMA-2728
    @Deprecated
    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @GetMapping(value = "/ccPaymentsSectionData/{contractId}")
    public ResponseEntity<?> ccPaymentsSectionData(@PathVariable("contractId") Contract contract) {

        return new ResponseEntity(ccAppService.ccPaymentsSectionData(contract, false), HttpStatus.OK);
    }

    // CC APP ACC-7295
    @UsedBy(others = UsedBy.Others.CC_App)
    @JwtSecured
    @GetMapping("/downloadAllTaxInvoicesAcc8030/{contract}/{limit}")
    public ResponseEntity<?> downloadAllTaxInvoicesAcc8030(
            @PathVariable(value = "contract") Long contract,
            @PathVariable(value = "limit") Integer limit,
            HttpServletResponse response) {

        List<InputStream> files = new ArrayList<>();
        List<Attachment> attachments = paymentRepository.getAllTaxInvoices(contract, limit > 0 ? limit : Integer.MAX_VALUE)
                .stream().map(e -> {
                    Map<String, Object> m = new HashMap<>();
                    m.put("id", e[0]);
                    m.put("version", e[1]);
                    m.put("creationDate", e[2]);
                    m.put("creator", e[3]);
                    m.put("uuid", e[4]);
                    m.put("entityType", e[5]);
                    m.put("attachmentsCount", e[6]);
                    m.put("name", e[7]);
                    m.put("size", e[8]);
                    m.put("extension", e[9]);
                    m.put("path", e[10]);
                    m.put("tag", e[11]);
                    m.put("ownerType", e[12]);
                    m.put("ownerId", e[13]);
                    m.put("uniqueTag", e[14]);
                    m.put("amazon", e[15]);
                    m.put("keepOriginalSize", e[16]);
                    ObjectMapper mapper = new ObjectMapper();
                    logger.info("attachment entity: " + e);
                    return mapper.convertValue(m, Attachment.class);
                }).collect(Collectors.toList());

        attachments.forEach(a -> {
            try {
                files.add(Storage.getStream(a));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        if (files.isEmpty()) return notFoundResponse();
        logger.info("files size: " + files.size());

        try {
            File res = PdfHelper.mergePdfStreams(files, "Tax invoices.pdf");
            createDownloadResponse(response, "Tax invoices.pdf", new FileInputStream(res));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return okResponse();
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @GetMapping(value = "/ccPaymentsHistory/{contractId}")
    public ResponseEntity<?> ccPaymentsHistory(@PathVariable("contractId") Contract contract) {

        Setup.getApplicationContext().getBean(CCAppService.class).insertTrackingLog(contract.getUuid(), CcAppAction.VIEW_PAYMENT_HISTORY);

        return new ResponseEntity(ccAppService.ccPaymentsHistory(contract), HttpStatus.OK);
    }

    @Deprecated
    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @GetMapping(value = "/ccPaymentsSectionButtonVisibility/{id}/{template}/{button}")
    public ResponseEntity<?> ccPaymentsSectionButtonVisibility(
            @PathVariable(name = "id") Contract contract,
            @PathVariable(name = "template") CcAppCmsTemplate template,
            @PathVariable(name = "button") CcPaymentSectionButton button) {

        return new ResponseEntity(ccAppContentService.resolveButtonVisibility(template, button, contract, new HashMap<>()), HttpStatus.OK);
    }

    // ACC-6158 CMA-3872
    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @GetMapping(value = "/ccPaymentsSectionDataCma3872/{contractId}")
    public ResponseEntity<?> ccPaymentsSectionDataCma3872(@PathVariable("contractId") Contract contract) {

        return ResponseEntity.ok(ccAppContentService.getPaymentSectionData(contract));
    }

    @PreAuthorize("hasPermission('clients','getPaymentsInfo')")
    @RequestMapping(value = "/getPaymentsInfo",
            method = RequestMethod.POST)
    public ResponseEntity<?> getPaymentsInfo(@RequestBody Map<String, Object> body) throws ParseException {

        PaymentTermConfig paymentTermConfig = getObjectMapper().convertValue(body.get("paymentTermConfig"), PaymentTermConfig.class);
        List<Object>  paymentTypeConfigsObjects = (List<Object>) body.get("paymentTypeConfigs");
        List<AbstractPaymentTypeConfig>  paymentTypeConfigs = paymentTypeConfigsObjects.stream()
               .map(o -> getObjectMapper().convertValue(o, PaymentTypeConfig.class)).collect(Collectors.toList());

        Integer additionalDiscountMonths = (Integer) body.get("additionalDiscountMonths");
        Integer paymentsDuration = (Integer) body.get("paymentsDuration");
        Double additionalDiscount = (Double) body.get("additionalDiscount");
        Double workerSalary = (Double) body.get("workerSalary");
        Boolean isProrated = (Boolean) body.get("isProrated");
        Boolean isProratedPlusMonth = (Boolean) body.get("isProratedPlusMonth");
        boolean consumePaidPayments = body.containsKey("consumePaidPayments") && body.get("consumePaidPayments") != null ?
                (Boolean) body.get("consumePaidPayments") : false;
        java.util.Date contractStartDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .parse((body.get("contractStartDate").toString()));
        Integer waivedMonths = body.containsKey("waivedMonths") && (Integer) body.get("waivedMonths") != null ?
                (Integer) body.get("waivedMonths") : 0;

        Double firstMonthPayment = (Double) body.get("firstMonthPayment");
        DateTime contractStartDateTime = new DateTime(contractStartDate);

        if (paymentTermConfig == null || paymentTypeConfigs == null
                || paymentsDuration == null || isProratedPlusMonth == null
                || isProrated == null || contractStartDateTime ==null)
            return new ResponseEntity("Wrong Data", HttpStatus.BAD_REQUEST);

        boolean isMaidVisa = paymentTermConfig.getContractProspectType().getCode()
                .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));
        if (isMaidVisa && workerSalary == null)
            return new ResponseEntity("Wrong Data", HttpStatus.BAD_REQUEST);

        if (waivedMonths > 0 && (!isMaidVisa || isProrated)) {
            return new ResponseEntity("Waived payments are supported only for non-prorated MV contract", HttpStatus.BAD_REQUEST);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("contractStartDate", new DateTime(contractStartDate));
        map.put("isWeeklyPkg", paymentTermConfig.getWeeklyAmount() > 0.0);
        map.put("isProrated", isProrated);
        map.put("isProratedPlusMonth", isProratedPlusMonth);
        map.put("consumePaidPayments", consumePaidPayments);
        map.put("isOneMonthAgreement", body.get("isOneMonthAgreement"));
        if (body.containsKey("contractId") && body.get("contractId") != null) {
            map.put("contractId", Long.valueOf((Integer) body.get("contractId")));
            map.put("isOneMonthAgreement", (boolean) map.get("isOneMonthAgreement") &&
                    !Setup.getRepository(PaymentRepository.class).existsMonthlyPaymentReceived((Long) map.get("contractId"), true));
        }
        map.put("firstMonthPayment", firstMonthPayment);
        map.put("additionalDiscountMonths", additionalDiscountMonths);
        map.put("paymentsDuration", paymentsDuration);
        map.put("additionalDiscount", additionalDiscount);
        map.put("workerSalary", workerSalary);
        map.put("dailyRateAmount", paymentTermConfig.getDailyRateAmount());
        map.put("isMaidVisa", isMaidVisa);
        map.put("ptc", paymentTermConfig);
        map.put("contractPaymentTypes", paymentTypeConfigs);
        map.put("waivedMonths", waivedMonths);
        AbstractPaymentTypeConfig monthlyPaymentType = paymentTypeConfigs.stream().filter(
                        item -> item.getType().getCode().equals(PaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                .findFirst().orElse(null);
        map.put("monthlyPaymentType", monthlyPaymentType);
        map.put("periodicalAdditionalDiscount", paymentTermConfig.getPeriodicalAdditionalDiscount());
        if (monthlyPaymentType != null) {
            map.put("affectedByAdditionalDiscount", monthlyPaymentType.getAffectedByAdditionalDiscount());
            map.put("monthlyPaymentDiscount", monthlyPaymentType.getDiscount());
            map.put("monthlyPaymentDiscountEffectiveAfter", monthlyPaymentType.getDiscountEffectiveAfter());
            map.put("monthlyPaymentAmount", monthlyPaymentType.getAmount());
        }

        // ACC-7151
        map.put("creditNoteAmount", body.containsKey("creditNoteAmount") &&
                body.get("creditNoteAmount") != null && (Double) body.get("creditNoteAmount") > 0.0 ?
                body.get("creditNoteAmount") : 0.0);
        map.put("creditNoteMonths", body.containsKey("creditNoteMonths") &&
                body.get("creditNoteMonths") != null && (Integer) body.get("creditNoteMonths") > 0 ?
                body.get("creditNoteMonths") : 0);

        // ACC-8721
        // If (contractId == null) => New Contract
        boolean isWorkerSalaryVatted = !map.containsKey("contractId") || Setup.getRepository(ContractRepository.class)
                .findOne((Long) map.get("contractId"))
                .isWorkerSalaryVatted();

        map.put("isWorkerSalaryVatted", isWorkerSalaryVatted);

        logger.info("creditNoteAmount: " + map.get("creditNoteAmount") +
                "; creditNoteMonths: " + map.get("creditNoteMonths") +
                "; isWorkerSalaryVatted" + isWorkerSalaryVatted);

        return new ResponseEntity(PaymentReceiptHelper.generatePaymentTermForms(map), HttpStatus.OK);
    }

    @Transactional
    @PreAuthorize("hasPermission('payments','initializePaymentTypeWithRevenueCodes')")
    @RequestMapping(value = "/initializePaymentTypeWithRevenueCodes", method = RequestMethod.GET)
    public ResponseEntity<?> initializePaymentTypeWithRevenueCodes() {
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        List<PicklistItem> typeOfPayments = picklistItemRepository.findByListOrderByNameAsc(Setup.getRepository(PicklistRepository.class)
                .findByCode("TypeOfPayment"));

        PicklistItem matchingFee = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("matching_fee")).findFirst().orElse(null);
        PicklistItem monthlyPayment = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("monthly_payment")).findFirst().orElse(null);
        PicklistItem overstayFee = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("overstay_fee")).findFirst().orElse(null);
        PicklistItem sameDayRecruitmentFee = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("same_day_recruitment_fee")).findFirst().orElse(null);
        PicklistItem serviceCharge = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("service_charge")).findFirst().orElse(null);
        PicklistItem tadbeerFees = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("tadbeer_fees")).findFirst().orElse(null);
        PicklistItem ccToMvContract = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("cc_to_mv_contract")).findFirst().orElse(null);
        PicklistItem urgentVisaCharges = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode().
                equalsIgnoreCase("urgent_visa_charges")).findFirst().orElse(null);
        PicklistItem accommodationFee = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("accommodation_fee")).findFirst().orElse(null);
        PicklistItem pcrWithoutVat = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("pcr_without_Vat")).findFirst().orElse(null);
        PicklistItem employingExDh = typeOfPayments.stream().filter(picklistItem -> picklistItem.getCode()
                .equalsIgnoreCase("employing_ex_dh")).findFirst().orElse(null);


        //Mv Clients
        pickListItemAddTag(matchingFee.getId(), "mv_wire_transfer_revenue", "FTR 01");
        pickListItemAddTag(monthlyPayment.getId(), "mv_wire_transfer_revenue", "MVR 02");
        pickListItemAddTag(overstayFee.getId(), "mv_wire_transfer_revenue", "MVR 04");
        pickListItemAddTag(sameDayRecruitmentFee.getId(), "mv_wire_transfer_revenue", "FTR 07");
        pickListItemAddTag(serviceCharge.getId(), "mv_wire_transfer_revenue", "MVR 06");
        pickListItemAddTag(tadbeerFees.getId(), "mv_wire_transfer_revenue", "TDR 13");
        pickListItemAddTag(ccToMvContract.getId(), "mv_wire_transfer_revenue", "FTR 01");
        pickListItemAddTag(urgentVisaCharges.getId(), "mv_wire_transfer_revenue", "FTR 07");

        pickListItemAddTag(monthlyPayment.getId(), "mv_wire_transfer_revenue_initial", "MVR 01");


        //CC Clients
        pickListItemAddTag(accommodationFee.getId(), "cc_wire_transfer_revenue", "FTR 01");
        pickListItemAddTag(ccToMvContract.getId(), "cc_wire_transfer_revenue", "FTR 01");
        pickListItemAddTag(monthlyPayment.getId(), "cc_wire_transfer_revenue", "FTR 02");
        pickListItemAddTag(pcrWithoutVat.getId(), "cc_wire_transfer_revenue", "FTR 02");
        pickListItemAddTag(sameDayRecruitmentFee.getId(), "cc_wire_transfer_revenue", "FTR 06");
        pickListItemAddTag(serviceCharge.getId(), "cc_wire_transfer_revenue", "FTR 02");
        pickListItemAddTag(employingExDh.getId(), "cc_wire_transfer_revenue", "FTR 01");


        return new ResponseEntity("Done", HttpStatus.OK);
    }

    private void pickListItemAddTag(Long pklId, String key, Object value) {
        Map<String, Object> tag = new HashMap<>();
        tag.put("key", key);
        tag.put("value", value);

        interModuleConnector.postJsonAsync("/admin/picklistItem/addComplexTag/" + pklId, tag);
    }

    @Transactional
    @PreAuthorize("hasPermission('payments','linkRefundTypesWithPaymentTypes')")
    @GetMapping(value = "/linkRefundTypesWithPaymentTypes")
    public ResponseEntity<?> linkRefundTypesWithPaymentTypes() {

        SelectQuery<PicklistItem> q = new SelectQuery<>(PicklistItem.class);
        q.filterBy("list.code", "=", "TypeOfPayment");
        q.filterBy("code", "in", Arrays.asList(
                "paid_the_client_-_refund", // #2
                "non-mp-refund",// #3
                "partial_mp_refunded_to_client",// #4
                "refund_due_to_pcr_test_delay",// #5
                "mv_salary_refund",// #6
                "refund_for_days_with_no_service",// #7
                "overstay_fees_refund", // #8
                "maidVisa_recruitment_fee_refund", // #9
                "vat_only_refund", // #10
                "accommodation_fees_refund", // #11
                "cc_to_mv_contract_refund", // #12
                "discount_refund", // #13
                "filipina_salary_adjustment_refund", // #14
                "flight_ticket_refund", // #15
                "insurance_refund", // #16
                "matching_fee_refund", // #17
                "pcr_test_refund", // #18
                "private_sponsorship_fees_refund", // #19
                "private_sponsorship_visa_cancellation_refund", // #20
                "service_charge_refund", // #21
                "tadbeer_fees_refund", // #22
                "transfer_to_private_sponsorship_refund", // #23
                "travel_assist_refund" // #24
        ));

        List<PicklistItem> l = q.execute();

        for (PicklistItem p : l) {
            if (p.hasTag("refund_for")) continue;

            String paymentTypeCode = null;
            switch (p.getCode()){
                case "paid_the_client_-_refund":// #2
                case "non-mp-refund":// #3
                case "partial_mp_refunded_to_client":// #4
                case "refund_due_to_pcr_test_delay":// #5
                case "mv_salary_refund":// #6
                case "refund_for_days_with_no_service":// #7
                    paymentTypeCode = "monthly_payment";
                    break;
                case "overstay_fees_refund":// #8
                    paymentTypeCode = "overstay_fee";
                    break;
                case "maidVisa_recruitment_fee_refund":// #9
                    paymentTypeCode = "same_day_recruitment_fee";
                    break;
                case "vat_only_refund":// #10
                    paymentTypeCode = "vat_only";
                    break;
                case "accommodation_fees_refund": // #11
                    paymentTypeCode = "accommodation_fee";
                    break;
                case "cc_to_mv_contract_refund": // #12
                    paymentTypeCode = "CC_to_MV_contract";
                    break;
                case "discount_refund": // #13
                    paymentTypeCode = "discount";
                    break;
                case "filipina_salary_adjustment_refund": // #14
                    paymentTypeCode = "Filipina_Salary_Adjustment";
                    break;
                case "flight_ticket_refund": // #15
                    paymentTypeCode = "Flight_Ticket";
                    break;
                case "insurance_refund": // #16
                    paymentTypeCode = "insurance";
                    break;
                case "matching_fee_refund": // #17
                    paymentTypeCode = "matching_fee";
                    break;
                case "pcr_test_refund": // #18
                    paymentTypeCode = "PCR_without_Vat";
                    break;
                case "private_sponsorship_fees_refund": // #19
                    paymentTypeCode = "Private_Sponsorship_fees";
                    break;
                case "private_sponsorship_visa_cancellation_refund": // #20
                    paymentTypeCode = "Private_sponsorship_visa_cancelation";
                    break;
                case "service_charge_refund": // #21
                    paymentTypeCode = "service_charge";
                    break;
                case "tadbeer_fees_refund": // #22
                    paymentTypeCode = "tadbeer_fees";
                    break;
                case "transfer_to_private_sponsorship_refund": // #23
                    paymentTypeCode = "employing_ex_dh";
                    break;
                case "travel_assist_refund": // #24
                    paymentTypeCode = "travel_assist";
                    break;
            }

            if (paymentTypeCode == null) continue;
            pickListItemAddTag(p.getId(), "refund_for", paymentTypeCode);
        }

        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        Picklist list = Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment");

        if (picklistItemRepository.findByListAndCodeIgnoreCase(
                list, "urgent_visa_charges_refund") != null) return ResponseEntity.ok("Done");

        { // #25
            PicklistItem p = new PicklistItem();
            p.setList(list);
            p.setCode("urgent_visa_charges_refund");
            p.setName("Urgent Visa Charges Refund");
            p = picklistItemRepository.save(p);
            pickListItemAddNormalTag(p.getId(), "refund");
            pickListItemAddTag(p.getId(), "refund_for", "Urgent_visa_charges");
        }

        { // #26
            PicklistItem p = new PicklistItem();
            p.setList(list);
            p.setCode("transfer_fee_refund");
            p.setName("Transfer Fee Refund");
            p = picklistItemRepository.save(p);
            pickListItemAddNormalTag(p.getId(), "refund");
            pickListItemAddTag(p.getId(), "refund_for", "transfer_fee");
        }

        { // #27
            PicklistItem p = new PicklistItem();
            p.setList(list);
            p.setCode("upgrading_nationality_refund");
            p.setName("Upgrading Nationality Refund");
            p = picklistItemRepository.save(p);
            pickListItemAddNormalTag(p.getId(), "refund");
            pickListItemAddTag(p.getId(), "refund_for", "upgrading_nationality");
        }

        { // #28
            PicklistItem p = new PicklistItem();
            p.setList(list);
            p.setCode("monthly_payment_add_on_refund");
            p.setName("Monthly Payment Add-On Refund");
            p = picklistItemRepository.save(p);
            pickListItemAddNormalTag(p.getId(), "refund");
            pickListItemAddTag(p.getId(), "refund_for", "monthly_payment_add_on");
        }

        return ResponseEntity.ok("Done");
    }

    private void pickListItemAddNormalTag(Long pklId, String name) {

        interModuleConnector.postJsonAsync("/admin/picklistItem/addTags/" + pklId, Collections.singletonList(name));
    }

    // ACC-8896
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT)
    @PreAuthorize("hasPermission('payments','getPaymentsReportGPT')")
    @GetMapping("/getPaymentsReportGPT")
    public ResponseEntity<?> getPaymentsReportGPT(
            @RequestParam(required = false) String mobileNumber,
            @RequestParam(required = false) String eidNumber,
            @RequestParam(required = false) String firstName,
            @RequestParam(required = false) String middleName,
            @RequestParam(required = false) String lastName) {
        return ResponseEntity.ok(paymentService.getPaymentsReportGPT(mobileNumber, eidNumber, firstName, middleName, lastName));
    }

    @PreAuthorize("hasPermission('payments','getAllPaymentOfContractPaidByCard')")
    @GetMapping(value = "/getAllPaymentOfContractPaidByCard/{contractId}")
    public ResponseEntity<?> getAllPaymentOfContractPaidByCard(
            @PathVariable("contractId") Contract contract) {

        List<Object[]> list = paymentRepository.findAllCardPaymentsByContract(contract.getId());

        return ResponseEntity.ok(list.stream()
                .map(o -> new HashMap<String, Object>() {{
                    put("paymentId", o[0]);
                    put("methodOfPayment", "Credit Card");
                    put("status", "Received");
                    put("paymentDate", new LocalDate(o[1]).toString("dd-MM-yyyy"));
                    put("amount", ((Double)o[2]).intValue());
                    put("reference", o[3]);
                }}).collect(Collectors.toList()));
    }

    @PreAuthorize("hasPermission('payments','getLastPaymentPaidByPurpose')")
    @GetMapping(value = "/getLastPaymentPaidByPurpose/{contractId}/{purposeId}")
    public ResponseEntity<?> getLastPaymentPaidByPurpose(
            @PathVariable("contractId") Contract contract, @PathVariable("purposeId") PaymentRequestPurpose purpose) {

        Map<String, Object> response = new HashMap<>();
        response.put("methodOfPayment", ExpensePaymentMethod.BANK_TRANSFER);
        Payment p = null;
        if (purpose.getTypeOfPayment() != null && purpose.getTypeOfPayment().hasTag("refund_for")) {
            List<Payment> payments = paymentRepository.findLastPaymentPaidByContractAndPaymentType(
                    contract.getId(), purpose.getTypeOfPayment().getTagValue("refund_for").getValue());
            if (!payments.isEmpty()) {
                p = payments.get(0);
            }
        }

        if (p == null) {
            List<Payment> l = paymentRepository.findLastPaymentPaidByContractAndPaymentType(contract.getId(), "monthly_payment");
            if (l.isEmpty()) return ResponseEntity.ok(response);
            p = l.get(0);
        }

        if (p.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)) return ResponseEntity.ok(response);

        response.put("paymentId", p.getId());
        response.put("method", p.getMethodOfPayment());
        response.put("status", p.getStatus());
        response.put("paymentDate", p.getDateOfPayment());
        response.put("amount", p.getAmountOfPayment());
        response.put("methodOfPayment", ExpensePaymentMethod.CREDIT_CARD);

        return ResponseEntity.ok(response);
    }

    // CC APP ACC-7295
    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PostMapping("/downloadAllTaxInvoices")
    public ResponseEntity<?> downloadAllTaxInvoices(@RequestBody List<String> attachmentIds, HttpServletResponse response) {
        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        List<InputStream> files = new ArrayList<>();

        attachmentIds.forEach(uuid -> {
            try {
                Attachment a = attachementRepository.findByUuid(uuid);
                if (a == null) return;
                files.add(Storage.getStream(a));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        if (files.isEmpty()) return notFoundResponse();

        File res;
        try {
            res = PdfHelper.mergePdfStreams(files, "Tax invoices.pdf");
            createDownloadResponse(response, "Tax invoices.pdf", new FileInputStream(res));
        } catch (IOException e) {
            e.printStackTrace();
        }

        return okResponse();
    }

    // ACC-7093
    @PreAuthorize("hasPermission('payments','getPaymentDiscountInfo')")
    @GetMapping(value = "/getPaymentDiscountInfo/{id}/{paymentType}")
    public ResponseEntity<?> getPaymentDiscountInfo(
            @PathVariable("id") Contract contract,
            @PathVariable("paymentType") PicklistItem paymentType,
            @RequestParam(name = "date", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(name = "isProRated", required = false, defaultValue = "false") boolean isProRated,
            @RequestParam(name = "proRatedAdded", required = false, defaultValue = "false") boolean proRatedAdded) {
        return ResponseEntity.ok(calculateDiscountsWithVatService.getPaymentDiscountInfo(contract, paymentType, date, isProRated, proRatedAdded));
    }

    // ACC-8411
    @UsedBy(modules = UsedBy.Modules.Sales)
    @PreAuthorize("hasPermission('payments', 'getReceivedPaymentsDetails')")
    @GetMapping(value = "/getReceivedPaymentsDetails/{id}")
    public ResponseEntity<?> getReceivedPaymentsDetails(@PathVariable("id") Contract contract) {

        List<Long> paymentIds = paymentRepository.findAllPaymentReceivedByContract(contract.getId())
                .stream().map(o ->Long.valueOf(o[0].toString())).collect(Collectors.toList());

        List<Payment> payments = paymentRepository.findAll(paymentIds);

        List<Map<String, Object>> allReceivedPaymentsDetails =
                payments.stream().map(p -> {
                    Map<String, Object> r = new HashMap<>();
                    ContractPayment contractPayment = paymentService.getContractPayment(p);
                    r.put("typeOfPayment", p.getTypeOfPayment() != null ? p.getTypeOfPayment().getName() : "");
                    r.put("amount", p.getAmountOfPayment() != null ? p.getAmountOfPayment() : 0.0D);
                    r.put("dateOfPayment", new LocalDate(p.getDateOfPayment()).toString("yyyy-MM-dd"));
                    r.put("additionalDiscountAmount", contractPayment != null && contractPayment.getAdditionalDiscountAmount() != null ?
                            contractPayment.getAdditionalDiscountAmount() : 0.0D);
                    r.put("moreAdditionalDiscount", contractPayment != null && contractPayment.getMoreAdditionalDiscount() != null
                            ? contractPayment.getMoreAdditionalDiscount() : 0.0D);
                    r.put("additionAmount", contractPayment != null && contractPayment.getAdditionAmount() != null ?
                            contractPayment.getAdditionAmount() : 0.0D);
                    r.put("isProrated", p.getIsProRated());
                    r.put("includeWorkerSalary", p.getIncludeWorkerSalary());
                    return r;
                }).collect(Collectors.toList());

        return ResponseEntity.ok(allReceivedPaymentsDetails);
    }

    /**
     * @created ACC-6993, ACC-7321, ACC-8592
     * @apiNote Validates if payment configurations match with Payment Term Config (PTC) and optionally updates payment dates.
     * Performs two primary operations:
     * 1. Payment Date Updates (if updatePaymentDatesByIds is present):
     *    - Updates payment dates for specified payment IDs
     *    - Validates date format and payment existence
     * 2. PTC Validation (based on feature flags matchWithDDs and matchWithPayments):
     *    - Checks if active monthly Direct Debits (DDs) match with the selected payment term
     *    - Verifies if received payments match with the payment term configuration
     *
     * @param body Request body containing:
     *             Required for PTC validation:
     *             - paymentTypeConfigs: List of payment type configurations
     *             - paymentTermConfig: Payment term configuration details
     *             - contractId: ID of the contract to validate
     *             - dailyRateAmount: (auto-populated) Daily rate amount from payment term config
     *             - abstractPaymentTerm: (auto-populated) Payment term configuration
     *             Optional for payment date updates:
     *             - updatePaymentDatesByIds: Flag to update payment dates
     *             - paymentIds: Comma-separated string of payment IDs to update
     *             - date: New date for payments in yyyy-MM-dd format
     *
     * @return ResponseEntity with a map containing:
     *         - "matched": boolean indicating if payments match PTC
     *         - Additional status information if payment dates were updated
     *
     * @throws BusinessException if:
     *         - An active DD doesn't match the selected payment term
     *         - Invalid date format is provided
     *         - Payment IDs are invalid or not found
     *
     * @see ContractPaymentService#getParameterFeatureMatchingDDcWithCPT()
     * @see ContractPaymentService#checkIfPaymentMatchesWithPTC(Map)
     * @see PaymentService#updateDateOfPayments(List, String)
     */
    @UsedBy(modules = UsedBy.Modules.Sales)
    @PreAuthorize("hasPermission('payments','checkIfPaymentMatchesWithPTC')")
    @PostMapping(value = "/checkIfPaymentMatchesWithPTC")
    public ResponseEntity<?> checkIfPaymentMatchesWithPTCAndUpdatePaymentsDateIfNeeded(@RequestBody Map<String, Object> body) {
        Map<String, Object> r = new HashMap<String, Object>() {{
            put("matched", paymentService.checkIfPaymentMatchesWithPTCAndUpdatePaymentsDateIfNeeded(body, getObjectMapper()));
        }};

        return ResponseEntity.ok(r);
    }

    /**
     * Create a new recurring payment after validating the input
     * ACC-9129
     * @param contractId Long
     * @param dateOfPayment Date: yyyy-MM-dd
     * @return ResponseEntity with success or failure message
     */
    @PreAuthorize("hasPermission('payments','createRecurringPaymentManual')")
    @PostMapping("/createRecurringPaymentManual")
    @Transactional
    public ResponseEntity<?> createRecurringPaymentManual(
            @RequestParam(name = "contractId") Long contractId,
            @RequestParam(name = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(name = "expiryDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date expiryDate) {
        logger.info("Creating recurring payment for contract ID: " + contractId +
                ", start date: " + new LocalDate(startDate).toString("yyyy-MM-dd") +
                ", expiry date: " + new LocalDate(expiryDate).toString("yyyy-MM-dd"));

        // 1- Validate
        if (contractId == null) throw new BusinessException("Contract ID is required");
        if (startDate == null || expiryDate == null) throw new BusinessException("Payment date is required");

        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) throw new BusinessException("Contract not found with ID: " + contractId);

        if (!ContractService.isEligibleForTokenizationViaContract(contract)) {
            throw new BusinessException("Tokenization is not allowed for the contract");
        }

        // 2- Create Recurring Payment
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        ContractPaymentType t = cpt.getContractPaymentTypes()
                .stream().filter(p -> PaymentHelper.isMonthlyPayment(p.getType()))
                .findFirst()
                .orElse(null);
        if (t == null) throw new BusinessException("There is no monthly payment type");

        paymentService.generateRecurringPayment(
                cpt,
                new LocalDate(startDate),
                new LocalDate(expiryDate),
                t.getType(), t.getSubType());

        return ResponseEntity.ok("Recurring payment created successfully");
    }

    @PostMapping("/addAdjustmentPaymentForPreCollectedContractsFromSheet")
    public ResponseEntity<?> addAdjustmentPaymentForPreCollectedContractsFromSheet(MultipartFile file) throws IOException {

        return ResponseEntity.ok(maidVisaFailedMedicalCheckService.
                addAdjustmentPaymentForPreCollectedContractsFromSheet(file));

    }
}
