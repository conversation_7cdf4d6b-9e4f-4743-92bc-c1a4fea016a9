package com.magnamedia.controller;

import com.aspose.words.ConvertUtil;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.WordTemplateRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.PaymentOrderProjection;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.PaymentOrderService;
import com.magnamedia.service.WordImage;
import com.magnamedia.service.WordTemplateService;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.PaymentRequestMethod;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 6-10-2020
 *         Jirra ACC-1962
 */
@RequestMapping("/paymentOrders")
@RestController
public class PaymentOrderController extends BaseRepositoryController<PaymentOrder> {

    private static final Logger logger = Logger.getLogger(PaymentOrderController.class.getName());

    public static final String PAYMENT_ORDER_CASH_ADVANCED_FORM_NOT_SIGNED_TAG_NAME = "cash_advance_agreement_not_signed";
    public static final String PAYMENT_ORDER_CASH_ADVANCED_FORM_SIGNED_TAG_NAME = "cash_advance_agreement_signed";
    public static final String PAYMENT_ORDER_REASON_FINAL_SETTLEMENT = "Final settlement";
    public static final String PAYMENT_ORDER_REASON_ABSCONDING_REMOVAL = "Absconding removal";
    public static final String REVENUE_CODE_FINAL_SETTLEMENT = "Final Settlement of housemaids - Resignation fees";
    public static final String REVENUE_Cash_collected_CC = "CAR 02";
    public static final String REVENUE_Cash_collected_MV = "CAR 01";
    public static final String CASHIER_POSITION_CODE = "Cashier";

    @Autowired
    private PaymentOrderRepository paymentOrderRepository;

    @Autowired
    private WordTemplateService wordTemplateService;

    @Autowired
    private WordTemplateRepository templateRepository;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private RevenueRepository revenueRepository;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private ExpenseRepository expenseRepository;

    @Override
    public BaseRepository<PaymentOrder> getRepository() {
        return paymentOrderRepository;
    }


    @PreAuthorize("hasPermission('paymentOrders','getPaymentsOrder')")
    @GetMapping(value = "/getPayments")
    public ResponseEntity getPaymentsOrder(
            Pageable pageable,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date from_date,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date to_date,
            @RequestParam(required = false) String name) {

        SelectFilter filter = getPaymentsSelectFilter(from_date, to_date, name, PaymentOrderStatus.PENDING);// still pending
        filter.and("reason", "not in ", Arrays.asList(PAYMENT_ORDER_REASON_FINAL_SETTLEMENT,
                PAYMENT_ORDER_REASON_ABSCONDING_REMOVAL));

        SelectQuery<PaymentOrder> selectQuery = new SelectQuery(PaymentOrder.class);
        selectQuery.joinFetch("housemaid");
        selectQuery.leftJoinFetch("expenseRequestTodo");

        selectQuery.filterBy(filter);

        Page<PaymentOrder> paymentOrders = selectQuery.execute(pageable);

        return new ResponseEntity(paymentOrders, HttpStatus.OK);
    }

    //ACC-2802
    @PreAuthorize("hasPermission('paymentOrders','getPaymentsOrder')")
    @GetMapping(value = "/getFinalSettlementPayments")
    public ResponseEntity<?> getFinalSettlementPaymentsOrder(Pageable pageable,
           @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date from_date,
           @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date to_date,
           @RequestParam(required = false) String name) {

        User user = CurrentRequest.getUser();
        if (user == null || !user.hasPosition(CASHIER_POSITION_CODE)) {
            return new ResponseEntity<>("You don't have the required permission", HttpStatus.UNAUTHORIZED);
        }

        SelectFilter filter = getPaymentsSelectFilter(from_date, to_date, name, PaymentOrderStatus.PENDING);
        filter.and("reason", "in", Arrays.asList(PAYMENT_ORDER_REASON_FINAL_SETTLEMENT,
                PAYMENT_ORDER_REASON_ABSCONDING_REMOVAL));
        SelectQuery<PaymentOrder> selectQuery = new SelectQuery(PaymentOrder.class);
        selectQuery.joinFetch("housemaid").leftJoinFetch("expenseRequestTodo");

        selectQuery.filterBy(filter);
        Page<PaymentOrder> paymentOrders = selectQuery.execute(pageable);
        // ACC-4507
        paymentOrders.stream().filter(p -> p.getReason().equalsIgnoreCase(PAYMENT_ORDER_REASON_FINAL_SETTLEMENT))
                .forEach(p -> p.setReason("Collecting resignation fees"));
        return new ResponseEntity(paymentOrders, HttpStatus.OK);
    }

    //ACC-4507
    @PreAuthorize("hasPermission('paymentOrders','reject')")
    @RequestMapping(value = "/reject/{paymentOrderId}", method = RequestMethod.POST)
    public ResponseEntity<?> paymentOrdersReject(
            @PathVariable("paymentOrderId") PaymentOrder paymentOrder,
            @RequestBody Map<String, Object> body) {

        if (paymentOrder == null) return new ResponseEntity<>("Payment order is null", HttpStatus.BAD_REQUEST);


        if (!bucketRepository.existsByHolderAndBucketType(CurrentRequest.getUser(), BucketType.CASH_BOX))
            throw new BusinessException("No cash bucket assigned to your user");

        String rejectionNotes = (String) body.get("rejectionNotes");
        paymentOrder.setStatus(PaymentOrderStatus.REJECTED);
        paymentOrder.setRejectionNotes(rejectionNotes);
        getRepository().save(paymentOrder);
        Setup.getApplicationContext().getBean(PaymentOrderService.class)
                .sendEmailUponRejection(paymentOrder);
        return ResponseEntity.ok().build();
    }

    private SelectFilter getPaymentsSelectFilter(
            Date from_date,
            Date to_date,
            String name,
            PaymentOrderStatus status) {

        SelectFilter mainFilter = new SelectFilter();

        if (name != null) {
            mainFilter.and("housemaid.name", "like", "%" + name + "%");
        }
        if (from_date != null && to_date != null) {
            mainFilter.and("date", ">=", from_date).and("date", "<=", to_date);
        } else if (from_date != null) {
            mainFilter.and("date", ">=", from_date);
        } else if (to_date != null) {
            mainFilter.and("date", "<=", to_date);
        }

        if (status != null) {
            mainFilter.and("status", "=", status);
        }

        SelectFilter filter0 = new SelectFilter("expenseRequestTodo", "IS NOT NULL", null);
        SelectFilter filter1 = new SelectFilter("expenseRequestTodo.status", "=", ExpenseRequestStatus.PAID);
        SelectFilter filter2 = new SelectFilter("expenseRequestTodo.paymentMethod", "=", ExpensePaymentMethod.CASH);
        SelectFilter filter22 = new SelectFilter("expenseRequestTodo.beneficiaryType", "=", ExpenseBeneficiaryType.MAID);

        SelectFilter filter3 = filter0.and(filter1).and(filter2).and(filter22);

        SelectFilter filter4 = new SelectFilter("expenseRequestTodo", "IS NULL", null);

        SelectFilter filter = filter3.or(filter4);

        return mainFilter.and(filter);
    }

    @PreAuthorize("hasPermission('paymentOrders','getPaymentsOrderHistory')")
    @GetMapping(value = "/getPaymentsHistory")
    public ResponseEntity<?> getPaymentsOrderHistory(
            Pageable pageable,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date from_date,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date to_date,
            @RequestParam(required = false) String name) {

        SelectQuery<PaymentOrder> selectQuery = new SelectQuery<>(PaymentOrder.class);
        selectQuery.joinFetch("housemaid");
        if (name != null) {
            selectQuery.filterBy("housemaid.name", "like", "%" + name + "%");
        }
        if (from_date != null && to_date != null) {
            selectQuery.filterBy("date", ">=", from_date).and("date", "<=", to_date);
        } else if (from_date != null) {
            selectQuery.filterBy("date", ">=", from_date);
        } else if (to_date != null) {
            selectQuery.filterBy("date", "<=", to_date);
        }

        selectQuery.filterBy("status", "=", PaymentOrderStatus.PAID);

        // ACC-2802
        selectQuery.filterBy("reason", "not in", Arrays.asList(PAYMENT_ORDER_REASON_FINAL_SETTLEMENT,
                PAYMENT_ORDER_REASON_ABSCONDING_REMOVAL));

        selectQuery.leftJoinFetch("expenseRequestTodo");

        Page<PaymentOrder> paymentOrders = (PageImpl) selectQuery.execute(pageable).map(obj
                -> projectionFactory.createProjection(
                PaymentOrderProjection.class, obj));

        return new ResponseEntity<>(paymentOrders, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('paymentOrders','payPaymentOrder')")
    @PostMapping(value = "/payPaymentOrder")
    @Transactional
    public ResponseEntity<?> payPaymentOrder(@RequestBody PaymentOrder paymentOrder) {

        if (paymentOrder.getStatus() == PaymentOrderStatus.PAID) {
            throw new RuntimeException("payment order is already paid");
        }
        PaymentOrder one = paymentOrderRepository.findOne(paymentOrder.getId());
        if (one.getPurpose() != null && one.getPurpose().getName().equalsIgnoreCase("Cash Advance")) {
            if (paymentOrder.getAttachment(PAYMENT_ORDER_CASH_ADVANCED_FORM_SIGNED_TAG_NAME) == null) {
                throw new RuntimeException("maid should signs first");
            }
        }

        one.setStatus(PaymentOrderStatus.PAID);
        paymentOrderRepository.save(one);

        return new ResponseEntity<>("", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('paymentOrders','downloadUnsignedCashAdvancedForm')")
    @RequestMapping(value = "/downloadUnsignedCashAdvancedForm/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> downloadUnsignedCashAdvancedForm(
            @PathVariable(name = "id") Long id) throws IOException, URISyntaxException {
        PaymentOrder paymentOrder = paymentOrderRepository.findOne(id);

        if (paymentOrder.getAttachment(PAYMENT_ORDER_CASH_ADVANCED_FORM_NOT_SIGNED_TAG_NAME) == null) {

            String fileName = "Cash-Advance-Form-" + paymentOrder.getHousemaid().getName().replace(" ", "_") + ".pdf";
            
            InputStream cashAdvanceFormStream = null;
            
            try {
                cashAdvanceFormStream = generateCashAdvance(paymentOrder, null);

                Attachment attachment = Storage.storeTemporary(
                        fileName,
                        cashAdvanceFormStream,
                        PAYMENT_ORDER_CASH_ADVANCED_FORM_NOT_SIGNED_TAG_NAME,
                        true);
                paymentOrder.addAttachment(attachment);
                paymentOrderRepository.save(paymentOrder);

                return new ResponseEntity(attachment, HttpStatus.OK);
            } finally {
                StreamsUtil.closeStream(cashAdvanceFormStream);
            }
        } else {
            Attachment attachment = paymentOrder.getAttachment(PAYMENT_ORDER_CASH_ADVANCED_FORM_NOT_SIGNED_TAG_NAME);
            return new ResponseEntity(attachment, HttpStatus.OK);
        }
    }

    @PreAuthorize("hasPermission('paymentOrders','generateSignedCashAdvanceForm')")
    @RequestMapping(value = "/signCashAdvancedForm/{id}", method = RequestMethod.POST)
    public ResponseEntity generateSignedCashAdvanceForm(@RequestPart MultipartFile signature, @PathVariable(name = "id") Long id)
            throws IOException, URISyntaxException {

        PaymentOrder paymentOrder = paymentOrderRepository.findOne(id);

        String fileName = "Cash-Advance-Form-" + paymentOrder.getHousemaid().getName().replace(" ", "_") + ".pdf";
        InputStream cashAdvanceFormStream = null;
        
        try {
            cashAdvanceFormStream = generateCashAdvance(paymentOrder, signature);

            Attachment attachment = Storage.storeTemporary(
                    fileName,
                    cashAdvanceFormStream,
                    PAYMENT_ORDER_CASH_ADVANCED_FORM_SIGNED_TAG_NAME,
                    true);
            paymentOrder.addAttachment(attachment);
            paymentOrderRepository.save(paymentOrder);

            return new ResponseEntity(attachment, HttpStatus.OK);
        } finally {
            StreamsUtil.closeStream(cashAdvanceFormStream);
        }
    }

    private InputStream generateCashAdvance(PaymentOrder paymentOrder, MultipartFile signature) throws IOException, URISyntaxException {
        WordTemplate template = templateRepository.findByCodeIgnoreCase("cashAdvanceAgreement");
        XWPFDocument document = new XWPFDocument(Storage.getStream(template.getAttachment("template")));
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        document.write(out);
        InputStream inputStream = new ByteArrayInputStream(out.toByteArray());

        DateTimeFormatter formatter = org.joda.time.format.DateTimeFormat.forPattern("dd/MM/yyyy");
        String today = LocalDate.now().toString(formatter);
        String landedInDubaiDate
                = (paymentOrder.getHousemaid().getLandedInDubaiDate() != null
                ? (new LocalDate(paymentOrder.getHousemaid().getLandedInDubaiDate())).toString(formatter)
                : "");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maid_name", paymentOrder.getHousemaid().getName());
        parameters.put("passport_number", paymentOrder.getHousemaid().getPassportNumber());
        parameters.put("landed_in_dubai_date", landedInDubaiDate);
        parameters.put("today", today);
        parameters.put("Full_Cash_Advance_Amount", (paymentOrder.getAmount() + ""));
        parameters.put("signature", (signature != null ? new WordImage(signature.getInputStream(), 100, 100) : ""));

        InputStream cashAdvanceFormStream = wordTemplateService.generateDocument(
                inputStream,
                parameters);

        try {
            return cashAdvanceFormStream;
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    @Transactional
    public void createPaymentOrder(Housemaid maid, Integer cashAdvanceDays, Double amount) {
        logger.log(Level.SEVERE, "CashAdvanceJob execute.");

        String limitStr = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_CASH_ADVANCED_LIMIT);

        int limit = Integer.parseInt(limitStr);

        PaymentOrderRepository repository = Setup.getRepository(PaymentOrderRepository.class);

        Long count = maid.getCashAdvanceCounter();
        logger.log(Level.SEVERE, "CashAdvanceJob getCashAdvanceCounter: " + count);
        logger.log(Level.SEVERE, "CashAdvanceJob limit: " + limit);
        if (count >= limit) {
            return;
        }

        if (amount != 0) {
            PaymentRequestPurposeRepository paymentRequestPurposeRepository = Setup.getRepository(PaymentRequestPurposeRepository.class);
            List<PaymentRequestPurpose> purposeList = paymentRequestPurposeRepository.findByForHousemaidAndNameContaining(true, "Cash Advance");
            PaymentRequestPurpose cash_advance = null;
            if (purposeList != null && purposeList.size() > 0)
                cash_advance = purposeList.get(0);

            PaymentOrder paymentOrder = new PaymentOrder();
            paymentOrder.setHousemaid(maid);
            paymentOrder.setAmount(amount);
            paymentOrder.setStatus(PaymentOrderStatus.PENDING);
            paymentOrder.setWithLoan(true);
            paymentOrder.setMethodOfPayment(PaymentRequestMethod.CASH);
            paymentOrder.setReason("Cash Advance");
            paymentOrder.setPurpose(cash_advance);
            paymentOrder.setDescription("Full time housemaid cash advance: " + maid.getName());
            paymentOrder.setDate(new Date());
            paymentOrder.setLoanType(LoanType.CASH_ADVANCE);

            repository.save(paymentOrder);

            maid.setCashAdvanceCounter(maid.getCashAdvanceCounter() + 1);
            housemaidRepository.save(maid);

            logger.log(Level.SEVERE, "CashAdvanceJob after EmployeeLoan Adding.");
        }
        logger.log(Level.SEVERE, "CashAdvanceJob execute end.");
    }

    //Jirra-2802
    @PreAuthorize("hasPermission('paymentOrders','exists/by-maid')")
    @RequestMapping(value = "/exists/by-maid/{maidId}/{reason}", method = RequestMethod.GET)
    public ResponseEntity searchByMaid(@PathVariable("maidId") Long housemaidId, @PathVariable("reason") String reason) {
        if (housemaidId == null) return ResponseEntity.ok(false);

        SelectQuery<PaymentOrder> query = new SelectQuery(PaymentOrder.class);
        query.filterBy("housemaid.id", "=", housemaidId);
        query.filterBy("reason", "=", reason);
        query.filterBy("status", "=", PaymentOrderStatus.PAID);

        List<PaymentOrder> paymentOrderList = query.execute();

        boolean isPayOrderExists = paymentOrderList != null && !paymentOrderList.isEmpty();

        return ResponseEntity.ok(isPayOrderExists);
    }

    // ACC-2802
    @PreAuthorize("hasPermission('paymentOrders','/collect/{id}/{attachmentId}')")
    @PostMapping(value = "/collect/{id}/{attachmentId}")
    @Transactional
    public ResponseEntity collectPayOrder(
            @PathVariable("id") PaymentOrder paymentOrder,
            @PathVariable("attachmentId") Attachment signature) {
        
        List<Bucket> buckets = bucketRepository.findByHolderAndBucketType(CurrentRequest.getUser(), BucketType.CASH_BOX);
        
        if(buckets.isEmpty()) throw new BusinessException("No cash bucket assigned to your user");

        if(buckets.size() > 1) throw new BusinessException(buckets.size() + " cash bucket assigned to your user");
        
        paymentOrder.setStatus(PaymentOrderStatus.PAID);
        paymentOrder.addAttachment(signature);

        Attachment receiptFile = generateReceiptFile(paymentOrder, signature);
        paymentOrder.addAttachment(receiptFile);

        if(!paymentOrder.getHousemaid().getHousemaidType().equals(HousemaidType.MAID_VISA)
            && paymentOrder.getReason().equalsIgnoreCase(PAYMENT_ORDER_REASON_FINAL_SETTLEMENT)) {
            try {
                Attachment finalSettlementAttachment = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                        .get("staffmgmt/finalsettlement/getfinalsettlementattachment?housemaid=" + paymentOrder.getHousemaid().getId(), Attachment.class);

                if (finalSettlementAttachment == null) {
                    logger.warning("Final Settlement Attachment is NULL");
                } else {
                    paymentOrder.addAttachment(finalSettlementAttachment);
                }
            } catch (Exception e) {
                throw new RuntimeException("There is a problem with Staff Management API which fetches Final Settlement Attachment", e);
            }
        }

        paymentOrderRepository.save(paymentOrder);

        //create new Transaction
        TransactionsController transactionController = Setup.getApplicationContext().getBean(TransactionsController.class);

        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(new Date().getTime()));
        transaction.setPnlValueDate(new java.sql.Date(new Date().getTime()));
        transaction.setToBucket(buckets.get(0));
        transaction.setPaymentType(PaymentMethod.CASH);
        transaction.setAmount(paymentOrder.getAmount());
        transaction.setLicense(PicklistHelper.getItem("transaction_license", TransactionsController.TRANSACTION_LICENSE_NO_VAT));
        transaction.setAttachments(paymentOrder.getAttachments()
                .stream()
                .filter(att -> !att.getTag().equalsIgnoreCase(PaymentOrder.FINAL_SETTLEMENT_SIGNATURE_ATTACHMENT_TAG))
                .collect(Collectors.toList()));

        if (paymentOrder.getReason().equalsIgnoreCase(PAYMENT_ORDER_REASON_FINAL_SETTLEMENT)) {
            transaction.setRevenue(revenueRepository.findByCode(REVENUE_CODE_FINAL_SETTLEMENT));

            String description = "Final Settlement - Resignation fees of @maid_full_name@ approved by @user@, resignation date @resignation_date@.";
            description = description.replace("@maid_full_name@", paymentOrder.getHousemaid().getName());
            description = description.replace("@user@", paymentOrder.getUserWhoClosedFinalSettlementToDo() != null ?
                    paymentOrder.getUserWhoClosedFinalSettlementToDo().getName() : "");
            description = description.replace("@resignation_date@",
                    paymentOrder.getHousemaid().getFinalSettlementCalculationDate() != null ?
                            DateUtil.formatDateDashedV2(paymentOrder.getHousemaid().getFinalSettlementCalculationDate()) : "");
            transaction.setDescription(description);

        } else {
            // ACC-4507
            transaction.setRevenue(revenueRepository.findByCode(
                    paymentOrder.getHousemaid().getHousemaidType().equals(HousemaidType.MAID_VISA) ?
                            REVENUE_Cash_collected_MV : REVENUE_Cash_collected_CC));
            transaction.setDescription("Cash collected from " + paymentOrder.getHousemaid().getName() + " for absconding removal.");
        }

        HousemaidTransaction housemaidTransaction = new HousemaidTransaction();
        housemaidTransaction.setHousemaid(paymentOrder.getHousemaid());
        transaction.getHousemaids().add(housemaidTransaction);
        transaction.setTransactionType(TransactionEntityType.HOUSEMAID);

        transactionController.createEntity(transaction);
        return ResponseEntity.ok(receiptFile);
    }

    private Attachment generateReceiptFile(PaymentOrder paymentOrder, Attachment signature) {
        Map<String, Object> parameters = new HashMap();

        String beneficiaryName = CurrentRequest.getUser() != null ? CurrentRequest.getUser().getFullName() : "";

        parameters.put("signature", signature != null ? new WordImage(Storage.getStream(signature),
                ConvertUtil.pixelToPoint(160), ConvertUtil.pixelToPoint(47)) : "");
        parameters.put("beneficiary", beneficiaryName);
        parameters.put("payment_amount", paymentOrder.getAmount() != null ? paymentOrder.getAmount() : "");
        parameters.put("paying_date", paymentOrder.getCreationDate());
        parameters.put("date_of_signature", Instant.now().atZone(ZoneId.systemDefault()).toLocalDate().toString());

        InputStream tmp = wordTemplateService.generateDocument("receipt_payment_order", parameters);
        Attachment receiptAttachment = Storage.storeTemporary(beneficiaryName + "payment_order " + Instant.now().atZone(ZoneId.systemDefault()).toLocalDate().toString() + " .pdf",
                tmp, PaymentOrder.FINAL_SETTLEMENT_RECEIPT_ATTACHMENT_TAG.toString(), false);
        return receiptAttachment;
    }
}
