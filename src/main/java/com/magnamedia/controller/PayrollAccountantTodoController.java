package com.magnamedia.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.maidsatv2.actions.employeragreement.SendRequestForApprovalAction;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.AccountantToDoProjection;
import com.magnamedia.entity.projection.OfficeStaffPayrollLogProjection;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.UserHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.*;
import com.magnamedia.service.AccountantTodoService;
import com.magnamedia.service.ClientMessagingAndRefundService;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentBankTransferStep;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import com.magnamedia.workflow.type.PayrollAccountantTodoType;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/accountantTodo")
public class PayrollAccountantTodoController extends BaseRepositoryController<PayrollAccountantTodo> {

    public enum ToDoCategory {
        PAYROLL_ACCOUNTANT_TODO, OFFICE_STAFF_PAYROLL_LOG_TODO
    }

    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;

    @Autowired
    private OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository;

    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    @Autowired
    private PayrollAccountantTodoController selfCtrl;

    @Autowired
    private AccountantTodoService accountantTodoService;

    @Autowired
    ExpensePaymentBankTransferStep expensePaymentBankTransferStep;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private PicklistItemRepository picklistItemRepository;

    @Autowired
    private ExpenseNotificationService expenseNotificationService;

    @Autowired
    private InterModuleConnector interModuleConnector;

    @Override
    public BaseRepository<PayrollAccountantTodo> getRepository() {
        return payrollAccountantTodoRepository;
    }


    @PreAuthorize("hasPermission('accountantTodo', 'create-from-refunds')")
    @RequestMapping(value = {"/create-from-refunds"}, method = {RequestMethod.POST})
    @ResponseBody
    @Transactional
    public ResponseEntity<?> createFromRefunds(
            @RequestBody List<Long> refundsIds,
            @RequestParam(name = "bypassValidation", defaultValue = "false") boolean bypassValidation) {


        for(Long id : refundsIds){
            ClientRefundToDo todo = clientRefundTodoRepository.findOne(id);
            if (todo != null){
                todo.setForceCreateBankTransfer(bypassValidation);

                PayrollAccountantTodo entity = new PayrollAccountantTodo();
                entity.setClientRefundToDo(todo);
                createEntity(entity);
            }
        }
        return okResponse();
    }

    @Override
    public ResponseEntity<?> createEntity(PayrollAccountantTodo entity) {
        if (entity.getClientRefundToDo() != null) {
            ClientRefundToDo clientRefundToDo = entity.getClientRefundToDo();
            if (!clientRefundToDo.isForceCreateBankTransfer() &&
                    !Setup.getApplicationContext().getBean(ClientRefundService.class)
                            .validateCreateTodoFromClientRefund(clientRefundToDo, ClientRefundTodoType.BANK_TRANSFER_CREATED)) {

                logger.info("createBankTransferTodo Validation Failed -> don't create Bank Transfer ToDo");
                return null;
            }

            entity.setCurrency(SalaryCurrency.AED);
            entity.setAmount(clientRefundToDo.getAmount());

            if(payrollAccountantTodoRepository.existsByClientRefundToDo(clientRefundToDo)) {
                logger.info("createBankTransferTodo exited; todo already exist");
                return new ResponseEntity<>(HttpStatus.NOT_MODIFIED);
            }

            Client client = clientRepository.findOne(clientRefundToDo.getClient().getId());

            switch(clientRefundToDo.getMethodOfPayment()) {
                case BANK_TRANSFER:
                    entity.setTaskName(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString());
                    entity.setIban(clientRefundToDo.getIban());
                    entity.setAccountName(clientRefundToDo.getAccountName());
                    entity.setLabel(String.format("Transfer Money to %s", client.getName()));
                    entity.setBankName(clientRefundToDo.getBankName());
                    break;
                case MONEY_TRANSFER:
                    String agencyName = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_NAME);
                    entity.setTaskName(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString());
                    entity.setLabel(String.format("Transfer Money to %s for %s", agencyName, client.getName()));
                    // ACC-3107
                    entity.setIban(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_IBAN));
                    entity.setAccountName(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME));

                    accountantTodoService.sendMoneyTransferEmail(
                            client.getName(), client.getMobileNumber(), clientRefundToDo.getAmount());
                    break;
            }

            Setup.getApplicationContext().getBean(ClientMessagingAndRefundService.class)
                    .sendOnClientRefundApproval(clientRefundToDo);
        } else if (entity.getExpensePayment() != null) {
            ExpensePayment payment = Setup.getRepository(ExpensePaymentRepository.class)
                    .findOne(entity.getExpensePayment().getId());

            if(payrollAccountantTodoRepository.existsByExpensePayment(payment)) {
                logger.info("createBankTransferTodo exited; todo already exist");
                return new ResponseEntity<>(HttpStatus.NOT_MODIFIED);
            }

            switch(entity.getExpensePayment().getMethod()) {
                case BANK_TRANSFER:
                    entity.setTaskName(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString());
                    entity.setIban(entity.getExpensePayment().getBeneficiaryIBAN());
                    entity.setAccountName(entity.getExpensePayment().getBeneficiaryAccountName());
                    // ACC-3473
                    entity.setAccountNumber(entity.getExpensePayment().getBeneficiaryAccountNumber());
                    entity.setMobileNumber(entity.getExpensePayment().getBeneficiaryMobileNumber());
                    entity.setInternational(entity.getExpensePayment().getBeneficiaryInternational());
                    entity.setSwift(entity.getExpensePayment().getBeneficiarySwift());
                    entity.setAddress(entity.getExpensePayment().getBeneficiaryAddress());
                    // ACC-3963
                    entity.setLabel(entity.getExpensePayment().getReplenishmentTodo() != null ?
                            "Replenish " + entity.getExpensePayment().getReplenishmentTodo().getBucket().getName() :
                            "Transfer Money to " + entity.getExpensePayment().getBeneficiaryName());
                    break;

                case MONEY_TRANSFER:
                    String agencyName = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_NAME);
                    entity.setTaskName(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString());
                    entity.setLabel(String.format("Transfer Money to %s for %s", agencyName, entity.getExpensePayment().getBeneficiaryName()));
                    // ACC-3107
                    entity.setIban(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_IBAN));
                    entity.setAccountName(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME));
                    entity.setMobileNumber(entity.getExpensePayment().getBeneficiaryMobileNumber());

                    accountantTodoService.sendMoneyTransferEmail(
                            entity.getExpensePayment().getBeneficiaryName(),
                            entity.getExpensePayment().getBeneficiaryMobileNumber(),
                            entity.getExpensePayment().getAmount());
                    break;
            }

            if (entity.getExpensePayment().getCurrency() != null
                    && entity.getExpensePayment().getCurrency().getId() != null){
                PicklistItem item = picklistItemRepository.findOne(
                        entity.getExpensePayment().getCurrency().getId());
                entity.setCurrency(getSalaryCurrencyFromItem(item));
            }
            entity.setAmount(entity.getExpensePayment().getAmount());
        } else if (entity.getSendRequestForApprovalAction() != null &&
                entity.getSendRequestForApprovalAction().getId() != null) {

            SendRequestForApprovalAction sendRequestForApprovalAction =
                    Setup.getRepository(SendRequestForApprovalActionRepository.class)
                            .findOne(entity.getSendRequestForApprovalAction().getId());

            if(payrollAccountantTodoRepository.existsBySendRequestForApprovalAction(sendRequestForApprovalAction)) {
                logger.info("createBankTransferTodo exited; todo already exist");
                return new ResponseEntity<>(HttpStatus.NOT_MODIFIED);
            }

            if (sendRequestForApprovalAction.getPaymentMethod() != null) {
                switch(sendRequestForApprovalAction.getPaymentMethod()) {
                    case BANK_TRANSFER:
                        entity.setTaskName(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString());
                        entity.setIban(sendRequestForApprovalAction.getIban());
                        entity.setAccountName(sendRequestForApprovalAction.getAccountName());
                        entity.setLabel(String.format("Transfer Money to %s",
                                sendRequestForApprovalAction.getBeneficiaryName()));
                        break;

                    case MONEY_TRANSFER:
                        String agencyName = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_NAME);
                        entity.setTaskName(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString());
                        entity.setLabel(String.format("Transfer Money to %s for %s", agencyName, sendRequestForApprovalAction.getBeneficiaryName()));
                        entity.setIban(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_IBAN));
                        entity.setAccountName(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME));

                        accountantTodoService.sendMoneyTransferEmail(
                                sendRequestForApprovalAction.getBeneficiaryName(),
                                sendRequestForApprovalAction.getBeneficiaryMobileNumber(),
                                sendRequestForApprovalAction.getAmount());
                        break;
                }
            }

            entity.setCurrency(SalaryCurrency.AED);
            entity.setAmount(sendRequestForApprovalAction.getAmount());
        }

        expenseNotificationService.expenseToDoCreatedEmail(entity.getEntityType(), entity.getTaskName());

        return super.createEntity(entity);
    }

    public SalaryCurrency getSalaryCurrencyFromItem(PicklistItem item){

        if (item == null || item.getCode() == null)
            return SalaryCurrency.AED;
        String itemCode = item.getCode().toLowerCase();
        switch (itemCode){
            case "aed":
                return SalaryCurrency.AED;
            case "usd":
                return SalaryCurrency.USD;
            case "eur":
                return SalaryCurrency.EUR;
            case "qr":
                return SalaryCurrency.QR;
        }
        return SalaryCurrency.AED;
    }



    @PreAuthorize("hasPermission('accountantTodo', 'findOpenedTodos')")
    @GetMapping("/findOpenedTodos")
    @ResponseBody
    public ResponseEntity<?> findOpenedTodos(Pageable pageable) {
        if (!CurrentRequest.getUser().hasPosition("bank_transfer_accountant_user_position"))
            return this.unauthorizedReponse();

        Page<PayrollAccountantTodo> page = payrollAccountantTodoRepository.findOpenToDos(pageable);
        page.forEach(t -> accountantTodoService.updateBankTransferInfo(t));

        return ResponseEntity.ok(this.project(page, AccountantToDoProjection.class));
    }

    @PreAuthorize("hasPermission('accountantTodo', 'cancelOpenedTodo')")
    @GetMapping(value = "/cancelOpenedTodo/{id}")
    @Transactional
    public ResponseEntity cancelOpenedTodo(@PathVariable("id") PayrollAccountantTodo todo) {
        if (!CurrentRequest.getUser().hasPosition("bank_transfer_accountant_user_position")) {
            return unauthorizedReponse();
        }

        if (!todo.getBeneficiaryType().get("beneficiaryType").equals("BUCKET_REPLENISHMENT")) {
            throw new BusinessException("You can't cancel this type of ToDo");
        }

        todo.setTaskName("");
        todo.setStopped(true);
        getRepository().save(todo);

        if (todo.getExpensePayment() != null) {
            todo.getExpensePayment().setTaskName("");
            todo.getExpensePayment().setStopped(true);
            Setup.getRepository(ExpensePaymentRepository.class).save(todo.getExpensePayment());

            if (todo.getExpensePayment().getReplenishmentTodo() != null) {
                todo.getExpensePayment().getReplenishmentTodo().setTaskName("");
                todo.getExpensePayment().getReplenishmentTodo().setStopped(true);
                Setup.getRepository(BucketReplenishmentTodoRepository.class)
                        .save(todo.getExpensePayment().getReplenishmentTodo());
            }
        }
        return okResponse();
    }

    public ResponseEntity<?> get(@PathVariable("id") Long id) {
        if (this.checkPermission("get") && CurrentRequest.getUser().hasPosition("bank_transfer_accountant_user_position")) {
            return new ResponseEntity(this.project(this.getRepository().findOne(id), AccountantToDoProjection.class), HttpStatus.OK);
        } else {
            return this.unauthorizedReponse();

        }
    }

    @PreAuthorize("hasPermission('accountantTodo','accountantAction')")
    @RequestMapping(value = "/accountantAction", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> accountantAction(@RequestBody ObjectNode objectNode) throws IOException {
        if (CurrentRequest.getUser().hasPosition("bank_transfer_accountant_user_position")) {

            this.validateAccountantAction(objectNode);

            this.update(objectNode);
            PayrollAccountantTodo updated = this.parse(objectNode);
            PayrollAccountantTodo payrollAccountantTodo = this.getRepository().findOne(updated.getId());

            if (payrollAccountantTodo.isStopped() && payrollAccountantTodo.isCompleted()) {
                throw new RuntimeException("Todo is already approved");
            }


            if (payrollAccountantTodo.getExpensePayment() != null) {
                ExpensePayment expensePayment = payrollAccountantTodo.getExpensePayment();
                expensePayment.setPaidBy(CurrentRequest.getUser());
                expensePayment.setPaymentDate(new Date());
                Setup.getRepository(ExpensePaymentRepository.class).save(expensePayment);
            }

            payrollAccountantTodo.setCompleted(true);
            payrollAccountantTodo.setStopped(true);
            payrollAccountantTodo.setCeoAction(PayrollAccountantTodoManagerAction.PENDING);
            payrollAccountantTodo.setManagerAction(PayrollAccountantTodoManagerAction.PENDING);

            if (payrollAccountantTodo.getTaskName()
                    .equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString())) {
                //payrollAccountantTodo.setCharges(0D);
                payrollAccountantTodoRepository.save(payrollAccountantTodo);

            } else if (payrollAccountantTodo.getTaskName()
                    .equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString())) {
                payrollAccountantTodoRepository.save(payrollAccountantTodo);

            } else {
                throw new RuntimeException("Unsupported Task");
            }

        } else {
            return this.unauthorizedReponse();
        }
        return ResponseEntity.ok("Todo Approved Successfully");
    }

    private void validateAccountantAction(ObjectNode objectNode) {
        PayrollAccountantTodo oldTodo = payrollAccountantTodoRepository
                .findOne(new ObjectMapper().convertValue(objectNode.get("id"), Long.class));

        if (oldTodo.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString())) {
            Double amount = new ObjectMapper().convertValue(objectNode.get("amount"), Double.class);
            if (!amount.equals(oldTodo.getAmount())) {
                throw new RuntimeException("Amount couldn't be changed");
            }

        } else if (oldTodo.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString())) {
            Double total = new ObjectMapper().convertValue(objectNode.get("total"), Double.class);
            Double charges = new ObjectMapper().convertValue(objectNode.get("charges"), Double.class);
            if (!total.equals(charges + oldTodo.getAmount())) {
                throw new RuntimeException("Total Amount should be equal to amount + charges");
            }

        }

    }

    private static final Logger logger =
            Logger.getLogger(PayrollAccountantTodoController.class.getName());

    @PreAuthorize("hasPermission('accountantTodo', 'findPendingConfirmationTodos')")
    @RequestMapping(value = "/findPendingConfirmationTodos", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity findPendingConfirmationTodos(@RequestParam(value = "questionedPage", required = false) CooQuestion.QuestionedPage questionedPage, Pageable pageable) {
        if (UserHelper.hasBankTransferCooPosition()) {

            logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page");
            return new ResponseEntity(getPendingConfirmationForCoo(pageable), HttpStatus.OK);
        } else if (UserHelper.hasBankTransferReviewerPosition()) {
            logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page");
            String managerName = "ACCOUNTANT_TODO_MANAGER_NAME:" + CurrentRequest.getUser().getUsername();
            logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page: " + managerName);
            Page p = payrollAccountantTodoRepository.findPendingConfirmationForManager(pageable, managerName);
            logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page " + p.getTotalElements());
            return new ResponseEntity(p, HttpStatus.OK);
        } else {
            return this.unauthorizedReponse();
        }
    }

    public Page getPendingConfirmationForCoo(Pageable pageable) {
        Page<Object[]> p = payrollAccountantTodoRepository.findPendingConfirmationForCoo(pageable);

        p = projectCooNativeQueryResult(p, CooQuestion.QuestionedPage.BANK_CONFIRMATION, pageable);

        Double totalAmount = payrollAccountantTodoRepository.sumPendingConfirmationForCoo();
        return new AccountingPage(p.getContent(), pageable, p.getTotalElements(),
                totalAmount == null ? 0 : totalAmount,
                totalAmount == null ? "0" : PaymentHelper.df.format(totalAmount));
    }

    private Page findQuestionedConfirmedModifiedToDos(Date fromDate, Date toDate, Pageable pageable) {
        Page<Object[]> p = payrollAccountantTodoRepository.findQuestionedConfirmedModifiedToDosForCoo(fromDate, toDate, pageable);

        return projectCooNativeQueryResult(p, CooQuestion.QuestionedPage.NIGHT_REVIEW, pageable);
    }

    private Page findConfirmedModifiedToDos(Date fromDate, Date toDate, boolean archive, Pageable pageable) {
        Page<Object[]> p = payrollAccountantTodoRepository.findConfirmedModifiedToDosForCoo(fromDate, toDate, archive, pageable);

        return projectCooNativeQueryResult(p, CooQuestion.QuestionedPage.NIGHT_REVIEW, pageable);
    }

    private Page projectCooNativeQueryResult(Page<Object[]> p, CooQuestion.QuestionedPage questionedPage, Pageable pageable) {
        List list = p.getContent().stream()
                .map(item -> {
                    if (item[1].equals("PAYROLL_ACCOUNTANT_TODO")) {
                        PayrollAccountantTodo accountantTodo = payrollAccountantTodoRepository.findOne(((BigInteger) item[0]).longValue());
                        accountantTodo.setCooQuestionedPage(questionedPage);
                        return project(accountantTodo, AccountantToDoProjection.class);
                    } else if (item[1].equals("OFFICE_STAFF_PAYROLL_LOG")) {
                        OfficeStaffPayrollLog officeStaffPayrollLog = officeStaffPayrollLogRepository.findOne(((BigInteger) item[0]).longValue());
                        officeStaffPayrollLog.setCooQuestionedPage(questionedPage);
                        return project(officeStaffPayrollLog, OfficeStaffPayrollLogProjection.class);
                    }

                    return item;
                }).collect(Collectors.toList());
        p = new PageImpl(list, pageable, p.getTotalElements());

        logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page " + p.getTotalElements());
        return p;
    }

    @PreAuthorize("hasPermission('accountantTodo', 'managerAction')")
    @RequestMapping(value = "/managerAction", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> managerActionAPI(@RequestBody PayrollAccountantTodo body) {
        if (managerActionValidation()) {
            managerAction(body);
            return ResponseEntity.ok("Action applied successfully");
        } else {
            return this.unauthorizedReponse();
        }
    }

    private boolean managerActionValidation() {
        return CurrentRequest.getUser() != null && CurrentRequest.getUser().hasPosition("bank_transfer_reviewer_user_position");
    }

    private void managerAction(PayrollAccountantTodo body) {
        PayrollAccountantTodo todo = payrollAccountantTodoRepository.findOne(body.getId());
        if (todo.getManagerAction() == PayrollAccountantTodoManagerAction.PENDING) {
            todo.setManagerAction(body.getManagerAction());
            todo.setManagerActionBy(CurrentRequest.getUser());
            payrollAccountantTodoRepository.save(todo);
        } else {
            throw new RuntimeException("Action has already been taken");
        }
    }

    @PreAuthorize("hasPermission('accountantTodo', 'ceoAction')")
    @RequestMapping(value = "/ceoAction", method = RequestMethod.POST)
    public ResponseEntity ceoActionAPI(@RequestBody Map body) {
        if (UserHelper.hasBankTransferCooPosition()) {
            selfCtrl.ceoAction(body);
            return ResponseEntity.ok("Action applied successfully");
        } else {
            return this.unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('accountantTodo', 'ceoAction/approveList')")
    @RequestMapping(value = "/ceoAction/approveList", method = RequestMethod.POST)
    public ResponseEntity ceoActionListAPI(@RequestBody List<Map> body) {
        if (body == null) return new ResponseEntity(HttpStatus.BAD_REQUEST);

        if (UserHelper.hasBankTransferCooPosition()) {

            for (Map todo : body) {
                todo.put("ceoAction", PayrollAccountantTodoManagerAction.APPROVED.toString());
                selfCtrl.ceoAction(todo);
            }

            return ResponseEntity.ok("Action applied successfully");
        } else {
            return this.unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('accountantTodo', 'ceoAction/approveAll')")
    @RequestMapping(value = "/ceoAction/approveAll", method = RequestMethod.POST)
    public ResponseEntity ceoActionAllAPI() {
        if (UserHelper.hasBankTransferCooPosition()) {
            Page<Object[]> pendingToDos = payrollAccountantTodoRepository.findPendingConfirmationForCoo(PageRequest.of(0, Integer.MAX_VALUE));

            for (Object[] todo : pendingToDos) {
                Map map = new HashMap();
                map.put("id", ((BigInteger) todo[0]).longValue());
                map.put("toDoCategory", todo[1]);
                map.put("ceoAction", PayrollAccountantTodoManagerAction.APPROVED.toString());

                selfCtrl.ceoAction(map);
            }

            return ResponseEntity.ok("Action applied successfully");
        } else {
            return this.unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('accountantTodo', 'get-modified-confirmed-todos/questioned-expenses')")
    @RequestMapping(value = "/get-modified-confirmed-todos/questioned-expenses", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity findQuestionedConfirmedModifiedToDosAPI(Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(new Date(), Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(14).toDate();

        if (UserHelper.hasNightReviewCooPosition()) {
            logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page");
            return new ResponseEntity(findQuestionedConfirmedModifiedToDos(fromDate, toDate, pageable), HttpStatus.OK);
        } else {
            return unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('accountantTodo', 'get-modified-confirmed-todos')")
    @RequestMapping(value = "/get-modified-confirmed-todos", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity findConfirmedModifiedToDosAPI(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                        @RequestParam boolean archive,
                                                        Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(date, Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(1).toDate();

        if (UserHelper.hasNightReviewCooPosition()) {
            logger.log(Level.SEVERE, "findPendingConfirmationTodos COO page");
            return new ResponseEntity(findConfirmedModifiedToDos(fromDate, toDate, archive, pageable), HttpStatus.OK);
        } else {
            return unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('accountantTodo','mark-as-done-by-coo')")
    @RequestMapping(value = "/mark-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markAsDoneByCooAPI(@RequestBody Map body) {
        if (!UserHelper.hasBankTransferCooPosition()) {
            return unauthorizedReponse();
        }

        selfCtrl.markAsDoneByCoo(body);

        return okResponse();
    }

    @PreAuthorize("hasPermission('accountantTodo','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-all-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markAllAsDoneByCooAPI(@RequestParam(value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                @RequestParam boolean archive) {
        if (!UserHelper.hasBankTransferCooPosition()) {
            return unauthorizedReponse();
        }

        Date toDate = DateUtil.getDateAtHour(date, Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(1).toDate();

        Page<Object[]> confirmedToDos = findConfirmedModifiedToDos(fromDate, toDate, archive, PageRequest.of(0, Integer.MAX_VALUE));

        for (Object[] todo : confirmedToDos) {
            Map map = new HashMap();
            map.put("id", ((BigInteger) todo[0]).longValue());
            map.put("toDoCategory", todo[1]);
            selfCtrl.markAsDoneByCoo(map);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('accountantTodo','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-list-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markListAsDoneByCooAPI(@RequestBody List<Map> body) {

        for (Map todo : body) {
            selfCtrl.markAsDoneByCoo(todo);
        }

        return okResponse();
    }

    @Transactional
    public void markAsDoneByCoo(Map todo) {
        if (todo == null) return;

        if (todo.containsKey("id") && todo.containsKey("toDoCategory")) {
            Long id = Long.parseLong(todo.get("id").toString());

            if (todo.get("toDoCategory").toString().equals(ToDoCategory.PAYROLL_ACCOUNTANT_TODO.toString())) {

                PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
                payrollAccountantTodo.setId(id);
                markAsDoneForPayrollAccountantToDo(payrollAccountantTodo);

            } else if (todo.get("toDoCategory").toString().equals(ToDoCategory.OFFICE_STAFF_PAYROLL_LOG_TODO.toString())) {

                OfficeStaffPayrollLog officeStaffPayrollLog = new OfficeStaffPayrollLog();
                officeStaffPayrollLog.setId(id);
                markAsDoneForOfficeStaffPayrollLogToDo(officeStaffPayrollLog);
            } else {
                logger.severe("todo has missing Data");
            }
        }
    }

    private void markAsDoneForPayrollAccountantToDo(PayrollAccountantTodo body) {
        PayrollAccountantTodo todo = getRepository().findOne(body.getId());
        todo.setDoneByCoo(true);
        getRepository().save(todo);
    }

    private void markAsDoneForOfficeStaffPayrollLogToDo(OfficeStaffPayrollLog body) {
        OfficeStaffPayrollLog todo = officeStaffPayrollLogRepository.findOne(body.getId());
        todo.setDoneByCoo(true);
        officeStaffPayrollLogRepository.save(todo);
    }

    @Transactional
    public void ceoAction(Map todo) {
        if (todo == null) return;

        if (todo.containsKey("id") && todo.containsKey("toDoCategory") && todo.containsKey("ceoAction")) {
            Long id = Long.parseLong(todo.get("id").toString());
            PayrollAccountantTodoManagerAction ceoAction = PayrollAccountantTodoManagerAction.valueOf(todo.get("ceoAction").toString());

            if (todo.get("toDoCategory").toString().equals(ToDoCategory.PAYROLL_ACCOUNTANT_TODO.toString())) {

                PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
                payrollAccountantTodo.setId(id);
                payrollAccountantTodo.setCeoAction(ceoAction);
                ceoActionForPayrollAccountantToDo(payrollAccountantTodo);

            } else if (todo.get("toDoCategory").toString().equals(ToDoCategory.OFFICE_STAFF_PAYROLL_LOG_TODO.toString())) {

                OfficeStaffPayrollLog officeStaffPayrollLog = new OfficeStaffPayrollLog();
                officeStaffPayrollLog.setId(id);
                officeStaffPayrollLog.setCeoAction(ceoAction);

                ceoActionForOfficeStaffPayrollLogToDo(officeStaffPayrollLog);
            } else {
                logger.severe("todo has missing Data");
            }
        }
    }

    private void ceoActionForPayrollAccountantToDo(PayrollAccountantTodo body) {
        PayrollAccountantTodo todo = payrollAccountantTodoRepository.findOne(body.getId());

        if (todo.getCeoAction() == PayrollAccountantTodoManagerAction.PENDING) {
            todo.setCeoAction(body.getCeoAction());
            todo.setCeoActionBy(CurrentRequest.getUser());
            payrollAccountantTodoRepository.save(todo);

            if (todo.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER.toString()) ||
                    todo.getTaskName().equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER.toString())) {
                if (todo.getClientRefundToDo() != null) {
                    Setup.getApplicationContext()
                            .getBean(ClientRefundService.class)
                            .closeClientRefundTodo(todo.getClientRefundToDo(), todo.getCeoAction().toString());
                } else if (todo.getExpensePayment() != null) {
                    expensePaymentBankTransferStep.doBusinessForExpensePaymentAfterConfirmBankTransfer(todo);
                }
            }
        } else {
            throw new RuntimeException("Action has already been taken");
        }
    }

    private void ceoActionForOfficeStaffPayrollLogToDo(OfficeStaffPayrollLog body) {
        OfficeStaffPayrollLog todo = officeStaffPayrollLogRepository.findOne(body.getId());

        if (todo.getCeoAction() == PayrollAccountantTodoManagerAction.PENDING) {
            todo.setCeoAction(body.getCeoAction());
            todo.setCeoActionBy(CurrentRequest.getUser());
            officeStaffPayrollLogRepository.save(todo);
        } else {
            throw new RuntimeException("Action has already been taken");
        }
    }
}