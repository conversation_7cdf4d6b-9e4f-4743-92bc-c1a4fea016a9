package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.*;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.workflow.entity.projection.BankStatementFileProjection;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * <AUTHOR> Kassam <<EMAIL>>
 *         Created At Apr 18, 2020
 **/


@RestController
@RequestMapping("/bankStatementFile")
public class BankStatementFileController extends BaseRepositoryController<BankStatementFile> {

    public static final String storageTransactionTerminalId = "********";
    public static final String tadbeerTransactionTerminalId = "********";

    public static final List<String> tadbeerTransactionTerminalIdsList = new ArrayList<String>() {{
        add("********");
        add("********");
        add("********");
        add("********");
        add("********");
        add("********");
        add("********");
    }};

    private DecimalFormat df = new DecimalFormat("###,###,###");

    @Autowired
    private BankStatementFileRepository bankStatementFileRepository;

    @Autowired
    private CreditCardStatementRecordRepository creditCardStatementRecordRepository;

    @Autowired
    private BankStatementTransactionRepository bankStatementTransactionRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private BucketRepository bucketRepository;

    @Autowired
    private TransactionsController transactionsController;

    @Autowired
    private PaymentController paymentController;

    @Autowired
    private BouncingFlowService bouncingFlowService;

    @Autowired
    private SwitchingBankAccountService switchingBankAccountService;

    @Autowired
    private ContractController contractController;

    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private BankStatementFileService bankStatementFileService;

    @Autowired
    @Qualifier("taskExecutor")
    private TaskExecutor taskExecutor;

    @Override
    public BaseRepository<BankStatementFile> getRepository() {
        return bankStatementFileRepository;
    }

    @PreAuthorize("hasPermission('bankStatementFile','filterStatement')")
    @RequestMapping(value = "/filterStatement", method = RequestMethod.GET)
    public ResponseEntity filterStatement(@RequestParam(name = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
                                          @RequestParam(name = "toDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
                                          @RequestParam(name = "showDeletedFiles", required = false, defaultValue = "false") boolean showDeletedFiles,
                                          Sort sort,
                                          Pageable pageable) throws Exception {

        SelectQuery<BankStatementFile> query = new SelectQuery(BankStatementFile.class);
        if (fromDate != null)
            query.filterBy("reportDate", ">=", fromDate);
        if (toDate != null)
            query.filterBy("reportDate", "<=", toDate);

        if (!showDeletedFiles)
            query.filterBy("deleted", "=", false);

        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        }

        Page<BankStatementFile> statementFiles = query.execute(pageable);
        return new ResponseEntity(project(statementFiles, BankStatementFileProjection.class), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','filterStatement')")
    @RequestMapping(value = "/deleteFileStatement", method = RequestMethod.POST)
    public ResponseEntity deleteFileStatement(@RequestBody BankStatementFile bankStatementFile) throws Exception {

        bankStatementFile = bankStatementFileRepository.findOne(bankStatementFile.getId());
        bankStatementFile.setDeleted(true);
        bankStatementFileRepository.save(bankStatementFile);

        return new ResponseEntity("deleted", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','filterStatement')")
    @RequestMapping(value = "/restoreFileStatement", method = RequestMethod.POST)
    public ResponseEntity restoreFileStatement(@RequestBody BankStatementFile bankStatementFile) throws Exception {

        bankStatementFile = bankStatementFileRepository.findOne(bankStatementFile.getId());
        bankStatementFile.setDeleted(false);
        bankStatementFileRepository.save(bankStatementFile);

        return new ResponseEntity("restored", HttpStatus.OK);
    }

    @Override
    @RequestMapping(value = {"/create"}, method = {RequestMethod.POST})
    @Transactional
    @NoPermission
    public ResponseEntity<?> create(@RequestBody BankStatementFile entity) {

        if (checkBeforeInsert(entity)) {

            ResponseEntity<?> responseEntity = super.create(entity);

            if (responseEntity.getStatusCode().equals(HttpStatus.OK) && responseEntity.getBody() instanceof BankStatementFile) {
                BankStatementFile file = (BankStatementFile) responseEntity.getBody();
                file.extractRecords();

                // ACC-7560
                Map<String, Object> payload = new HashMap<>();
                payload.put("entityId", file.getId());
                BackgroundTaskHelper.createBGTParsingStatementUploaded(
                        UploadStatementEntityType.BankStatementFile,
                        "create_bank_statement_transaction_" + file.getId(),
                        payload);

                return new ResponseEntity<>(file.getId(), HttpStatus.OK);
            }
            return new ResponseEntity<>("Id must be null.", HttpStatus.BAD_REQUEST);

        } else
            return new ResponseEntity<>("The count, tags, or types of attachments aren't convenient!.", HttpStatus.BAD_REQUEST);
    }

    public void addNewFile(Long bankStatementFileId) {
        BankStatementFile bankStatementFile = getRepository().findOne(bankStatementFileId);

        bankStatementFileService.extractRecords(bankStatementFile);
        bankStatementFileService.processRecords(bankStatementFile);
        bankStatementFile.setResolved(true);

        getRepository().save(bankStatementFile);
    }

    @PreAuthorize("hasPermission('bankStatementFile', 'listTransactions')")
    @RequestMapping(value = {"/{id}/listTransactions"},
            method = {RequestMethod.GET}
    )
    public ResponseEntity<?> listTransactions(@PathVariable Long id) {

        return new ResponseEntity<>(bankStatementTransactionRepository.findByFileId(id), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile', 'resolveTransaction')")
    @RequestMapping(value = {"/resolveTransaction"},
            method = {RequestMethod.POST}
    )
    public ResponseEntity<?> resolveTransaction(@RequestParam(name = "transactionId") Long transactionId) {
        BankStatementTransaction bankTransaction = bankStatementTransactionRepository.findOne(transactionId);
        if (bankTransaction != null && !bankTransaction.isResolved()) {
            bankTransaction.setResolved(true);
            bankStatementTransactionRepository.save(bankTransaction);
            return new ResponseEntity<>("Resolved Successfully", HttpStatus.OK);
        } else
            return new ResponseEntity<>("The transaction is already resolved", HttpStatus.BAD_REQUEST);

    }

    private boolean checkBeforeInsert(BankStatementFile entity) {

        // check types of attachments according to tags
        if (entity.getAttachments() != null && entity.getAttachments().size() == 2) {
            Attachment attachment1 = entity.getAttachments().get(0);
            Attachment attachment2 = entity.getAttachments().get(1);
            attachment1 = Storage.getAttchment(attachment1.getId());
            attachment2 = Storage.getAttchment(attachment2.getId());
            entity.setAttachments(Arrays.asList(attachment1, attachment2));

            return entity.getAttachment("bankStatement") != null &&
                    Arrays.asList("xlsx", "xls").contains(entity.getAttachment("bankStatement").getExtension()) &&
                    entity.getAttachment("creditCardStatement") != null &&
                    entity.getAttachment("creditCardStatement").getExtension().equals("csv");
        } else {
            if (entity.getAttachments() != null && entity.getAttachments().size() == 1) {
                Attachment attachment1 = entity.getAttachments().get(0);
                attachment1 = Storage.getAttchment(attachment1.getId());
                entity.setAttachments(Arrays.asList(attachment1));

                return (entity.getAttachment("bankStatement") != null &&
                        Arrays.asList("xlsx", "xls").contains(entity.getAttachment("bankStatement").getExtension()))
                        ||
                        (entity.getAttachment("creditCardStatement") != null &&
                                entity.getAttachment("creditCardStatement").getExtension().equals("csv"));
            }
        }
        return false;
    }

    @PreAuthorize("hasPermission('bankStatementFile','summary')")
    @RequestMapping("/{id}/summary")
    public ResponseEntity Summary(@PathVariable("id") BankStatementFile file) {
        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);
        Map<String, Object> res = this.getSummary(file);
        return res != null ? new ResponseEntity<>(res, HttpStatus.OK) :
                new ResponseEntity<>("summary transaction error", HttpStatus.BAD_REQUEST);
    }

    @PreAuthorize("hasPermission('bankStatementFile','getProcessSummary')")
    @RequestMapping("/{id}/getProcessSummary")
    public ResponseEntity getProcessSummary(@PathVariable("id") BankStatementFile file) {
        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);


        HashMap<String, Object> res = new HashMap<>();

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "BankStatementTransaction");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        query.filterBy("name", "like", "BankStatementFile_" + file.getId() + "%");
        query.sortBy("creationDate", false, true);
        query.setLimit(1);
        List<BackgroundTask> backGroundTasks = query.execute();

        boolean processing = false;
        String pageName = "";
        long totalRecords = 0;
        long confirmedRecords = 0;
        if (!backGroundTasks.isEmpty()) {
            processing = true;
            BackgroundTask backgroundTask = backGroundTasks.get(0);
            BankStatementTransaction transaction = bankStatementTransactionRepository.findOne(backgroundTask.getRelatedEntityId());
            switch (transaction.getBankTransactionType()) {
                case DIRECT_DEBIT:
                    pageName = "DirectDebits";
                    break;
                case CHEQUE:
                    pageName = "ChequeDeposits";
                    break;
                case CASH:
                case WIRE_TRANSFER:
                    pageName = "CashDeposits";
                    break;
                case PDC:
                    pageName = "PDCPayments";
                    break;
                case EXPENSES:
                case BUCKET_REFILL:
                    pageName = "Expenses";
                    break;
                case MAIDS_AT_EXPENSE:
                    pageName = "MaidsAtExpense";
                    break;
                default: {
                    pageName = "Expenses";
                    processing = false;
                    break;
                }
            }

            if (processing) {
                String[] split = backgroundTask.getName().split("_");
                if (split.length == 3) {
                    String timeStamp = split[2];

                    SelectQuery<BackgroundTask> totalCountQuery = new SelectQuery<>(BackgroundTask.class);
                    totalCountQuery.filterBy("relatedEntityType", "=", "BankStatementTransaction");
                    totalCountQuery.filterBy("name", "=", "BankStatementFile_" + file.getId() + "_" + timeStamp);

                    AggregateQuery aggregateQuery = new AggregateQuery(totalCountQuery, Aggregate.Count, "id");
                    totalRecords = aggregateQuery.execute().longValue();

                    SelectQuery<BackgroundTask> confirmedCountQuery = new SelectQuery<>(BackgroundTask.class);
                    confirmedCountQuery.filterBy("relatedEntityType", "=", "BankStatementTransaction");
                    confirmedCountQuery.filterBy("status", "IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
                    confirmedCountQuery.filterBy("name", "=", "BankStatementFile_" + file.getId() + "_" + timeStamp);

                    AggregateQuery aggregateQuery1 = new AggregateQuery(confirmedCountQuery, Aggregate.Count, "id");
                    confirmedRecords = aggregateQuery1.execute().longValue();


                }
            }
        }

        res.put("totalRecords", totalRecords);
        res.put("processedRecords", confirmedRecords);
        res.put("processing", processing);
        res.put("pageName", pageName);

        return new ResponseEntity<>(res, HttpStatus.OK);
    }


//    @PreAuthorize("hasPermission('bankStatementFile','cashDepositTransactions')")
//    @RequestMapping("/{id}/cashDepositTransactions")
//    public ResponseEntity getDirectDebitTransaction(@PathVariable("id") BankStatementFile file) {
//        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
//        query.filterBy("file", "=", file);
//        query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
//        List<BankStatementTransaction> list = query.execute();
//        List<DirectDebitTransactionProjection> res = list.stream().map(t -> projectionFactory.createProjection(
//                DirectDebitTransactionProjection.class, t)).collect(Collectors.toList());
//
//        return new ResponseEntity(res, HttpStatus.OK);
//    }

    private Map<String, Object> getSummary(BankStatementFile file) {
        Map<String, Object> res = new HashMap<>();
        List<BankTransactionStatus> unmatched = Arrays.asList(
                BankTransactionStatus.UNMATCHED_CLIENT_NOT_FOUND,
                BankTransactionStatus.UNMATCHED_PAYMENT_NOT_FOUND,
                BankTransactionStatus.UNMATCHED_MAIDS_AT_EXPENSE,
                BankTransactionStatus.UNMATCHED_REFUND_REQUEST_NOT_FOUND);
        List<BankTransactionStatus> unmatchedForDirectDebitAndCheques = Arrays.asList(
                BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND,
                BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);


        // direct debit
        Map<String, Object> receivedDirectDebit = getSummaryForTypeAndStatusForReceived(file, BankTransactionType.DIRECT_DEBIT, Collections.singletonList(BankTransactionStatus.RECEIVED));
        Map<String, Object> unmatchedDirectDebit = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.DIRECT_DEBIT), unmatchedForDirectDebitAndCheques);
        Map<String, Object> bouncedDirectDebit = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.DIRECT_DEBIT), Collections.singletonList(BankTransactionStatus.BOUNCED));

        // cheque deposit
        Map<String, Object> receivedChequeDeposit = getSummaryForTypeAndStatusForReceived(file, BankTransactionType.CHEQUE, Collections.singletonList(BankTransactionStatus.RECEIVED));
        Map<String, Object> unmatchedChequeDeposit = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.CHEQUE), unmatchedForDirectDebitAndCheques);
        Map<String, Object> bouncedChequeDeposit = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.CHEQUE), Collections.singletonList(BankTransactionStatus.BOUNCED));

        // cash
        Map<String, Object> cash = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.CASH), null);

        // pdc
        Map<String, Object> pdc = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.PDC), null);

        // wire transfer
        Map<String, Object> wireTransfer = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.WIRE_TRANSFER), null);

        // undefined transaction // undefined transaction tab
        Map<String, Object> undefinedTransaction = getSummaryForTypeAndStatusForUnmatchedExpensesAndUndefinedTransactions(file, BankTransactionType.UNDEFINED_TRANSACTION);

        // unmatched expenses // expenses tab
        Map<String, Object> unmatchedExpenses = getSummaryForTypeAndStatusForUnmatchedExpensesAndUndefinedTransactions(file, BankTransactionType.UNMATCHED_EXPENSES);

        // expenses
        Map<String, Object> matchedExpenses = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.EXPENSES), Collections.singletonList(BankTransactionStatus.MATCHED));
        Map<String, Object> unmatchedClientExpenses = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.EXPENSES), unmatched);
        Map<String, Object> otherExpenses = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.EXPENSES), Collections.singletonList(BankTransactionStatus.OTHER_EXPENSES));
        Map<String, Object> bucketRefills = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.BUCKET_REFILL), null);

        Map<String, Object> maidsAtExpenses = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.MAIDS_AT_EXPENSE), Collections.singletonList(BankTransactionStatus.MATCHED));
        Map<String, Object> unmatchedMaidsAtExpenses = getSummaryForTypeAndStatus(file, Collections.singletonList(BankTransactionType.MAIDS_AT_EXPENSE), Collections.singletonList(BankTransactionStatus.UNMATCHED_MAIDS_AT_EXPENSE));

        Map<String, Object> bankTransferMatchedTransaction = getSummaryForTypeAndStatus(file, Arrays.asList(BankTransactionType.EXPENSE_REQUEST, BankTransactionType.REPLENISHMENT_REQUEST), Collections.singletonList(BankTransactionStatus.MATCHED));

        res.put("receivedDirectDebit", receivedDirectDebit);
        res.put("unmatchedDirectDebit", unmatchedDirectDebit);
        res.put("bouncedDirectDebit", bouncedDirectDebit);
        res.put("receivedChequeDeposit", receivedChequeDeposit);
        res.put("unmatchedChequeDeposit", unmatchedChequeDeposit);
        res.put("bouncedChequeDeposit", bouncedChequeDeposit);
        res.put("cash", cash);
        res.put("pdc", pdc);
        res.put("wireTransfer", wireTransfer);
        res.put("undefinedTransaction", undefinedTransaction);
        res.put("unmatchedExpenses", unmatchedExpenses);
        res.put("clientRefundExpenses", matchedExpenses);
        res.put("unmatchedClientRefundExpenses", unmatchedClientExpenses);
        res.put("otherExpenses", otherExpenses);
        res.put("bucketRefills", bucketRefills);
        res.put("isResolved", file.isResolved());
        res.put("creditCardPayment", file.getTotalCreditCardTransactions());
        res.put("bankTransferMatchedTransaction", bankTransferMatchedTransaction);
        res.put("maidsAtExpenses", maidsAtExpenses);
        res.put("unmatchedMaidsAtExpenses", unmatchedMaidsAtExpenses);

        // tabs total information
        // direct debit tab
        Map<String, Object> directDebitTab = new HashMap<>();
        directDebitTab.put("total", (int) receivedDirectDebit.get("total") + (int) unmatchedDirectDebit.get("total") + (int) bouncedDirectDebit.get("total"));

        int directDebitTabAmountResolved = (int) receivedDirectDebit.get("AED_INT_RESOLVED") + (int) bouncedDirectDebit.get("AED_INT_RESOLVED");
        directDebitTab.put("AED_RESOLVED", df.format(directDebitTabAmountResolved));

        int directDebitTabAmountUnResolved = (int) receivedDirectDebit.get("AED_INT_UNRESOLVED") + (int) unmatchedDirectDebit.get("AED_INT_UNRESOLVED") + (int) bouncedDirectDebit.get("AED_INT_UNRESOLVED");
        directDebitTab.put("AED_UNRESOLVED", df.format(directDebitTabAmountUnResolved));

        res.put("directDebitTab", directDebitTab);


        // cheque Deposit Tab
        Map<String, Object> chequeDepositTab = new HashMap<>();
        chequeDepositTab.put("total", (int) receivedChequeDeposit.get("total") + (int) unmatchedChequeDeposit.get("total") + (int) bouncedChequeDeposit.get("total"));

        int chequeDepositTabResolved = (int) receivedChequeDeposit.get("AED_INT_RESOLVED") + (int) bouncedChequeDeposit.get("AED_INT_RESOLVED");
        chequeDepositTab.put("AED_RESOLVED", df.format(chequeDepositTabResolved));

        int chequeDepositTabUnResolved = (int) receivedChequeDeposit.get("AED_INT_UNRESOLVED") + (int) unmatchedChequeDeposit.get("AED_INT_UNRESOLVED") + (int) bouncedChequeDeposit.get("AED_INT_UNRESOLVED");
        chequeDepositTab.put("AED_UNRESOLVED", df.format(chequeDepositTabUnResolved));

        res.put("ChequeDepositTab", chequeDepositTab);


        // credit card payment tab
        Map<String, Object> creditCardPaymentTab = new HashMap<>();
        creditCardPaymentTab.put("total", file.getTotalCreditCardTransactions());
        creditCardPaymentTab.put("AED", df.format(file.getTotalCreditCardAmount()));
        res.put("CreditCardPaymentTab", creditCardPaymentTab);


        // cash Deposit Tab
        Map<String, Object> cashDepositTab = new HashMap<>();
        cashDepositTab.put("total", (int) cash.get("total") + (int) wireTransfer.get("total"));

        int cashDepositTabResolved = (int) cash.get("AED_INT_RESOLVED") + (int) wireTransfer.get("AED_INT_RESOLVED");
        cashDepositTab.put("AED_RESOLVED", df.format(cashDepositTabResolved));

        int cashDepositTabUnResolved = (int) cash.get("AED_INT_UNRESOLVED") + (int) wireTransfer.get("AED_INT_UNRESOLVED");
        cashDepositTab.put("AED_UNRESOLVED", df.format(cashDepositTabUnResolved));

        res.put("CashDepositTab", cashDepositTab);


        // pdc Payment Tab
        Map<String, Object> pDCPaymentTab = new HashMap<>();
        pDCPaymentTab.put("total", pdc.get("total"));

        int pDCPaymentTabResolved = (int) pdc.get("AED_INT_RESOLVED");
        pDCPaymentTab.put("AED_RESOLVED", df.format(pDCPaymentTabResolved));

        int pDCPaymentTabUnResolved = (int) pdc.get("AED_INT_UNRESOLVED");
        pDCPaymentTab.put("AED_UNRESOLVED", df.format(pDCPaymentTabUnResolved));

        res.put("PDCPaymentTab", pDCPaymentTab);

        // expenses Tab
        Map<String, Object> expensesTab = new HashMap<>();
        expensesTab.put("total", (int) unmatchedClientExpenses.get("total") + (int) matchedExpenses.get("total") +
                (int) otherExpenses.get("total") + (int) unmatchedExpenses.get("total") + (int) bucketRefills.get("total")
                     + (int) maidsAtExpenses.get("total") + (int) unmatchedMaidsAtExpenses.get("total"));

        int expensesTabAmountResolved = (int) matchedExpenses.get("AED_INT_RESOLVED") +
                (int) otherExpenses.get("AED_INT_RESOLVED") + (int) unmatchedExpenses.get("AED_INT_RESOLVED") + (int) bucketRefills.get("AED_INT_RESOLVED")
                + (int) maidsAtExpenses.get("AED_INT_RESOLVED") + (int) unmatchedMaidsAtExpenses.get("AED_INT_RESOLVED");
        expensesTab.put("AED_RESOLVED", df.format(expensesTabAmountResolved));

        int expensesTabAmountUnResolved = (int) unmatchedClientExpenses.get("AED_INT_UNRESOLVED") + (int) matchedExpenses.get("AED_INT_UNRESOLVED") +
                (int) otherExpenses.get("AED_INT_UNRESOLVED") + (int) unmatchedExpenses.get("AED_INT_UNRESOLVED") + (int) bucketRefills.get("AED_INT_UNRESOLVED")
                + (int) maidsAtExpenses.get("AED_INT_UNRESOLVED") + (int) unmatchedMaidsAtExpenses.get("AED_INT_UNRESOLVED");
        expensesTab.put("AED_UNRESOLVED", df.format(expensesTabAmountUnResolved));

        res.put("ExpensesTab", expensesTab);

        // undefined Transaction Tab
        Map<String, Object> undefinedTransactionTab = new HashMap<>();
        undefinedTransactionTab.put("total", undefinedTransaction.get("total"));

        int undefinedTransactionTabResolved = (int) undefinedTransaction.get("AED_INT_RESOLVED");
        undefinedTransactionTab.put("AED_RESOLVED", df.format(undefinedTransactionTabResolved));

        int undefinedTransactionTabUnResolved = (int) undefinedTransaction.get("AED_INT_UNRESOLVED");
        undefinedTransactionTab.put("AED_UNRESOLVED", df.format(undefinedTransactionTabUnResolved));
        res.put("UndefinedTransactionTab", undefinedTransactionTab);

        return res;
    }


    public Map<String, Object> getSummaryForTypeAndStatus(BankStatementFile file, List<BankTransactionType> types, List<BankTransactionStatus> statuses) {
        Map<String, Object> res = new HashMap();
        res.put("total", getCount(file, types, statuses, null));
        res.put("resolved", getCount(file, types, statuses, true));
        res.put("unresolved", getCount(file, types, statuses, false));

        Integer sumAED = getSum(file, types, statuses, null);
        res.put("AED", df.format(sumAED));
        res.put("AED_INT", sumAED);

        Integer resolvedSumAED = getSum(file, types, statuses, true);
        res.put("AED_RESOLVED", df.format(resolvedSumAED));
        res.put("AED_INT_RESOLVED", resolvedSumAED);

        Integer unResolvedSumAED = getSum(file, types, statuses, false);
        res.put("AED_UNRESOLVED", df.format(unResolvedSumAED));
        res.put("AED_INT_UNRESOLVED", unResolvedSumAED);


        return res;
    }

    public Map<String, Object> getSummaryForTypeAndStatusForReceived(BankStatementFile file, BankTransactionType type, List<BankTransactionStatus> statuses) {
        Map<String, Object> res = new HashMap();
        res.put("total", getCountForReceived(file, type, statuses, null));
        res.put("resolved", getCountForReceived(file, type, statuses, true));
        res.put("unresolved", getCountForReceived(file, type, statuses, false));

        Integer sumAED = getSumForReceived(file, type, statuses, null);
        res.put("AED", df.format(sumAED));
        res.put("AED_INT", sumAED);

        Integer resolvedSumAED = getSumForReceived(file, type, statuses, true);
        res.put("AED_RESOLVED", df.format(resolvedSumAED));
        res.put("AED_INT_RESOLVED", resolvedSumAED);

        Integer unResolvedSumAED = getSumForReceived(file, type, statuses, false);
        res.put("AED_UNRESOLVED", df.format(unResolvedSumAED));
        res.put("AED_INT_UNRESOLVED", unResolvedSumAED);


        return res;
    }

    public Map<String, Object> getSummaryForTypeAndStatusForUnmatchedExpensesAndUndefinedTransactions(BankStatementFile file, BankTransactionType type) {
        Map<String, Object> res = null;
        res = new HashMap<>();
        res.put("total", getCountForUnMatchedExpensesAndUndefinedTransactions(file, type, null));
        res.put("resolved", getCountForUnMatchedExpensesAndUndefinedTransactions(file, type, true));
        res.put("unresolved", getCountForUnMatchedExpensesAndUndefinedTransactions(file, type, false));

        Integer sumAED = getSumForUnMatchedExpensesAndUndefinedTransactions(file, type, null);
        res.put("AED", df.format(sumAED));
        res.put("AED_INT", sumAED);

        Integer resolvedSumAED = getSumForUnMatchedExpensesAndUndefinedTransactions(file, type, true);
        res.put("AED_RESOLVED", df.format(resolvedSumAED));
        res.put("AED_INT_RESOLVED", resolvedSumAED);

        Integer unResolvedSumAED = getSumForUnMatchedExpensesAndUndefinedTransactions(file, type, false);
        res.put("AED_UNRESOLVED", df.format(unResolvedSumAED));
        res.put("AED_INT_UNRESOLVED", unResolvedSumAED);


        return res;
    }


    public Integer getCount(BankStatementFile file, List<BankTransactionType> types, List<BankTransactionStatus> statuses, Boolean isResolved) {
        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        //Jirra ACC-2545
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        query.filterBy("file", "=", file);
        query.filterBy("bankTransactionType", "in", types);
        if (statuses != null && !statuses.isEmpty())
            query.filterBy("bankTransactionStatus", "in", statuses);
        if (isResolved != null)
            query.filterBy("resolved", "=", isResolved);
        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Count, "id");
        return aggregateQuery.execute().intValue();
    }

    public Integer getCountForReceived(BankStatementFile file, BankTransactionType type, List<BankTransactionStatus> statuses, Boolean isResolved) {
        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);

        //Jirra ACC-2545
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        query.filterBy("file", "=", file);
        query.filterBy("bankTransactionType", "=", type);
        if (statuses != null && !statuses.isEmpty())
            query.filterBy("bankTransactionStatus", "in", statuses);
        if (isResolved != null) {
            if (isResolved) {
                SelectFilter filter = new SelectFilter("resolved", "=", true)
                        .or("payment.status", "=", PaymentStatus.RECEIVED);
                query.filterBy(filter);
            } else {
                SelectFilter filter = new SelectFilter("resolved", "=", false)
                        .and("payment.status", "!=", PaymentStatus.RECEIVED);
                query.filterBy(filter);
            }
        }
        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Count, "id");
        return aggregateQuery.execute().intValue();
    }

    public Integer getCountForUnMatchedExpensesAndUndefinedTransactions(BankStatementFile file, BankTransactionType type, Boolean isCreatedByUser) {
        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        //Jirra ACC-2545
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        query.filterBy("file", "=", file);
        query.filterBy("bankTransactionType", "=", type);

        if (isCreatedByUser != null)
            query.filterBy("transactionCreatedByUser", "=", isCreatedByUser);

        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Count, "id");
        return aggregateQuery.execute().intValue();
    }

    public Integer getSum(BankStatementFile file, List<BankTransactionType> types, List<BankTransactionStatus> statuses, Boolean isResolved) {
        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        //Jirra ACC-2545
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        query.filterBy("file", "=", file);
        query.filterBy("bankTransactionType", "in", types);
        if (statuses != null && !statuses.isEmpty())
            query.filterBy("bankTransactionStatus", "in", statuses);
        if (isResolved != null)
            query.filterBy("resolved", "=", isResolved);

        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
        return aggregateQuery.execute().intValue();
    }

    public Integer getSumForReceived(BankStatementFile file, BankTransactionType type, List<BankTransactionStatus> statuses, Boolean isResolved) {
        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        //Jirra ACC-2545
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        query.filterBy("file", "=", file);
        query.filterBy("bankTransactionType", "=", type);
        if (statuses != null && !statuses.isEmpty())
            query.filterBy("bankTransactionStatus", "in", statuses);
        if (isResolved != null) {
            if (isResolved) {
                SelectFilter filter = new SelectFilter("resolved", "=", true)
                        .or("payment.status", "=", PaymentStatus.RECEIVED);
                query.filterBy(filter);
            } else {
                SelectFilter filter = new SelectFilter("resolved", "=", false)
                        .and("payment.status", "!=", PaymentStatus.RECEIVED);
                query.filterBy(filter);
            }
        }


        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
        return aggregateQuery.execute().intValue();
    }

    public Integer getSumForUnMatchedExpensesAndUndefinedTransactions(BankStatementFile file, BankTransactionType type, Boolean isCreatedByUser) {
        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        //Jirra ACC-2545
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        query.filterBy("file", "=", file);
        query.filterBy("bankTransactionType", "=", type);

        if (isCreatedByUser != null)
            query.filterBy("transactionCreatedByUser", "=", isCreatedByUser);

        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
        return aggregateQuery.execute().intValue();
    }

    @Transactional
    public void confirmBankStatementTransaction(Long bankTransactionId, HashMap parameters) throws Exception {
        BankStatementTransaction bankTransaction = bankStatementTransactionRepository.findOne(bankTransactionId);
        if(bankTransaction.isResolved()) return;

        BankStatementRecord bankRecord = bankTransaction.getBankStatementRecord();
        logger.log(Level.INFO, "confirming transaction: " + bankTransaction.getId() + " of type: " + bankTransaction.getBankTransactionType() + " transactions");

        bankTransaction.setProcessing(true);
        bankStatementTransactionRepository.save(bankTransaction);
        bankTransaction = bankStatementTransactionRepository.findOne(bankTransactionId);

        try {
            switch (bankTransaction.getBankTransactionType()) {
                case DIRECT_DEBIT:
                    bankStatementFileService.confirmDirectDebitTransaction(bankTransaction);
                    break;
                case CASH:
                    bankStatementFileService.confirmCashDepositOrWireTransferTransaction(bankTransaction, bankRecord, PaymentMethod.BANK_DEPOSIT);
                    break;
                case CHEQUE:
                    bankStatementFileService.confirmChequeDepositTransaction(bankTransaction, bankRecord);
                    break;
                case WIRE_TRANSFER:
                    bankStatementFileService.confirmCashDepositOrWireTransferTransaction(bankTransaction, bankRecord, PaymentMethod.WIRE_TRANSFER);
                    break;
                case CARD:
                    //it will have an API specified for it "confirmNewTransactions"
                    break;
                case PDC:
                    bankStatementFileService.confirmPDCTransaction(bankTransaction, bankRecord);
                    break;
                case EXPENSES:
                    bankStatementFileService.confirmExpensesTransaction(bankTransaction, bankRecord);
                    break;
                case BUCKET_REFILL:
                    bankStatementFileService.confirmBucketRefillTransaction(bankTransaction, bankRecord);
                    break;
                case EXPENSE_REQUEST:
                case REPLENISHMENT_REQUEST:
                    bankStatementFileService.confirmExpensesPaymentTransaction(bankTransaction, parameters);
                    break;
                case MAIDS_AT_EXPENSE:
                    bankStatementFileService.confirmExEmployerTransaction(bankTransaction, bankRecord);
                    break;
                default:
                    break;
            }
        } finally {
            bankTransaction = bankStatementTransactionRepository.findOne(bankTransactionId);
            bankTransaction.setProcessing(false);
            bankStatementTransactionRepository.save(bankTransaction);
        }
    }

    @Transactional
    public void confirmBankStatementTransactionList(
            List<String> ids,
            HashMap parameters) throws Exception {

        if (ids == null || ids.isEmpty()) return;

        for (String id : ids){
            confirmBankStatementTransaction(Long.parseLong(id), parameters);
        }
    }

    @PreAuthorize("hasPermission('bankStatementFile','confirmTransactions')")
    @RequestMapping("/confirmTransactions")
    public ResponseEntity<?> confirmTransactions(@RequestBody List<HashMap> bankTransactions) throws Exception {

        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "BankStatementTransaction");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();

        if (!result.isEmpty())
            throw new RuntimeException("This feature is requested before and still processing.");

        logger.log(Level.INFO, "Enter confirmTransactions API for " + bankTransactions.size() + " transactions");

        Map<Long, HashMap> transactionsMap = new HashMap();
        for (HashMap bankTransaction : bankTransactions) {
            transactionsMap.put(Long.parseLong(bankTransaction.get("id").toString()), bankTransaction);
        }

        BankStatementTransaction firstOne = bankStatementTransactionRepository.findOne(
                Long.parseLong(bankTransactions.get(0).get("id").toString()));

        SelectQuery<BankStatementTransaction> selectQuery = new SelectQuery(BankStatementTransaction.class);
        selectQuery.filterBy("id", "in", new ArrayList(transactionsMap.keySet()));
        selectQuery.filterBy("resolved", "=", false);
        selectQuery.filterBy("processing", "=", false);

        if (firstOne.getBankTransactionType().equals(BankTransactionType.DIRECT_DEBIT)) {
            selectQuery.sortBy("directDebitFile.directDebit.id", true, true);
            List<BankStatementTransaction> bankTransactionsList = selectQuery.execute();

            long time = new Date().getTime();
            List<Long> processingRecordsIds = new ArrayList<>();
            BankStatementTransaction lastRecord = null;

            //ACC-4585
            List<BankStatementTransaction> matchedTransactionByDdfList = new ArrayList<>();
            for (BankStatementTransaction record : bankTransactionsList) {
                matchedTransactionByDdfList.add(record);
                if (record.getTransactionAmount() != null && record.getPayment() != null
                        && record.getDirectDebitFile() != null && record.getDirectDebitFile().getDirectDebit() != null
                        && record.getDirectDebitFile().getDirectDebit().getType().equals(DirectDebitType.ONE_TIME)) {
                    SelectQuery<BankStatementTransaction> matchedTransactionByDdf = new SelectQuery(BankStatementTransaction.class);
                    matchedTransactionByDdf.filterBy("directDebitFile.id", "=", record.getDirectDebitFile().getId());
                    matchedTransactionByDdf.filterBy("date", "=", record.getDate());
                    matchedTransactionByDdf.filterBy("id", "<>", record.getId());
                    matchedTransactionByDdfList.addAll(matchedTransactionByDdf.execute());
                }
            }

            for (BankStatementTransaction record : matchedTransactionByDdfList){
                if (lastRecord == null ||
                        lastRecord.getDirectDebitFile() == null ||
                        lastRecord.getDirectDebitFile().getDirectDebit() == null ||
                        record.getDirectDebitFile() == null ||
                        record.getDirectDebitFile().getDirectDebit() == null ||
                        !lastRecord.getDirectDebitFile().getDirectDebit().getId().equals(
                                record.getDirectDebitFile().getDirectDebit().getId())){

                    if (!processingRecordsIds.isEmpty())
                        createBackgroundTaskList(processingRecordsIds, record.getFile().getId(), null, time);

                    processingRecordsIds = new ArrayList<>();
                }
                processingRecordsIds.add(record.getId());
                lastRecord = record;
            }

            if (!processingRecordsIds.isEmpty()) {
                createBackgroundTaskList(processingRecordsIds, firstOne.getFile().getId(), null, time);
            }
            return ResponseEntity.ok("confirmation registerd, Payments will be updated.");
        }

        List<BankStatementTransaction> bankTransactionsList = selectQuery.execute();
        long time = new Date().getTime();
        Long userId = CurrentRequest.getUser().getId();
        for (BankStatementTransaction bankTransaction : bankTransactionsList) {

            createBackgroundTaskValidation(bankTransaction, transactionsMap.get(bankTransaction.getId()), time, userId);
        }

        Thread.sleep(5000);
        return new ResponseEntity("confirmation registerd, Payments will be updated.", HttpStatus.OK);
    }

    // ACC-8246
    public void createBackgroundTaskValidation(BankStatementTransaction b, HashMap parameters, long time, Long userId) {
        if (BankTransactionType.EXPENSE_REQUEST.equals(b.getBankTransactionType()) &&
                b.getExpense() != null && b.getExpense().getDisabled()) {
            throw new BusinessException("Expense code " + b.getExpense().getCode() + " is disabled");
        }
        createBackgroundTask(b.getId(), parameters, time, userId);
    }

    @Async
    public void createBackgroundTask(Long bankTransactionId, HashMap parameters, long time, Long userId) {

        SecurityContext sc = SecurityContextHolder.getContext();
        try {
            UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(
                    Setup.getRepository(UserRepository.class).findOne(userId), null, null);
            sc.setAuthentication(authReq);
            BankStatementTransaction bankTransaction = bankStatementTransactionRepository.findOne(bankTransactionId);

            String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BGT_QUEUE_OF_CONFIRM_BANK_STATEMENT_TRANSACTION);
            backgroundTaskService.create(new BackgroundTask.builder(
                    "BankStatementFile_" + bankTransaction.getFile().getId() + "_" + time,
                    "accounting",
                    "bankStatementFileController",
                    "confirmBankStatementTransaction")
                    .withRelatedEntity("BankStatementTransaction", bankTransaction.getId())
                    .withParameters(
                            new Class[] { Long.class, HashMap.class },
                            new Object[] { bankTransaction.getId(), parameters })
                    .withQueue(BackgroundTaskQueues.valueOf(queue))
                    .build());
        } finally {
            sc.setAuthentication(null);
        }
    }

    public void createBackgroundTaskList(List<Long> bankTransactionIds, Long fileId, HashMap parameters, long time) {

        if (bankTransactionIds == null || bankTransactionIds.isEmpty()) return;

        taskExecutor.execute(new Runnable() {
            Long userId = CurrentRequest.getUser().getId();

            @Override
            public void run() {
                SecurityContext sc = SecurityContextHolder.getContext();
                try {
                    UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(
                            Setup.getRepository(UserRepository.class).getOne(userId), null, null);
                    sc.setAuthentication(authReq);

                    String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BGT_QUEUE_OF_CONFIRM_BANK_STATEMENT_TRANSACTION);
                    backgroundTaskService.create(new BackgroundTask.builder(
                            "BankStatementFile_" + fileId + "_" + time,
                            "accounting",
                            "bankStatementFileController",
                            "confirmBankStatementTransactionList")
                            .withRelatedEntity("BankStatementFile", fileId)
                            .withParameters(
                                    new Class[] { List.class, HashMap.class }, 
                                    new Object[] { bankTransactionIds.stream().map(x -> x.toString()).collect(Collectors.toList()), parameters })
                            .withQueue(BackgroundTaskQueues.valueOf(queue))
                            .build());
                } finally {
                    sc.setAuthentication(null);
                }
            }
        });
    }

    @PreAuthorize("hasPermission('bankStatementFile','confirmTransactions')")
    @RequestMapping("/confirmAllTransactions/{id}/{grid}")
    public ResponseEntity<?> confirmAllTransactions(
            @PathVariable(value = "id") BankStatementFile file,
            @PathVariable(value = "grid") String grid) throws Exception {

        SelectQuery<BackgroundTask> query2 = new SelectQuery<>(BackgroundTask.class);
        query2.filterBy("relatedEntityType", "=", "BankStatementTransaction");
        query2.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query2.execute();

        if (!result.isEmpty())
            throw new RuntimeException("This feature is requested before and still processing.");

        logger.log(Level.INFO, "Enter confirmAllTransactions API for " + file.getId() + ".");
        String selectStatement = "select t.id from BankStatementTransaction t " +
                "left join t.payment p ";

        Map<String, Object> para = new HashMap<>();
        StringBuilder cond = new StringBuilder("where t.file.id = :fileId"); para.put("fileId", file.getId());
        cond.append(" and t.date >= :tDate "); para.put("tDate", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));
        cond.append(" and t.resolved = :resolved "); para.put("resolved", false);
        cond.append(" and t.processing = :processing "); para.put("processing", false);

        switch (grid) {
            case "directDebitMatchedTransactionReceivedPayment":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.DIRECT_DEBIT);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.RECEIVED);
                cond.append(" and p.status != :status ");
                para.put("status", PaymentStatus.RECEIVED);
                break;
            case "directDebitMatchedTransactionBouncedPayment":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.DIRECT_DEBIT);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.BOUNCED);
                break;
            case "matchedTransactionReceivedCheques":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.CHEQUE);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.RECEIVED);
                cond.append(" and p.status != :status ");
                para.put("status", PaymentStatus.RECEIVED);
                break;
            case "matchedTransactionBouncedCheques":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.CHEQUE);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.BOUNCED);
                cond.append(" and p.status != :status ");
                para.put("status", PaymentStatus.BOUNCED);
                break;
            case "matchedTransactionsClientRefunds":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.EXPENSES);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.MATCHED);
                break;
            case "otherExpenses":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.EXPENSES);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.OTHER_EXPENSES);
                break;
            case "bucketRefills":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.BUCKET_REFILL);
                break;
            case "matchedPdcPaymentTransactions":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.PDC);
                break;
            case "transactionsUnknownWireTransfers":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.WIRE_TRANSFER);
                break;
            case "transactionsUnknownCashDeposits":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.CASH);
                break;
            case "bankTransferMatchedTransaction":
                cond.append(" and t.bankTransactionType in :bankTransactionType ");
                para.put("bankTransactionType", Arrays.asList(BankTransactionType.EXPENSE_REQUEST, BankTransactionType.REPLENISHMENT_REQUEST));
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.MATCHED);
                break;
            case "maidsAtExpense":
                cond.append(" and t.bankTransactionType = :bankTransactionType ");
                para.put("bankTransactionType", BankTransactionType.MAIDS_AT_EXPENSE);
                cond.append(" and t.bankTransactionStatus = :bankTransactionStatus ");
                para.put("bankTransactionStatus", BankTransactionStatus.MATCHED);
                break;
        }
        List<Long> bankTransactionsList = new SelectQuery<>(selectStatement + cond, "", Long.class, para).execute();
        logger.log(Level.INFO, "Enter confirmAllTransactions count is " + (bankTransactionsList != null ? bankTransactionsList.size() : 0) + ".");

        long time = new Date().getTime();
        Long userId = CurrentRequest.getUser().getId();
        for (Long bankTransaction : bankTransactionsList) {
            if (grid.equals("bankTransferMatchedTransaction")) {
                createBackgroundTaskValidation(bankStatementTransactionRepository.findOne(bankTransaction), null, time, userId);
            } else {
                createBackgroundTask(bankTransaction, null, time, userId);
            }
        }

        Thread.sleep(5000);
        return new ResponseEntity("confirmation registerd, Payments will be updated.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','cashAndWireTransactions')")
    @RequestMapping("/{id}/cashAndWireTransactions")
    public ResponseEntity getCashAndWireTransactions(@PathVariable("id") BankStatementFile file, @RequestParam(name = "gridType") String gridType, Pageable pageable) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);
        SelectQuery<BankStatementTransaction> query;
        AggregateQuery totalAmountQuery;
        Double totalAmount;
        PageImpl page;
        AccountingPage accountingPage = null;

        switch (gridType) {
            case "transactionsUnknownCashDeposits":// Cash Deposit
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CASH);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "transactionsUnknownWireTransfers": // Wire Transfer
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.WIRE_TRANSFER);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;

            case "alreadyMatchedTransactionsUnknownWireTransfers": // Wire Transfer
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy(new SelectFilter("bankTransactionType", "=", BankTransactionType.WIRE_TRANSFER)
                        .or("bankTransactionType", "=", BankTransactionType.CASH));

                query.filterBy("resolved", "=", true);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
        }
        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','cashAndWireTransactionsCSV')")
    @RequestMapping("/{id}/cashAndWireTransactionsCSV")
    public void getCashAndWireTransactionsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType) throws IOException {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<BankStatementTransaction> query = null;

        String[] headerList = null;

        switch (gridType) {
            case "transactionsUnknownCashDeposits":// Cash Deposit
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CASH);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                headerList = new String[]{"date", "transactionAmount", "revenue", "description", "paymentDetail"};

                break;
            case "transactionsUnknownWireTransfers": // Wire Transfer
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.WIRE_TRANSFER);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                headerList = new String[]{"date", "transactionAmount", "revenue", "description", "paymentDetail"};

                break;

            case "alreadyMatchedTransactionsUnknownWireTransfers": // Wire Transfer
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy(new SelectFilter("bankTransactionType", "=", BankTransactionType.WIRE_TRANSFER)
                        .or("bankTransactionType", "=", BankTransactionType.CASH));

                query.filterBy("resolved", "=", true);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                headerList = new String[]{"bankTransactionType", "date", "transactionAmount", "revenue", "description", "paymentDetail"};

                break;
        }

        if (query == null)
            throw new RuntimeException("wrong gridType sent");


        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), DDAndChequeAndPDCTransactionCSVProjection.class, headerList);

            createDownloadResponse(response, "CashAndWire_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('bankStatementFile','expensesTransactions')")
    @RequestMapping("/{id}/expensesTransactions")
    public ResponseEntity getExpensesTransactions(
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType,
            Pageable pageable) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        query.leftJoin("payment");
        query.filterBy("date", ">=", java.sql.Date.valueOf(
                TransactionsController.TRANSACTION_MINIMUM_DATE)); // ACC-2545

        AggregateQuery totalAmountQuery;
        Double totalAmount;
        PageImpl page;
        AccountingPage accountingPage = null;

        switch (gridType) {
            case "matchedTransactionsClientRefunds":// Matched Expenses Client Refund
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", false);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;

            case "alreadyMatchedTransactionsClientRefunds":// Matched Expenses Client Refund
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", true);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "unmatchedTransactionsClientRefunds": // Unmatched Expenses Client Refund
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "in", Arrays.asList(
                        BankTransactionStatus.UNMATCHED_CLIENT_NOT_FOUND,
                        BankTransactionStatus.UNMATCHED_PAYMENT_NOT_FOUND,
                        BankTransactionStatus.UNMATCHED_REFUND_REQUEST_NOT_FOUND));
                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "otherExpenses": // Other Expenses
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.OTHER_EXPENSES);
                query.filterBy("resolved", "=", false);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "alreadyMatchedOtherExpenses": // Other Expenses
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.OTHER_EXPENSES);
                query.filterBy("resolved", "=", true);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "bucketRefills": // Bucket Refills
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.BUCKET_REFILL);
                query.filterBy("resolved", "=", false);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "alreadyMatchedBucketRefills": // Bucket Refills
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.BUCKET_REFILL);
                query.filterBy("resolved", "=", true);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "unmatchedTransactions": // Unmatched Expenses Transactions
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNMATCHED_EXPENSES);
                query.filterBy("transactionCreatedByUser", "=", false);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "manuallyMatchedTransactions":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNMATCHED_EXPENSES);
                query.filterBy("transactionCreatedByUser", "=", true);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "maidsAtExpense":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.MAIDS_AT_EXPENSE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", false);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "alreadyMatchedMaidsAtExpense":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.MAIDS_AT_EXPENSE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", true);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "unmatchedMaidsAtExpense":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.MAIDS_AT_EXPENSE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.UNMATCHED_MAIDS_AT_EXPENSE);
                query.filterBy("transactionCreatedByUser", "=", false);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable)
                        .map(obj -> projectionFactory.createProjection(
                                ExpenseAndBucketTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
        }

        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bankStatementFile','expensesTransactionsCSV')")
    @RequestMapping("/{id}/expensesTransactionsCSV")
    public void getExpensesTransactionsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType) throws IOException {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        query.leftJoin("payment");
        //Jirra ACC-2545
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        String[] headerList = null;

        switch (gridType) {
            case "matchedTransactionsClientRefunds":// Matched Expenses Client Refund
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", false);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "expense", "paymentId", "clientName", "description", "paymentDetail"};

                break;

            case "alreadyMatchedTransactionsClientRefunds":// Matched Expenses Client Refund
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", true);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "expense", "description", "paymentId", "clientName", "paymentDetail"};

                break;
            case "unmatchedTransactionsClientRefunds": // Unmatched Expenses Client Refund
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                List<BankTransactionStatus> unmatchedStatuses = Arrays.asList(BankTransactionStatus.UNMATCHED_CLIENT_NOT_FOUND, BankTransactionStatus.UNMATCHED_PAYMENT_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "description", "reason", "paymentDetail"};

                break;
            case "otherExpenses": // Other Expenses
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.OTHER_EXPENSES);
                query.filterBy("resolved", "=", false);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "forName", "expense", "description", "paymentDetail"};

                break;
            case "alreadyMatchedOtherExpenses": // Other Expenses
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.EXPENSES);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.OTHER_EXPENSES);
                query.filterBy("resolved", "=", true);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "forName", "expense", "description", "paymentDetail"};

                break;
            case "bucketRefills": // Bucket Refills
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.BUCKET_REFILL);
                query.filterBy("resolved", "=", false);

                headerList = new String[]{"date", "transactionAmount", "fromBucket", "toBucket", "vatAmount", "vatType", "paymentDetail"};

                break;
            case "alreadyMatchedBucketRefills": // Bucket Refills
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.BUCKET_REFILL);
                query.filterBy("resolved", "=", true);

                headerList = new String[]{"date", "transactionAmount", "fromBucket", "toBucket", "vatAmount", "vatType", "paymentDetail"};

                break;
            case "unmatchedTransactions": // Unmatched Expenses Transactions
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNMATCHED_EXPENSES);
                query.filterBy("transactionCreatedByUser", "=", false);

                headerList = new String[]{"date", "transactionAmount", "paymentDetail", "Note"};

                break;

            case "manuallyMatchedTransactions": // Unmatched Expenses Transactions
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNMATCHED_EXPENSES);
                query.filterBy("transactionCreatedByUser", "=", true);

                headerList = new String[]{"transactionAmount", "expense", "vatAmount", "vatType", "description", "paymentDetail"};

                break;
            case "maidsAtExpense":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.MAIDS_AT_EXPENSE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", false);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "forName", "expense", "description", "paymentDetail"};

                break;
            case "alreadyMatchedMaidsAtExpense":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.MAIDS_AT_EXPENSE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);
                query.filterBy("resolved", "=", true);

                headerList = new String[]{"date", "transactionAmount", "vatAmount", "vatType", "forName", "expense", "description", "paymentDetail"};

                break;
            case "unmatchedMaidsAtExpense":
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.MAIDS_AT_EXPENSE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.UNMATCHED_MAIDS_AT_EXPENSE);
                query.filterBy("transactionCreatedByUser", "=", false);

                headerList = new String[]{"date", "transactionAmount", "paymentDetail", "Note"};

                break;
        }
        if (query == null)
            throw new RuntimeException("wrong gridType sent");


        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), DDAndChequeAndPDCTransactionCSVProjection.class, headerList);

            createDownloadResponse(response, "Expenses_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }


    @PreAuthorize("hasPermission('bankStatementFile','undefinedTransactions')")
    @RequestMapping("/{id}/undefinedTransactions")
    public ResponseEntity getUndefinedTransactions(@PathVariable("id") BankStatementFile file,
                                                   @RequestParam(name = "gridType") String gridType,
                                                   Pageable pageable) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        query.leftJoin("bankStatementRecord");
        query.leftJoin("payment");
        //Jirra ACC-2545
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        AggregateQuery totalAmountQuery;
        Double totalAmount;
        PageImpl page;
        AccountingPage accountingPageResult = null;
        switch (gridType) {
            case "creditTransactions": {

                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNDEFINED_TRANSACTION);
                query.filterBy("transactionCreatedByUser", "=", false);
                query.filterBy("bankStatementRecord.creditAmount", ">", 0.0);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        UndefinedTransactionProjection.class, obj));
                accountingPageResult =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);

                break;
            }
            case "debitTransactions": {

                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNDEFINED_TRANSACTION);
                query.filterBy("transactionCreatedByUser", "=", false);
                query.filterBy("bankStatementRecord.debitAmount", ">", 0.0);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        UndefinedTransactionProjection.class, obj));
                accountingPageResult =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);

                break;
            }
            case "manuallyMatchedTransactions": {

                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNDEFINED_TRANSACTION);
                query.filterBy("transactionCreatedByUser", "=", true);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        ManuallyMatchedTransactionProjection.class, obj));
                accountingPageResult =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);

                break;
            }
        }

        return new ResponseEntity(accountingPageResult, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','undefinedTransactionsCSV')")
    @RequestMapping("/{id}/undefinedTransactionsCSV")
    public void getUndefinedTransactionsCSV(
            HttpServletResponse response,
            @RequestParam(name = "gridType") String gridType,
            @PathVariable("id") BankStatementFile file) throws IOException {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        query.leftJoin("bankStatementRecord");
        query.leftJoin("payment");
        //Jirra ACC-2545
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        Class prjectionClass = null;
        String[] headerList = null;

        switch (gridType) {
            case "creditTransactions": {
                // Undefined Transactions
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNDEFINED_TRANSACTION);
                query.filterBy("transactionCreatedByUser", "=", false);
                query.filterBy("bankStatementRecord.creditAmount", ">", 0.0);

                prjectionClass = UndefinedTransactionProjection.class;
                headerList = new String[]{"date", "transactionAmount", "paymentDetail"};
                break;
            }
            case "debitTransactions": {
                // Undefined Transactions
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNDEFINED_TRANSACTION);
                query.filterBy("transactionCreatedByUser", "=", false);
                query.filterBy("bankStatementRecord.debitAmount", ">", 0.0);

                prjectionClass = UndefinedTransactionProjection.class;
                headerList = new String[]{"date", "transactionAmount", "paymentDetail"};

                break;
            }
            case "manuallyMatchedTransactions": {

                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.UNDEFINED_TRANSACTION);
                query.filterBy("transactionCreatedByUser", "=", true);

                prjectionClass = ManuallyMatchedTransactionProjection.class;
                headerList = new String[]{"transactionAmount", "expense", "vatAmount", "vatType", "description", "paymentDetail"};

                break;
            }
        }

        if (query == null)
            throw new RuntimeException("wrong gridType");


        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), prjectionClass, headerList);

            createDownloadResponse(response, "undefined_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }



    @PreAuthorize("hasPermission('bankStatementFile','directDebitTransactions')")
    @RequestMapping("/{id}/directDebitTransactions")
    public ResponseEntity getDirectDebitTransaction(
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType,
            Pageable pageable) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<BankStatementTransaction> query;
        AggregateQuery totalAmountQuery;
        Double totalAmount;
        Page<BankStatementTransaction> page;
        AccountingPage accountingPage = null;
        List<BankStatementTransaction> bankStatementTransactions;
        switch (gridType) {
            case "matchedTransactionReceivedPayment": // received DD
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter = new SelectFilter("resolved", "=", false)
                        .and("payment.status", "!=", PaymentStatus.RECEIVED);

                query.filterBy(filter);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();

                page = query.execute(pageable);

                bankStatementTransactions = page.getContent().stream()
                        .collect(Collectors.groupingBy(BankStatementTransaction::getDirectDebitFile, Collectors.groupingBy(BankStatementTransaction::getDate,
                                Collectors.reducing((left, right) -> {
                                    left.setTransactionAmount(left.getTransactionAmount() + right.getTransactionAmount());
                                    left.setDescription((left.getDescription() == null || left.getDescription().isEmpty() ?
                                            "" : left.getDescription() + "\n") + right.getDescription()); //ACC-4876
                                    return  left; }))))
                        .values().stream()
                        .flatMap(m -> m.values().stream())
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());

                accountingPage = new AccountingPage(bankStatementTransactions.stream().map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj)).collect(Collectors.toList()),
                        pageable, totalAmount, page.getTotalElements());
                break;
            case "alreadyMatchedTransactionReceivedPayment": // received DD
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter1 = new SelectFilter("resolved", "=", true)
                        .or("payment.status", "=", PaymentStatus.RECEIVED);

                query.filterBy(filter1);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();

                page = query.execute(pageable);
                accountingPage = new AccountingPage(page.getContent().stream().map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj)).collect(Collectors.toList()), pageable, page.getTotalElements(), totalAmount);
                break;
            case "matchedTransactionBouncedPayment": // bounced DD

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                SelectFilter filter2 = new SelectFilter("resolved", "=", false)
//                        .and("payment.status", "!=", PaymentStatus.BOUNCED);
//                query.filterBy(filter2);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = query.execute(pageable);
                bankStatementTransactions = page.getContent().stream()
                        .collect(Collectors.groupingBy(BankStatementTransaction::getDirectDebitFile, Collectors.groupingBy(BankStatementTransaction::getDate,
                                Collectors.reducing((left, right) -> {
                                    left.setTransactionAmount(left.getTransactionAmount() + right.getTransactionAmount());
                                    left.setDescription((left.getDescription() == null || left.getDescription().isEmpty() ?
                                            "" : left.getDescription() + "\n") + right.getDescription()); //ACC-4876
                                    return  left; }))))
                        .values().stream()
                        .flatMap(m -> m.values().stream())
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());

                accountingPage = new AccountingPage(bankStatementTransactions.stream().map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj)).collect(Collectors.toList()),
                        pageable, totalAmount, page.getTotalElements());
                break;
            case "alreadyMatchedTransactionBouncedPayment": // bounced DD
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", true);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                SelectFilter filter3 = new SelectFilter("resolved", "=", true)
//                        .or("payment.status", "=", PaymentStatus.BOUNCED);
//                query.filterBy(filter3);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);

                break;
            case "unmatchedReceivedTransactionFromBankStatement": { // Unmatched from bank statement
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                List<BankTransactionStatus> unmatchedStatuses =
                        Arrays.asList(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND
                        );

                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            }
            case "unmatchedBouncedTransactionFromBankStatement": { // Unmatched from bank statement
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                List<BankTransactionStatus> unmatchedStatuses =
                        Arrays.asList(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND
                        );

                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            }
        }
        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','bankTransferTransactions')")
    @RequestMapping("/{id}/bankTransferTransactions")
    public ResponseEntity getBankTransferTransactions(@PathVariable("id") BankStatementFile file, @RequestParam(name = "gridType") String gridType, Pageable pageable) {
        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<BankStatementTransaction> query;
        AggregateQuery totalAmountQuery;
        Double totalAmount;
        PageImpl page;
        AccountingPage accountingPage = null;
        switch (gridType) {
            case "bankTransferMatchedTransaction":
                query = new SelectQuery(BankStatementTransaction.class);
                query.leftJoin("expensePayment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "in", Arrays.asList(BankTransactionType.EXPENSE_REQUEST, BankTransactionType.REPLENISHMENT_REQUEST));
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.MATCHED);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        BankTransferTransactionProjection.class, obj));
                accountingPage = new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
        }

        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bankStatementFile','getDirectDebitTransactionCSV')")
    @RequestMapping("/{id}/directDebitTransactionsCSV")
    public void getDirectDebitTransactionCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType) throws IOException {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<BankStatementTransaction> query = null;

        String[] headerList = null;

        Class projectionClass = null;

        switch (gridType) {
            case "matchedTransactionReceivedPayment": // received DD
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter = new SelectFilter("resolved", "=", false)
                        .and("payment.status", "!=", PaymentStatus.RECEIVED);
                query.filterBy(filter);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "applicationId", "clientName",
                        "revenue", "paymentStatus", "description", "bankTransactionMatchType", "paymentDetail"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;

                break;
            case "alreadyMatchedTransactionReceivedPayment": // received DD
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter1 = new SelectFilter("resolved", "=", true)
                        .or("payment.status", "=", PaymentStatus.RECEIVED);
                query.filterBy(filter1);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "applicationId", "clientName",
                        "revenue", "paymentStatus", "description", "paymentDetail"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;

                break;
            case "matchedTransactionBouncedPayment": // bounced DD

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "applicationId", "paymentDetail", "BouncedPaymentId", "paymentStatus", "clientName"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;


                break;
            case "alreadyMatchedTransactionBouncedPayment": // bounced DD
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", true);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "paymentDetail", "BouncedPaymentId", "clientName",
                        "applicationId"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;

                break;
            case "unmatchedReceivedTransactionFromBankStatement": { // Unmatched from bank statement
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                List<BankTransactionStatus> unmatchedStatuses
                        = Arrays.asList(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                headerList = new String[]{"date", "transactionAmount", "paymentDetail", "reason", "description", "Note"};

                projectionClass = DDUnMatchedTransactionCSVProjection.class;

                break;
            }
            case "unmatchedBouncedTransactionFromBankStatement": { // Unmatched from bank statement
                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.DIRECT_DEBIT);
                List<BankTransactionStatus> unmatchedStatuses
                        = Arrays.asList(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                headerList = new String[]{"date", "transactionAmount", "paymentDetail", "reason", "Note"};

                projectionClass = DDUnMatchedTransactionCSVProjection.class;

                break;
            }
        }

        if (query == null)
            throw new RuntimeException("wrong gridType sent");

        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), projectionClass, headerList);

            createDownloadResponse(response, "DirectDebit_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }


    @PreAuthorize("hasPermission('bankStatementFile','directDebitTransactions')")
    @RequestMapping("/{id}/chequeDepositTransactions")
    public ResponseEntity getChequeDepositTransaction(@PathVariable("id") BankStatementFile file, @RequestParam(name = "gridType") String gridType, Pageable pageable) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<BankStatementTransaction> query;
        AggregateQuery totalAmountQuery;
        Double totalAmount;
        PageImpl page;
        AccountingPage accountingPage = null;

        switch (gridType) {
            case "matchedTransactionReceivedCheques": // received cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter = new SelectFilter("resolved", "=", false)
                        .and("payment.status", "!=", PaymentStatus.RECEIVED);
                query.filterBy(filter);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;

            case "alreadyMatchedTransactionReceivedCheques": // received cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter1 = new SelectFilter("resolved", "=", true)
                        .or("payment.status", "=", PaymentStatus.RECEIVED);
                query.filterBy(filter1);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "matchedTransactionBouncedCheques": // bounced cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                SelectFilter filter2 = new SelectFilter("resolved", "=", false)
//                        .and("payment.status", "!=", PaymentStatus.BOUNCED);
//                query.filterBy(filter2);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;

            case "alreadyMatchedTransactionBouncedCheques": // bounced cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", true);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                SelectFilter filter3 = new SelectFilter("resolved", "=", true)
//                        .or("payment.status", "=", PaymentStatus.BOUNCED);
//                query.filterBy(filter3);

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;

            case "unmatchedReceivedTransactionFromBankStatement": {// Unmatched cheque from bank statement

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                List<BankTransactionStatus> unmatchedStatuses
                        = Arrays.asList(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            }
            case "unmatchedBouncedTransactionFromBankStatement": { // Unmatched cheque from bank statement

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                List<BankTransactionStatus> unmatchedStatuses
                        = Arrays.asList(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            }
        }
        return new ResponseEntity(accountingPage, HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('bankStatementFile','chequeDepositTransactionsCSV')")
    @RequestMapping("/{id}/chequeDepositTransactionsCSV")
    public void chequeDepositTransactionsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType) throws IOException {


        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<BankStatementTransaction> query = null;

        String[] headerList = null;
        Class projectionClass = null;

        switch (gridType) {
            case "matchedTransactionReceivedCheques": // received cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter = new SelectFilter("resolved", "=", false)
                        .and("payment.status", "!=", PaymentStatus.RECEIVED);
                query.filterBy(filter);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                headerList = new String[]{"bankName", "chequeName", "chequeNumber", "status", "creationDate",
//                        "contractName", "paymentName", "clientName", "contractProspectType",
//                        "typeOfPayment", "methodOfPayment", "amountOfPayment", "dateOfPayment", "vatPaidByClient", "Note"};
//
//                projectionClass = ChequeTransactionCSVProjection.class;

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "chequeNumberCSV", "chequeName", "revenue", "description",
                        "clientName", "bankTransactionMatchType", "paymentDetail"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;


                break;

            case "alreadyMatchedTransactionReceivedCheques": // received cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.RECEIVED);

                SelectFilter filter1 = new SelectFilter("resolved", "=", true)
                        .or("payment.status", "=", PaymentStatus.RECEIVED);
                query.filterBy(filter1);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                headerList = new String[]{"bankName", "chequeName", "chequeNumber", "status", "creationDate",
//                        "contractName", "paymentName", "clientName", "contractProspectType",
//                        "typeOfPayment", "methodOfPayment", "amountOfPayment", "dateOfPayment", "vatPaidByClient", "Note"};
//
//                projectionClass = ChequeTransactionCSVProjection.class;

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "chequeNumberCSV", "chequeName", "revenue", "paymentStatus", "description",
                        "clientName", "paymentDetail", "bankTransactionMatchType"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;

                break;
            case "matchedTransactionBouncedCheques": // bounced cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", false);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                SelectFilter filter2 = new SelectFilter("resolved", "=", false)
//                        .and("payment.status", "!=", PaymentStatus.BOUNCED);
//                query.filterBy(filter2);

//                headerList = new String[]{"bankName", "chequeName", "chequeNumber", "status", "creationDate",
//                        "contractName", "paymentName", "clientName", "reasonOfBouncingCheque", "contractProspectType",
//                        "typeOfPayment", "methodOfPayment", "amountOfPayment", "dateOfPayment", "vatPaidByClient", "Note"};
//
//                projectionClass = ChequeTransactionCSVProjection.class;

                // acc-2343
                headerList = new String[]{"date", "chequeNumberCSV", "chequeName", "amountOfPayment", "paymentDetail", "BouncedPaymentId", "clientName"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;

                break;

            case "alreadyMatchedTransactionBouncedCheques": // bounced cheque

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                query.filterBy("bankTransactionStatus", "=", BankTransactionStatus.BOUNCED);
                query.filterBy("resolved", "=", true);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

//                SelectFilter filter3 = new SelectFilter("resolved", "=", true)
//                        .or("payment.status", "=", PaymentStatus.BOUNCED);
//                query.filterBy(filter3);

//                headerList = new String[]{"bankName", "chequeName", "chequeNumber", "status", "creationDate",
//                        "contractName", "paymentName", "clientName", "reasonOfBouncingCheque", "contractProspectType",
//                        "typeOfPayment", "methodOfPayment", "amountOfPayment", "dateOfPayment", "vatPaidByClient", "Note"};
//
//                projectionClass = ChequeTransactionCSVProjection.class;

                // acc-2343
                headerList = new String[]{"date", "amountOfPayment", "chequeNumberCSV", "chequeName", "paymentDetail", "BouncedPaymentId", "clientName"};

                projectionClass = DDAndChequeAndPDCTransactionProjection.class;

                break;

            case "unmatchedReceivedTransactionFromBankStatement": { // Unmatched cheque from bank statement

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                List<BankTransactionStatus> unmatchedStatuses
                        = Arrays.asList(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                // acc-2343
//                headerList = new String[]{"date", "transactionAmount", "paymentDetail", "reason"};
                headerList = new String[]{"date", "transactionAmount", "chequeNumberCSV", "paymentDetail", "description", "reason"};

                projectionClass = DDUnMatchedTransactionCSVProjection.class;

                break;
            }

            case "unmatchedBouncedTransactionFromBankStatement": { // Unmatched cheque from bank statement

                query = new SelectQuery<>(BankStatementTransaction.class);
                query.leftJoin("payment");
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.CHEQUE);
                List<BankTransactionStatus> unmatchedStatuses
                        = Arrays.asList(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND);
                query.filterBy("bankTransactionStatus", "in", unmatchedStatuses);

                //Jirra ACC-2545
                query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

                // acc-2343
//                headerList = new String[]{"date", "transactionAmount", "paymentDetail", "reason"};
                headerList = new String[]{"date", "transactionAmount", "chequeNumberCSV", "paymentDetail", "reason"};

                projectionClass = DDUnMatchedTransactionCSVProjection.class;

                break;
            }
        }

        if (query == null)
            throw new RuntimeException("wrong gridType sent");


        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), projectionClass, headerList);

            createDownloadResponse(response, "ChequeDeposit_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('bankStatementFile','directDebitTransactions')")
    @RequestMapping("/{id}/pdcPaymentTransactions")
    public ResponseEntity getPDCPaymentTransaction(@PathVariable("id") BankStatementFile file,
                                                   @RequestParam(name = "gridType") String gridType, Pageable pageable) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        query.leftJoin("payment");

        //Jirra ACC-2545
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        PageImpl page;
        Double totalAmount;
        AccountingPage accountingPage = null;

        switch (gridType) {
            case "matchedPdcPaymentTransactions": {
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.PDC);
                query.filterBy("resolved", "=", false);

                AggregateQuery totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            }
            case "alreadyMatchedPdcPaymentTransactions": {

                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.PDC);
                query.filterBy("resolved", "=", true);

                AggregateQuery totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "transactionAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        DDAndChequeAndPDCTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            }
        }

        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bankStatementFile','pdcPaymentTransactionsCSV')")
    @RequestMapping("/{id}/pdcPaymentTransactionsCSV")
    public void getPDCPaymentTransactionCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType) throws IOException {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<BankStatementTransaction> query = new SelectQuery<>(BankStatementTransaction.class);
        query.leftJoin("payment");

        //Jirra ACC-2545
        query.filterBy("date", ">=", java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));


        String[] headerList = null;

        switch (gridType) {
            case "matchedPdcPaymentTransactions": {
                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.PDC);
                query.filterBy("resolved", "=", false);

                headerList = new String[]{"date", "transactionAmount", "expense", "description", "paymentDetail"};

                break;
            }
            case "alreadyMatchedPdcPaymentTransactions": {

                query.filterBy("file", "=", file);
                query.filterBy("bankTransactionType", "=", BankTransactionType.PDC);
                query.filterBy("resolved", "=", true);

                headerList = new String[]{"date", "transactionAmount", "expense", "description", "paymentDetail"};
                break;
            }
        }

        if (query == null)
            throw new RuntimeException("wrong gridType sent");


        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), DDAndChequeAndPDCTransactionCSVProjection.class, headerList);

            createDownloadResponse(response, "pdcPayment_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('bankStatementFile','unMatchedDirectDebitTransactionsFromErp')")
    @RequestMapping("/{id}/unMatchedDirectDebitTransactionsFromErp")
    public ResponseEntity getUnMatchedDirectDebitFromERPTransaction(@PathVariable("id") BankStatementFile file, Pageable pageable) {
        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        //Jirra ACC-2545
        if (file.getReportDate().before(new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate()))
            return new ResponseEntity(new AccountingPage(new ArrayList(), pageable, 0L, 0D), HttpStatus.OK);

        // get unmatched payment in ERP which have same date of this file
        Page<Payment> paymentPage = paymentRepository.findByFileAndMatchedWithDirectDebit(file.getId(), file.getReportDate(), pageable);

        // get ids of payment form transactions (matched payment in this file)
//        List<Long> paymentsIds = transactions.stream().map(b -> b.getPayment().getId()).collect(Collectors.toList());

        // get unmatched payment in ERP which have same date of this file
//        SelectQuery<Payment> paymentQuery = new SelectQuery<>(Payment.class);
//        paymentQuery.filterBy("dateOfPayment", "=", file.getReportDate());
//        if (paymentsIds != null && !paymentsIds.isEmpty())
//            paymentQuery.filterBy("id", "not in", paymentsIds);

//        AggregateQuery totalAmountQuery = new AggregateQuery(paymentQuery, Aggregate.Sum, "amountOfPayment");
//        Double totalAmount = totalAmountQuery.execute().doubleValue();
        Double totalAmount = paymentRepository.sumAmountOfPaymentByFileAndMatchedWithDirectDebit(file.getId(), file.getReportDate());
        List result = paymentPage.getContent().stream().map(obj
                -> projectionFactory.createProjection(UnmatchedDDFromErpProjection.class, obj)).collect(Collectors.toList());
        AccountingPage accountingPage =
                new AccountingPage(result, pageable, paymentPage.getTotalElements(), totalAmount);

        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','unMatchedDirectDebitTransactionsFromErpCSV')")
    @RequestMapping("/{id}/unMatchedDirectDebitTransactionsFromErpCSV")
    public void getUnMatchedDirectDebitFromERPTransactionCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file) throws IOException {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        // get matched bank statement transactions of this file
//        List<BankStatementTransaction> transactions = bankStatementTransactionRepository.findByFileAndMatchedWithDirectDebit(file.getId());

        // get ids of payment form transactions (matched payment in this file)
//        List<Long> paymentsIds = transactions.stream().map(b -> b.getPayment().getId()).collect(Collectors.toList());

        // get unmatched payment in ERP which have same date of this file
//        SelectQuery<Payment> paymentQuery = new SelectQuery<>(Payment.class);
//        paymentQuery.filterBy("dateOfPayment", "=", file.getReportDate());
//        if (paymentsIds != null && !paymentsIds.isEmpty())
//            paymentQuery.filterBy("id", "not in", paymentsIds);


        // get unmatched payment in ERP which have same date of this file
        Page<Payment> paymentPage = paymentRepository.findByFileAndMatchedWithDirectDebit(file.getId(), file.getReportDate(), null);

        String[] headerList = new String[]{"dateOfPayment", "amountOfPayment", "clientName", "contractId"};


        InputStream is = null;
        
        try {
            is = generateCsv(paymentPage.getContent(), UnmatchedDDFromErpProjection.class, headerList);

            createDownloadResponse(response, "DirectDebit_" + "unMatchedDirectDebitTransactionsFromErp" + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @Transactional
    @PreAuthorize("hasPermission('bankStatementFile','confirmAndSaveTransactions')")
    @RequestMapping(value = "/{id}/confirmAndSaveTransactions", method = RequestMethod.POST)
    public ResponseEntity confirmAndSaveTransactions(@PathVariable("id") BankStatementFile file, @RequestBody List<Transaction> transactions) {
        logger.log(Level.SEVERE, "Enter confirmAndSaveTransactions for " + transactions.size() + " transactions. ");
        if (transactions != null && !transactions.isEmpty()) {

            transactions.forEach(t -> {
                logger.log(Level.SEVERE, "#confirmAndSaveTransactions, trying to create a transaction at  " + transactions.indexOf(t) + ", with amount " + t.getAmount());
                t.setPaymentType(PaymentMethod.CARD);
                t.setTransactionType(TransactionEntityType.UNKNOWN);
                t.setAutomatic(true);
                t.setToBucket(bucketRepository.findByCode("BC 10"));//Mustaqeem ADCB
                //ACC-1995
                t.setBankStatementFile(file);
                transactionsController.createEntity(t);
            });
            List<Long> TransactionsIds = transactions.stream().filter(t -> t.getId() != null)
                    .map(t -> t.getId()).collect(Collectors.toList());
            return new ResponseEntity(TransactionsIds, HttpStatus.OK);
        }
        return new ResponseEntity("no received transactions!", HttpStatus.BAD_REQUEST);
    }

    //ACC-1995
    @PreAuthorize("hasPermission('bankStatementFile','unMatchedDirectDebitTransactionsFromErp')")
    @RequestMapping("/{id}/getAddedTadbeerTransactions")
    public ResponseEntity getAddedTadbeerTransactions(@PathVariable("id") BankStatementFile file) {
        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        List<Transaction> transactions = Setup.getRepository(TransactionRepository.class).findByBankStatementFileAndDateGreaterThanEqual(file, new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate());
        return new ResponseEntity<>(
                transactions.stream().map(obj -> projectionFactory.createProjection(TransactionProjection.class, obj)).collect(Collectors.toList()),
                HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bankStatementFile','getAddedTadbeerTransactionsCSV')")
    @RequestMapping("/{id}/getAddedTadbeerTransactionsCSV")
    public void getAddedTadbeerTransactionsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file) throws IOException {
        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        List<Transaction> transactions = Setup.getRepository(TransactionRepository.class).findByBankStatementFileAndDateGreaterThanEqual(file, new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate());

        String[] headerList = new String[]{"amount", "revenueCode", "vatAmount", "vatType", "description", "transaction"};


        InputStream is = null;
        
        try {
            is = generateCsv(transactions, TransactionStatementCSVProjection.class, headerList);

            createDownloadResponse(response, "Tadbeer.csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }


    @PreAuthorize("hasPermission('bankStatementFile','creditCardCommission')")
    @RequestMapping("/{id}/creditCardCommission")
    public ResponseEntity getCreditCardCommission(@PathVariable("id") BankStatementFile file) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        Long totalTransactions = file.getTotalCreditCardTransactions();
        Double totalTransactionsAmount = file.getTotalCreditCardAmount();
        Double totalCommission = file.getTotalCommission();
        Double totalVatOnCommission = file.getTotalVatOnCommission();


//      Double commissionPerTransaction = file.getCommissionPerTransaction();
//      res.put("commissionPerTransaction", commissionPerTransaction.longValue());

        Double comissionPercent = (totalCommission / totalTransactionsAmount) * 100;

        Map<String, Object> res = new HashMap<>();
        DecimalFormat decimalFormat = new DecimalFormat("#,###,##0.00");
        res.put("commissionPerTransaction", decimalFormat.format(comissionPercent) + "%");
        res.put("totalCommission", totalCommission.longValue());
        res.put("totalVatOnCommission", totalVatOnCommission.longValue());
        res.put("totalTransactions", totalTransactions);
        res.put("totalAmount", df.format(totalTransactionsAmount));

        return new ResponseEntity(res, HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bankStatementFile','creditCardAmount')")
    @RequestMapping("/{id}/creditCardAmount")
    public ResponseEntity getCreditCardAmount(@PathVariable("id") BankStatementFile file) {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        Map<String, Object> res = new HashMap<>();

        Double tadbeerTransactionAmount = file.getTadbeerTransactionAmount();
        Double tadbeerTransactionCount = file.getTadbeerTransactionCount();

        res.put("tadbeerTransactionAmount", tadbeerTransactionAmount.longValue());
        res.put("tadbeerTransactionCount", tadbeerTransactionCount.longValue());

        Double storageTransactionAmount = file.getStorageTransactionAmount();
        Double storageTransactionCount = file.getStorageTransactionCount();

        res.put("storageTransactionAmount", storageTransactionAmount.longValue());
        res.put("storageTransactionCount", storageTransactionCount.longValue());

        Double totalPosSettlementAmountFromBankStatement = file.getTotalPosSettlementAmount();
        res.put("totalPosSettlementAmountFromBankStatement", totalPosSettlementAmountFromBankStatement.longValue());

        Double totalMaidsTransactionsAmountFromCreditCardStatement = file.getTotalMaidsTransactionsAmount();
        Double totalMaidsTransactionsCountFromCreditCardStatement = file.getTotalMaidsTransactionsCount();

        res.put("totalMaidsTransactionsAmountFromCreditCardStatement", totalMaidsTransactionsAmountFromCreditCardStatement.longValue());
        res.put("totalMaidsTransactionsCountFromCreditCardStatement", totalMaidsTransactionsCountFromCreditCardStatement.longValue());

        res.put("minDateOfTadbeerTransaction", creditCardStatementRecordRepository.findMinDate(file.getId(), tadbeerTransactionTerminalIdsList));

        return new ResponseEntity(res, HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bankStatementFile','creditCardTransactions')")
    @RequestMapping("/{id}/creditCardTransactions")
    public ResponseEntity getCreditCardTransactions(@PathVariable("id") BankStatementFile file, @RequestParam(name = "gridType") String gridType, Pageable pageable) throws Exception {

        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        SelectQuery<CreditCardStatementRecord> query;
        AggregateQuery totalAmountQuery;
        Double totalAmount;
        PageImpl page;
        AccountingPage accountingPage = null;

        Date date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        switch (gridType) {
            case "storageTransactions": // storage transactions

                query = new SelectQuery<>(CreditCardStatementRecord.class);
                query.filterBy("bankStatementFile", "=", file);
                query.filterBy("terminalId", "=", storageTransactionTerminalId);
                query.filterBy("transactionDate", ">=", date);
                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "salesAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        CreditCardTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
                break;
            case "tadbeerTransactions": // tadbeer transactions
                query = new SelectQuery<>(CreditCardStatementRecord.class);
                query.filterBy("bankStatementFile", "=", file);
                query.filterBy("terminalId", "in", tadbeerTransactionTerminalIdsList);
                query.filterBy("transactionDate", ">=", date);
                totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "salesAmount");
                totalAmount = totalAmountQuery.execute().doubleValue();
                page = (PageImpl) query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        CreditCardTransactionProjection.class, obj));
                accountingPage =
                        new AccountingPage(page.getContent(), pageable, page.getTotalElements(), totalAmount);
        }
        return new ResponseEntity(accountingPage, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bankStatementFile','creditCardTransactionsCSV')")
    @RequestMapping("/{id}/creditCardTransactionsCSV")
    public void getCreditCardTransactionsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankStatementFile file,
            @RequestParam(name = "gridType") String gridType) throws Exception {

        if (file == null)
            throw new RuntimeException("no stored file with passed (id)!");

        SelectQuery<CreditCardStatementRecord> query = null;

        String[] headerList = null;
        Date date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);

        switch (gridType) {
            case "storageTransactions": // storage transactions

                query = new SelectQuery<>(CreditCardStatementRecord.class);
                query.filterBy("bankStatementFile", "=", file);
                query.filterBy("terminalId", "=", storageTransactionTerminalId);
                query.filterBy("transactionDate", ">=", date);

                headerList = new String[]{"transactionDate", "salesAmount", "terminalId",};

                break;
            case "tadbeerTransactions": // tadbeer transactions
                query = new SelectQuery<>(CreditCardStatementRecord.class);
                query.filterBy("bankStatementFile", "=", file);
                query.filterBy("terminalId", "in", tadbeerTransactionTerminalIdsList);
                query.filterBy("transactionDate", ">=", date);

                headerList = new String[]{"transactionDate", "terminalId", "sequenceNumber", "authCode", "salesAmount"};
        }

        if (query == null)
            throw new RuntimeException("wrong gridType sent");


        InputStream is = null;
        
        try {
            is = generateCsv(query.execute(), CreditCardTransactionProjection.class, headerList);

            createDownloadResponse(response, "CreditCard_" + gridType + ".csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }
//    ==================================================================================================

    @Transactional
    @PreAuthorize("hasPermission('bankStatementFile','matchPaymentToTransaction')")
    @PostMapping("/matchPaymentToTransaction")
    public ResponseEntity<?> matchPaymentToTransaction(
            @RequestParam(name = "transactionId") Long transactionId,
            @RequestParam(name = "paymentId") Long paymentId) throws Exception {

        BankStatementTransaction bankTransaction = bankStatementTransactionRepository.findOne(transactionId);
        Payment payment = paymentRepository.findOne(paymentId);
        if (bankTransaction == null || payment == null) {
            throw new RuntimeException("payment or transaction is empty");
        }

        switch(bankTransaction.getBankTransactionType()) {
            case DIRECT_DEBIT:
                // Payment Not Found ?
                if (bankTransaction.getReason() != null &&
                        bankTransaction.getReason().equals("Contract Not Found")) {

                    bankTransaction.setContract(payment.getContract());
                    bankTransaction.setClient(payment.getContract().getClient().getName());
                }
                bankTransaction.setPayment(payment);

                if (payment.getDirectDebitFile() != null) {
                    bankTransaction.setDirectDebitFile(payment.getDirectDebitFile());
                } else {
                    String paymentDetail = bankTransaction.getPaymentDetail();
                    String applicationId = paymentDetail.substring(paymentDetail.lastIndexOf("_") + 1, paymentDetail.length());
                    applicationId = applicationId.trim();

                    SelectQuery<DirectDebitFile> query = new SelectQuery<>(DirectDebitFile.class);
                    query.filterBy("applicationId", "=", applicationId);
                    query.filterBy("ddStatus", "=", DirectDebitStatus.CONFIRMED);
                    List<DirectDebitFile> directDebitFiles = query.execute();

                    if (!directDebitFiles.isEmpty()) {
                        DirectDebitFile debitFile = directDebitFiles.get(0);
                        bankTransaction.setDirectDebitFile(debitFile);
                    }
                }
                bankTransaction.setPaymentStatus(payment.getStatus());
                bankTransaction.setReason(null);
                bankTransaction.setBankTransactionMatchType(BankTransactionMatchType.Manually);

                switch(bankTransaction.getBankTransactionStatus()) {
                    case UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND:
                        bankTransaction.setBankTransactionStatus(BankTransactionStatus.RECEIVED);
                        bankTransaction.setDescription("Client-" + payment.getContract().getClient().getName()
                                + " / Payment-" + payment.getId()
                                + " / Contract-" + payment.getContract().getId()
                                + " / Date of payment-" + payment.getDateOfPayment());

                        bankStatementFileService.confirmDirectDebitTransaction(bankTransaction);
                        break;
                    case UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND:
                        bankTransaction.setBankTransactionStatus(BankTransactionStatus.BOUNCED);
                        bankTransaction.setBouncedPaymentId(payment.getId());

                        bankStatementFileService.confirmDirectDebitTransaction(bankTransaction);
                        break;
                }
                break;

            case CHEQUE:
                if (bankTransaction.getReason() != null &&
                        bankTransaction.getReason().equals("Payment Not Found")) {

                    bankTransaction.setPayment(payment);

                    if (payment.getContract() != null && payment.getContract().getClient() != null) {
                        bankTransaction.setContract(payment.getContract());
                        bankTransaction.setClient(payment.getContract().getClient().getName());
                    }

                    bankTransaction.setReason(null);
                    bankTransaction.setBankTransactionMatchType(BankTransactionMatchType.Manually);

                    switch(bankTransaction.getBankTransactionStatus()) {
                        case UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND:
                            bankTransaction.setBankTransactionStatus(BankTransactionStatus.RECEIVED);
                            bankTransaction.setDescription("Client-" + payment.getContract().getClient().getName()
                                    + " / Payment-" + payment.getId()
                                    + " / Contract-" + payment.getContract().getId()
                                    + " / Date of payment-" + payment.getDateOfPayment());

                            bankStatementFileService.confirmChequeDepositTransactionForLinkToPaymentAction(bankTransaction);
                            break;
                        case UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND:
                            bankTransaction.setBankTransactionStatus(BankTransactionStatus.BOUNCED);
                            bankTransaction.setBouncedPaymentId(payment.getId());

                            bankStatementFileService.confirmChequeDepositTransactionForLinkToPaymentAction(bankTransaction);
                            break;
                    }
                }
                break;

            case EXPENSES:
                // UNMATCHED_PAYMENT_NOT_FOUND?
                if (bankTransaction.getBankTransactionStatus() == BankTransactionStatus.UNMATCHED_CLIENT_NOT_FOUND) {
                    bankTransaction.setContract(payment.getContract());
                    bankTransaction.setClient(payment.getContract().getClient().getName());
                }

                bankTransaction.setPayment(payment);
                bankTransaction.setPaymentStatus(payment.getStatus());
                bankTransaction.setReason(null);
                bankTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
                bankTransaction.setBankTransactionMatchType(BankTransactionMatchType.Manually);
                bankStatementTransactionRepository.save(bankTransaction);
                break;
        }

        return new ResponseEntity<>("Linked Successfully", HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = {"/addNoteToTransaction"},
            method = {RequestMethod.POST}
    )
    public ResponseEntity<?> addNoteToTransaction(@RequestParam(name = "transactionId") Long transactionId,
                                                  @RequestParam(name = "note") String note) {
        BankStatementTransaction bankTransaction = bankStatementTransactionRepository.findOne(transactionId);
        if (bankTransaction == null) {
            throw new RuntimeException("transaction is empty");
        }
        if (bankTransaction.getBankTransactionType() == BankTransactionType.DIRECT_DEBIT) {
            if (bankTransaction.getReason().equals("Contract Not Found")
                    || bankTransaction.getReason().equals("Payment Not Found")) {

                bankTransaction.setNote(note);
                bankStatementTransactionRepository.save(bankTransaction);

            } else {
                throw new RuntimeException("cannot add note");
            }
        } else if (bankTransaction.getBankTransactionType() == BankTransactionType.UNMATCHED_EXPENSES) {

            bankTransaction.setNote(note);
            bankStatementTransactionRepository.save(bankTransaction);

        } else {
            throw new RuntimeException("cannot add note");
        }
        return new ResponseEntity<>("Added Successfully", HttpStatus.OK);
    }

    @Transactional
    @PreAuthorize("hasPermission('bankStatementFile','confirmAndSaveTransactions')")
    @RequestMapping(value = "/createTransaction/{bankStatementTransaction}", method = RequestMethod.POST)
    public ResponseEntity createTransaction(
            @PathVariable BankStatementTransaction bankStatementTransaction,
            @RequestBody Transaction transaction) {

        if (bankStatementTransaction == null)
            throw new RuntimeException("bankStatementTransaction not founded");

        if (!bankStatementTransaction.getBankTransactionType().equals(BankTransactionType.UNDEFINED_TRANSACTION)
                && !bankStatementTransaction.getBankTransactionType().equals(BankTransactionType.UNMATCHED_EXPENSES))
            throw new RuntimeException("cannot create transaction for this bankStatementTransaction");

        transaction.setBankStatementFile(bankStatementTransaction.getFile());

        if (transaction.getRevenue() != null) {
            transaction.setToBucket(bucketRepository.findByCode("BC 10"));//Mustaqeem ADCB
            bankStatementTransaction.setToBucket(transaction.getToBucket().getName());
        }

        if (transaction.getExpense() != null) {
            transaction.setFromBucket(bucketRepository.findByCode("BC 10"));//Mustaqeem ADCB
            bankStatementTransaction.setFromBucket(transaction.getFromBucket().getName());
        }

        bankStatementTransaction.setRevenue(transaction.getRevenue());
        bankStatementTransaction.setExpense(transaction.getExpense());
        bankStatementTransaction.setVatType(transaction.getVatType());
        bankStatementTransaction.setVatAmount(transaction.getVatAmount());

        ResponseEntity<?> entity = transactionsController.createEntity(transaction);
        if (entity.getBody() instanceof Transaction) {
            Transaction createdTransaction = (Transaction) entity.getBody();
            bankStatementTransaction.setTransaction(createdTransaction);
            if (bankStatementTransaction.getBankTransactionType().equals(BankTransactionType.UNMATCHED_EXPENSES)) {
                bankStatementTransaction.setExpenseCode(createdTransaction.getExpense() != null ? createdTransaction.getExpense().getCode() : "");
                bankStatementTransaction.setDescription(createdTransaction.getDescription());
            }
            bankStatementTransaction.setTransactionCreatedByUser(true);

            bankStatementTransactionRepository.save(bankStatementTransaction);
        }

        return new ResponseEntity("done", HttpStatus.OK);
    }

}
