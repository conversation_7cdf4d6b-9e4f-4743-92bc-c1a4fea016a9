package com.magnamedia.service;

import com.magnamedia.controller.ExpensePaymentController;
import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.ExpensePaymentPayInvoiceDto;
import com.magnamedia.entity.dto.ExpenseRequestPayInvoiceDto;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentBankTransferStep;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentInCreditCardHolderStep;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentInReconciliatorConfirmationStep;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

/**
 * Mohammad Nosairat (Jan 19, 2021)
 */
@Service
public class ExpensePaymentService {
    protected static final Logger logger = Logger.getLogger(ExpensePaymentService.class.getName());


    @Autowired
    private ExpensePaymentBankTransferStep expensePaymentBankTransferStep;
    @Autowired
    private ExpensePaymentInReconciliatorConfirmationStep expensePaymentInReconciliatorConfirmationStep;
    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;
    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;
    @Autowired
    private SupplierRepository supplierRepository;
    @Autowired
    private HousemaidService houseMaidService;
    @Autowired
    private NewVisaRequestRepository newVisaRequestRepository;
    @Autowired
    private TelecomPhoneBillRepository telecomPhoneBillRepository;
    @Autowired
    private ExpensePaymentController expensePaymentController;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private InsuranceBalanceLogRepository insuranceBalanceLogRepository;

    public void initExpensePaymentFlow(ExpensePayment expensePayment) {
        if (expensePayment.getStatus().equals(ExpensePaymentStatus.PAID)
                || expensePayment.getStatus().equals(ExpensePaymentStatus.PAID_PENDING_INVOICE)) {

            if (expensePayment.getConfirmed() != null && expensePayment.getConfirmed().equals(Boolean.TRUE))
                return;


            if (!expensePayment.getMethod().equals(ExpensePaymentMethod.CASH)
                    && !expensePayment.getMethod().equals(ExpensePaymentMethod.SALARY))
                return;
        }

        switch (expensePayment.getMethod()) {
            case CASH: {
                initCashPaymentType(expensePayment);
                break;
            }
            case BANK_TRANSFER:
                expensePayment.setTaskName(
                        ExpensePaymentToDoType.TO_DO_BANK_TRANSFER_SCREEN.toString());
                expensePaymentBankTransferStep.initBankTransferTask(expensePayment);
                break;
            case MONEY_TRANSFER:
                expensePayment.setTaskName(
                        ExpensePaymentToDoType.TO_DO_BANK_TRANSFER_SCREEN.toString());
                expensePaymentBankTransferStep.initMoneyTransferTask(expensePayment);
                break;
            case CREDIT_CARD:
                expensePayment.setTaskName(
                        ExpensePaymentToDoType.TO_DO_IN_CREDIT_CARD_HOLDER_SCREEN.toString());
                break;
            case CHEQUE:
                break;
            case SALARY:
                initSalaryPaymentType(expensePayment);
                break;
            case INVOICED:
                expensePayment.setTaskName("");
                break;

        }
        expensePaymentRepository.save(expensePayment);
    }

    public void initSalaryPaymentType(ExpensePayment expensePayment) {
        expensePayment.setTaxable(Boolean.FALSE);
        if (expensePayment.getRequiresInvoice() != null && expensePayment.getRequiresInvoice().equals(Boolean.TRUE)) {
            expensePayment.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
            expensePayment.setTaskName(ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString());
        } else {
            expensePayment.setStatus(ExpensePaymentStatus.PAID);
            if (expensePayment.getAttachments() == null || expensePayment.getAttachments().isEmpty())
                expensePaymentInReconciliatorConfirmationStep.reconciliatorConfirmationStep(expensePayment);
            else
                expensePayment.setTaskName(ExpensePaymentToDoType.TO_DO_IN_RECONCILIATOR_CONFIRMATION_SCREEN.toString());
        }
    }

    public void initCashPaymentType(ExpensePayment expensePayment) {
        switch (expensePayment.getStatus()) {
            case PENDING:
                expensePayment.setTaskName(
                        ExpensePaymentToDoType.TO_DO_IN_PENDING_PAYMENT_CASHIER_SCREEN.toString());
                break;
            case PAID_PENDING_INVOICE:
                expensePayment.setTaskName(
                        ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString());
                break;
            case PAID:
                expensePayment.setTaskName(
                        ExpensePaymentToDoType.TO_DO_IN_RECONCILIATOR_CONFIRMATION_SCREEN.toString());
                break;
            default:
                break;
        }
    }

    public void confirmSalaryPayment(ExpensePayment expensePayment) {
        expensePayment.setStatus(ExpensePaymentStatus.PAID);
    }


    public ExpensePayment generateInvoicePayment(ExpensePaymentPayInvoiceDto payInvoiceDto, Bucket bucket) {
        ExpenseRequestTodo requestToDo = expenseRequestTodoRepository
                .findOne(payInvoiceDto.getExpenseRequestPayInvoiceDtos().get(0).getId());
        Supplier supplier = null;
        if (requestToDo != null
                && requestToDo.getBeneficiaryType() != null
                && requestToDo.getBeneficiaryType().equals(ExpenseBeneficiaryType.SUPPLIER)
                && requestToDo.getBeneficiaryId() != null)
            supplier = supplierRepository.findOne(requestToDo.getBeneficiaryId());
        ExpensePayment payment = fillPaymentAttributesFromRequest(requestToDo);
        payment.setExpenseToPost(requestToDo.getExpense());
        payment.setMethod(ExpensePaymentMethod.BANK_TRANSFER);
        Double amount = payInvoiceDto.getExpenseRequestPayInvoiceDtos().stream()
                .map(ExpenseRequestPayInvoiceDto::getAmount)
                .reduce(0D, Double::sum);
        payment.setAmount(amount);
        payment.setFromBucket(bucket);
        payment.setStatus(ExpensePaymentStatus.PENDING);
        payment.setConfirmed(Boolean.FALSE);
        payment.setType(ExpensePaymentType.PAY);
        payment.setTaxable(payInvoiceDto.getTaxable());
        payment.setVatAmount(payInvoiceDto.getVatAmount());
        payment.setExpenseRequestTodos(payInvoiceDto.getExpenseRequestPayInvoiceDtos().stream()
                .map(t -> {
                    ExpenseRequestTodo e = new ExpenseRequestTodo();
                    e.setId(t.getId());
                    return e;
                }).collect(Collectors.toList()));

        payment.getAttachments().addAll(payInvoiceDto.getAttachments());
        String desc = (payment.getDescription() != null && !payment.getDescription().isEmpty()) ? payment.getDescription().split("/")[0] : "";
        desc += " \n Payment for " + (supplier != null ? supplier.getName() : "") + " for:";
        for (ExpenseRequestPayInvoiceDto dto : payInvoiceDto.getExpenseRequestPayInvoiceDtos()) {
            if (requestToDo != null)
                desc += " \n " + dto.getExpense() + " FOR " + dto.getRelatedTo() + " " + dto.getAmount();
        }
        payment.setDescription(desc);
        expensePaymentController.createEntity(payment);
        return payment;
    }

    @Autowired
    StockKeeperToDoService stockKeeperToDoService;

    @Transactional
    public void businessOnRequestAfterPaymentIsPaid(ExpenseRequestTodo todo) {
        ExpenseRequestType expenseRequestType = todo.getExpenseRequestType();
        if (expenseRequestType == null) return;

        switch (expenseRequestType) {
            case DEWA:
            case TELECOM:
                businessAfterDEWAAndTelecomPaymentIsPaid(todo);
                break;
            case PURCHASING:
                stockKeeperToDoService.businessAfterPayPurchaseOrderPayment(todo);
                break;
            case INSURANCE:
                businessAfterInsurancePaymentIsPaid(todo);
                break;
        }
    }

    public void businessOnPaymentAfterPaymentIsPaid(ExpensePayment expensePayment) {
        checkAndCreateTransaction(expensePayment);
    }

    @Transactional
    public void checkAndCreateTransaction(ExpensePayment expensePayment) {
        if (expensePayment.getMethod() == null || !expensePayment.getMethod().equals(ExpensePaymentMethod.CASH)) return;

        Double additionAmount = expensePayment.getLocalCurrencyAmount() - (expensePayment.getLoanAmount() != null ? expensePayment.getLoanAmount() : 0D);

        if (additionAmount > 0) {

            Transaction transaction = new Transaction();
            updateTransactionObjectFromPayment(expensePayment, transaction, new Date(System.currentTimeMillis()), additionAmount, expensePayment.getVatAmount());
            transaction.setDescription(expensePayment.getDescription());

            for (Attachment a : expensePayment.getAttachments()) {
                if (a.getTag().toLowerCase().contains("signature"))
                    continue;

                if (a.getTag().equals(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString())) {
                    Attachment transactionVatInvoice = Storage.cloneTemporary(a, AttachmentTag.VAT_TRANSACTION_INVOICE.toString());
                    transaction.addAttachment(transactionVatInvoice);
                } else {
                    transaction.addAttachment(a);
                }
            }

            //ACC-3247
            if (expensePayment.getReplenishmentTodo() != null) {
                BucketReplenishmentTodoRepository replenishmentTodoRepo = Setup.getRepository(BucketReplenishmentTodoRepository.class);
                BucketReplenishmentTodo bucketReplenishmentTodo = expensePayment.getReplenishmentTodo();
                bucketReplenishmentTodo.setTransactionAdded(true);
                replenishmentTodoRepo.save(bucketReplenishmentTodo);
            }
            
            initTransactionRelatedToData(transaction, expensePayment.getRelatedToType(), expensePayment.getRelatedToId());
            Setup.getApplicationContext().getBean(TransactionsController.class).createEntity(transaction);
            expensePayment.setTransaction(transaction);
            expensePaymentRepository.save(expensePayment);
        }
        
        if (expensePayment.getLoanAmount() != null && expensePayment.getLoanAmount() != 0){
            
            //transaction.setAmount(transaction.getAmount() - expensePayment.getLoanAmount());
            Transaction loanTransaction = new Transaction();
            updateTransactionObjectFromPayment(expensePayment, loanTransaction, new Date(System.currentTimeMillis()), expensePayment.getLoanAmount(), null);
            //loanTransaction.setAmount(expensePayment.getLoanAmount());
            loanTransaction.setToBucket(null);
            ExpenseRepository expenseRepo = Setup.getRepository(ExpenseRepository.class);
            Expense loanExpense = expenseRepo.findByCodeAndDeletedFalse(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_LOAN_EXPENSE_CODE));
            if (loanExpense == null)
                throw new RuntimeException("There is no Loan Expense.");
            loanTransaction.setExpense(loanExpense);
            loanTransaction.setDescription("Adding loan to " + expensePayment.getRelatedToType().toString() + " " + expensePayment.getRelatedToName() + " based on Expense " + (expensePayment.getExpenseToPost() != null ? expensePayment.getExpenseToPost().getCaption() : "") + ".");

            for (Attachment a : expensePayment.getAttachments()) {
                
                if (a.getTag().toLowerCase().contains("signature"))
                    continue;
                if (a.getTag().equals(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString())) {
                    Attachment transactionVatInvoice = Storage.cloneTemporary(a, AttachmentTag.VAT_TRANSACTION_INVOICE.toString());
                    loanTransaction.addAttachment(transactionVatInvoice);
                } else {
                    loanTransaction.addAttachment(a);
                }
            }
            initTransactionRelatedToData(loanTransaction, expensePayment.getRelatedToType(), expensePayment.getRelatedToId());
            Setup.getApplicationContext().getBean(TransactionsController.class).createEntity(loanTransaction);
        }
    }

    public void updateTransactionObjectFromPayment(ExpensePayment expensePayment, Transaction transaction, Date payDate, Double amount, Double vatAmount) {
        if (amount == null)
            return;
        transaction.setAmount(amount);
        transaction.setVatAmount(vatAmount);
        transaction.setPnlValueDate(payDate);
        transaction.setDate(payDate);
        transaction.setFromBucket(expensePayment.getFromBucket());
        transaction.setExpensePaymentId(expensePayment.getId());
        transaction.setPaymentType(PaymentMethod.CASH);

        if (vatAmount != null && vatAmount != 0) {
            transaction.setVatType(VatType.IN);
            transaction.setLicense(PicklistHelper.getItem(
                    PICKLIST_TRANSACTION_LICENSE,
                    PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        } else {
            transaction.setVatType(null);
            transaction.setLicense(PicklistHelper.getItem(
                    PICKLIST_TRANSACTION_LICENSE,
                    "no_vat"));
        }

        if (expensePayment.getToBucket() != null)
            transaction.setToBucket(expensePayment.getToBucket());
        else
            transaction.setExpense(expensePayment.getExpenseToPost());

        transaction.checkAttachment();
    }

    @Transactional
    public void businessAfterInsurancePaymentIsPaid(ExpenseRequestTodo todo) {
        InsuranceBalanceLog lastLog = insuranceBalanceLogRepository.findTopByOrderByLogDateDesc();
        InsuranceBalanceLog log = new InsuranceBalanceLog();
        log.setDescription("Payment");
        log.setLogDate(new java.util.Date());
        log.setType(InsuranceBalanceLog.InsuranceBalanceLogType.PAYMENT);
        log.setAmount(todo.getAmount());
        log.setBalance(lastLog.getBalance() - todo.getAmount());
        log.setRefId(todo.getId());
        insuranceBalanceLogRepository.save(log);
    }


    @Transactional
    public void businessAfterDEWAAndTelecomPaymentIsPaid(ExpenseRequestTodo todo) {
        if (todo.getTelecomPhones() != null) {
            for (TelecomPhone telecomPhone : todo.getTelecomPhones()) {
                TelecomPhoneBill telecomPhoneBill = new TelecomPhoneBill();
                telecomPhoneBill.setPhone(telecomPhone);
                telecomPhoneBill.setName(todo.getInvoiceNumber());
                telecomPhoneBill.setBillDate(todo.getExpensePayment().getPaymentDate());
                telecomPhoneBill.setAmount(todo.getExpensePayment().getAmount().intValue());
                telecomPhoneBill.setBillDate(todo.getExpensePayment().getPaymentDate());
                telecomPhoneBill.setAttachments(todo.getAttachments());
                telecomPhoneBillRepository.save(telecomPhoneBill);
            }
        }
    }

    public Boolean confirmRelatedExpensePayment(BankStatementTransaction bankTransaction) {
        if (bankTransaction == null || bankTransaction.getExpensePayment() == null) return false;

        ExpensePayment expensePayment = bankTransaction.getExpensePayment();
        logger.info("Enter confirmRelatedExpensePayment for transaction: " + bankTransaction.getId() + ", Expense Payment: " + expensePayment.getId());

        List<Transaction> transactions = new ArrayList();

        if (bankTransaction.getBankTransactionType().equals(BankTransactionType.EXPENSE_REQUEST)) {
            transactions.addAll(confirmExpensePaymentRequest(bankTransaction, expensePayment));
        } else if (bankTransaction.getBankTransactionType().equals(BankTransactionType.REPLENISHMENT_REQUEST)) {
            transactions.addAll(confirmExpensesPaymentReplenishment(bankTransaction, expensePayment));
        }

        TransactionsController transactionsController = Setup.getApplicationContext().getBean(TransactionsController.class);

        for (Transaction transaction : transactions) {
            transactionsController.createEntity(transaction);
        }

        bankTransaction.setTransaction(transactions.get(0));

        expensePayment.setConfirmed(true);
        //Jirra ACC-3437
        expensePayment.setCallUpdateAmountsTrigger(Boolean.FALSE);
        expensePayment.setLocalCurrencyAmount(bankTransaction.getTransactionAmount());
        expensePaymentRepository.save(expensePayment);
        return true;
    }


    @Transactional
    public List<Transaction> confirmExpensePaymentRequest(BankStatementTransaction bankTransaction, ExpensePayment expensePayment) {
        logger.info("Enter confirmExpensesPaymentRefund");
        List<Transaction> transactions = new ArrayList();

        if (expensePayment.getMethod().equals(ExpensePaymentMethod.BANK_TRANSFER)) {
            transactions.add(confirmExpensePaymentRequest_BankTransfer(bankTransaction, expensePayment));
        } else if (expensePayment.getMethod().equals(ExpensePaymentMethod.MONEY_TRANSFER)) {
            transactions.addAll(confirmExpensePaymentRequest_MoneyTransfer(bankTransaction, expensePayment));
        }

        List<ExpenseRequestTodo> toDos = expensePayment.getExpenseRequestTodos();
        ExpenseRequestTodoRepository todoRepo = Setup.getRepository(ExpenseRequestTodoRepository.class);
        for (ExpenseRequestTodo todo : toDos) {
            todo.setConfirmed(true);
            todoRepo.save(todo);
        }

        return transactions;
    }

    public Transaction confirmExpensePaymentRequest_BankTransfer(BankStatementTransaction bankTransaction, ExpensePayment expensePayment) {
        logger.info("Enter confirmExpensesPaymentRefund_BankTransfer");

        List<Attachment> noVatAttachments = getNoVatAttachments(expensePayment);

        List<Attachment> vatAttachments = getVatAttachments(expensePayment);

        boolean missingTaxInvoice = bankTransaction.getVatAmount() != null && !bankTransaction.getVatAmount().equals(0D) && (vatAttachments == null || vatAttachments.isEmpty());

        return getBankTransferTransaction(expensePayment, bankTransaction, noVatAttachments, vatAttachments, missingTaxInvoice);
    }

    @Transactional
    public List<Transaction> confirmExpensePaymentRequest_MoneyTransfer(BankStatementTransaction bankTransaction, ExpensePayment expensePayment) {
        logger.info("Enter confirmExpensesPaymentRefund_MoneyTransfer");
        ExpenseRepository expenseRepo = Setup.getRepository(ExpenseRepository.class);
        PayrollAccountantTodoRepository payrollAccountantTodoRepository = Setup.getRepository(PayrollAccountantTodoRepository.class);

        PayrollAccountantTodo payrollAccountantTodo = payrollAccountantTodoRepository.findFirstByExpensePayment(expensePayment);
        payrollAccountantTodo = payrollAccountantTodoRepository.save(payrollAccountantTodo);

        java.util.Date now = new java.util.Date();
        Bucket bankBucket = bucketRepository.findFirstByBucketType(BucketType.BANK_ACCOUNT);
        Bucket ansariBucket = bucketRepository.findFirstByBucketType(BucketType.MONEY_TRANSFER);
        Expense ansariExpense = expenseRepo.findByCodeAndDeletedFalse(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ANSARI_EXPENSE_CODE));

        Transaction toAnsariBucketTransaction = createTransaction(expensePayment,
                payrollAccountantTodo.getTotal(), new Date(now.getTime()), now,
                bankTransaction.getDescription(), PaymentMethod.BANK_TRANSFER, 0D, null, bankBucket, ansariBucket,
                null, null, null, false);

        String description = "Ansari charges on transfer of amount " + payrollAccountantTodo.getTotal();

        Double fromAnsariBucketToAnsariExpenseVatAmount = payrollAccountantTodo.getCharges() - DiscountsWithVatHelper.getAmountWithoutVat(payrollAccountantTodo.getCharges());

        Transaction toAnsariExpenseTransaction = createTransaction(expensePayment,
                payrollAccountantTodo.getCharges(), new Date(now.getTime()), now,
                description, PaymentMethod.MONEY_TRANSFER, fromAnsariBucketToAnsariExpenseVatAmount, VatType.IN, ansariBucket, null,
                ansariExpense, null, null, true);

        List<Attachment> noVatAttachments = getNoVatAttachments(expensePayment);

        List<Attachment> vatAttachments = getVatAttachments(expensePayment);

        boolean missingTaxInvoice = bankTransaction.getVatAmount() != null && !bankTransaction.getVatAmount().equals(0D) && (vatAttachments == null || vatAttachments.isEmpty());

        Transaction toFinalExpenseTransaction = createTransaction(expensePayment,
                payrollAccountantTodo.getAmount(), new Date(now.getTime()), now,
                bankTransaction.getDescription(), PaymentMethod.MONEY_TRANSFER, bankTransaction.getVatAmount(), VatType.IN, ansariBucket, null,
                bankTransaction.getExpense(), noVatAttachments, vatAttachments, missingTaxInvoice);

        return new ArrayList() {
            {
                add(toAnsariBucketTransaction);
                add(toAnsariExpenseTransaction);
                add(toFinalExpenseTransaction);
            }
        };
    }

    @Transactional
    public List<Transaction> confirmExpensesPaymentReplenishment(BankStatementTransaction bankTransaction, ExpensePayment expensePayment) {
        logger.info("Enter confirmExpensesPaymentReplenishment");
        List<Transaction> transactions = new ArrayList();

        if (expensePayment.getMethod().equals(ExpensePaymentMethod.BANK_TRANSFER)) {
            transactions.add(confirmExpensesPaymentReplenishment_BankTransfer(bankTransaction, expensePayment));
        } else if (expensePayment.getMethod().equals(ExpensePaymentMethod.MONEY_TRANSFER)) {
            transactions.addAll(confirmExpensesPaymentReplenishment_MoneyTransfer(bankTransaction, expensePayment));
        }

        BucketReplenishmentTodoRepository todoRepo = Setup.getRepository(BucketReplenishmentTodoRepository.class);
        BucketReplenishmentTodo todo = expensePayment.getReplenishmentTodo();
        todo.setTransactionAdded(true);
        todo.setConfirmed(true);
        todoRepo.save(todo);

        return transactions;
    }

    public Transaction confirmExpensesPaymentReplenishment_BankTransfer(BankStatementTransaction bankTransaction, ExpensePayment expensePayment) {
        logger.info("Enter confirmExpensesPaymentReplenishment_BankTransfer");

        return getBankTransferTransaction(expensePayment, bankTransaction, null, null, null);
    }

    @Transactional
    public List<Transaction> confirmExpensesPaymentReplenishment_MoneyTransfer(BankStatementTransaction bankTransaction, ExpensePayment expensePayment) {
        logger.info("Enter confirmExpensesPaymentReplenishment_MoneyTransfer");
        BucketRepository bucketRepo = Setup.getRepository(BucketRepository.class);
        ExpenseRepository expenseRepo = Setup.getRepository(ExpenseRepository.class);
        PayrollAccountantTodoRepository payrollAccountantTodoRepository = Setup.getRepository(PayrollAccountantTodoRepository.class);

        PayrollAccountantTodo payrollAccountantTodo = payrollAccountantTodoRepository.findFirstByExpensePayment(expensePayment);
        payrollAccountantTodo = payrollAccountantTodoRepository.save(payrollAccountantTodo);

        java.util.Date now = new java.util.Date();
        Bucket bankBucket = bucketRepo.findFirstByBucketType(BucketType.BANK_ACCOUNT);
        Bucket ansariBucket = bucketRepo.findFirstByBucketType(BucketType.MONEY_TRANSFER);
        Expense ansariExpense = expenseRepo.findByCodeAndDeletedFalse(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ANSARI_EXPENSE_CODE));

        Transaction toAnsariBucketTransaction = createTransaction(expensePayment, payrollAccountantTodo.getTotal(), new Date(now.getTime()), now,
                bankTransaction.getDescription(), PaymentMethod.BANK_TRANSFER, 0D, null, bankBucket, ansariBucket,
                null, null, null, false);

        String description = "Ansari charges on transfer of amount " + payrollAccountantTodo.getTotal();
        Double fromAnsariBucketToAnsariExpenseVatAmount = payrollAccountantTodo.getCharges() - DiscountsWithVatHelper.getAmountWithoutVat(payrollAccountantTodo.getCharges());
        Transaction toAnsariExpenseTransaction = createTransaction(expensePayment,
                payrollAccountantTodo.getCharges(), new Date(now.getTime()), now,
                description, PaymentMethod.MONEY_TRANSFER, fromAnsariBucketToAnsariExpenseVatAmount, VatType.IN, ansariBucket, null,
                ansariExpense, null, null, true);

        return new ArrayList() {
            {
                add(toAnsariBucketTransaction);
                add(toAnsariExpenseTransaction);
            }
        };
    }

    @Transactional
    public Transaction getBankTransferTransaction(ExpensePayment expensePayment,
                                                  BankStatementTransaction bankTransaction,
                                                  List<Attachment> noVatAttachments,
                                                  List<Attachment> vatAttachments,
                                                  Boolean missingVatInvoice) {
        BucketRepository bucketRepository = Setup.getRepository(BucketRepository.class);

        java.util.Date now = new java.util.Date();
        Bucket fromBucket = !StringUtils.isEmpty(bankTransaction.getFromBucket()) ? bucketRepository.findByCode(bankTransaction.getFromBucket()) : null;
        Bucket toBucket = !StringUtils.isEmpty(bankTransaction.getToBucket()) ? bucketRepository.findByCode(bankTransaction.getToBucket()) : null;

        // Attachments
        return createTransaction(expensePayment,
                bankTransaction.getTransactionAmount(), new Date(now.getTime()), now,
                bankTransaction.getDescription(), PaymentMethod.BANK_TRANSFER, bankTransaction.getVatAmount(), bankTransaction.getVatType(),
                fromBucket, toBucket,
                bankTransaction.getExpense(),
                noVatAttachments, vatAttachments, missingVatInvoice);
    }

    @Transactional
    public Transaction createTransaction(ExpensePayment expensePayment,
                                         Double amount, Date date, java.util.Date pnlValueDate,
                                         String description, PaymentMethod typeOfPayment, Double vatAmount, VatType vatType,
                                         Bucket fromBucket, Bucket toBucket, Expense expense,
                                         List<Attachment> noVatAttachments, List<Attachment> vatAttachments, Boolean missingVatInvoice) {
        Transaction transaction = new Transaction();
        transaction.setAmount(amount);
        transaction.setDate(date);
        transaction.setPnlValueDate(pnlValueDate);
        transaction.setDescription(description);
        transaction.setPaymentType(typeOfPayment);
        transaction.setVatAmount(vatAmount);
        if (vatAmount != null && !vatAmount.equals(0D)) {
            transaction.setVatType(vatType);
            transaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        } else {
            transaction.setVatType(null);
            transaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));
        }

        transaction.setFromBucket(fromBucket);
        transaction.setToBucket(toBucket);
        transaction.setExpense(expense);
        transaction.setMissingTaxInvoice(missingVatInvoice);

        List<Attachment> attachments = new ArrayList();

        if (noVatAttachments != null) {
            attachments.addAll(noVatAttachments);
        }

        if (vatAttachments != null) {
            attachments.addAll(vatAttachments);
        }

        for (Attachment attachment : attachments) {
            transaction.addAttachment(attachment);
        }

        transaction.setExpensePaymentId(expensePayment.getId());
        initTransactionRelatedToData(transaction, expensePayment.getRelatedToType(), expensePayment.getRelatedToId());

        return transaction;
    }

    public List<Attachment> getAttachments(ExpensePayment expensePayment) {
        List<Attachment> attachments = new ArrayList();
        attachments.addAll(getVatAttachments(expensePayment));
        attachments.addAll(getNoVatAttachments(expensePayment));
        return attachments;
    }

    public List<Attachment> getVatAttachments(ExpensePayment expensePayment) {
        List<Attachment> attachments = new ArrayList();

        for (Attachment attachment : expensePayment.getAttachments().stream().filter(attachment -> !attachment.getTag().toLowerCase().contains("signature")).collect(Collectors.toList())) {
            if (!isVatAttachment(attachment)) {
                continue;
            }

            String tag = "VAT_" + attachment.getTag();
            Attachment clonedAtt = Storage.cloneTemporary(attachment, tag);
            attachments.add(clonedAtt);
        }

        return attachments;
    }

    public List<Attachment> getNoVatAttachments(ExpensePayment expensePayment) {
        List<Attachment> attachments = new ArrayList();

        for (Attachment attachment : expensePayment.getAttachments().stream().filter(attachment -> !attachment.getTag().toLowerCase().contains("signature")).collect(Collectors.toList())) {
            if (isVatAttachment(attachment)) {
                continue;
            }

            String tag = attachment.getTag();
            Attachment clonedAtt = Storage.cloneTemporary(attachment, tag);
            attachments.add(clonedAtt);
        }

        return attachments;
    }

    public boolean isVatAttachment(Attachment attachment) {
        return attachment.getTag().equalsIgnoreCase(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString()) || attachment.getTag().equalsIgnoreCase(AttachmentTag.EXPENSE_REQUEST_VAT_INVOICE.toString());
    }

    public ExpensePayment fillPaymentAttributesFromRequest(ExpenseRequestTodo requestTodo) {
        Expense expenseToUse = requestTodo.getExpenseToPost() != null ? requestTodo.getExpenseToPost() : requestTodo.getExpense();
        ExpensePayment expensePayment = new ExpensePayment();
        expensePayment.setMethod(requestTodo.getPaymentMethod());
        expensePayment.setAmount(requestTodo.getAmount());
        expensePayment.setExpenseToPost(expenseToUse);
        expensePayment.setRequiresInvoice(requestTodo.getExpense().getRequireInvoice());
        expensePayment.setInvoiceNumber(requestTodo.getInvoiceNumber());
//        expensePayment.setInvoiceAttached(requestTodo.getInvoiceAttached());
//        expensePayment.setAttachedValidVatInvoice(requestTodo.getAttachedInvoiceIsValidVatInvoice());
        expensePayment.setBeneficiaryType(requestTodo.getBeneficiaryType());
        expensePayment.setBeneficiaryId(requestTodo.getBeneficiaryId());
        expensePayment.setCurrency(requestTodo.getCurrency());
        expensePayment.setLocalCurrencyAmount(requestTodo.getAmountInLocalCurrency());
        expensePayment.setAmountToPay(requestTodo.getAmountToPay());
        expensePayment.setLoanAmount(requestTodo.getLoanAmount());
        expensePayment.setRelatedToId(requestTodo.getRelatedToId());
        expensePayment.setRelatedToType(requestTodo.getRelatedToType());
        if (requestTodo.getPaymentMethod() == null || !requestTodo.getPaymentMethod().equals(ExpensePaymentMethod.INVOICED)) {
            expensePayment.setRequester(requestTodo.getRequestedBy());
            expensePayment.setApprovedBy(requestTodo.getApprovedBy());
        }
        expensePayment.setFromDate(requestTodo.getRequestFrom());
        expensePayment.setToDate(requestTodo.getRequestTo());
        expensePayment.setVatAmount(requestTodo.getVatAmount());
        expensePayment.setBeneficiaryName(requestTodo.getBeneficiaryName());
        expensePayment.setBeneficiaryAccountName(requestTodo.getBeneficiaryAccountName());
        expensePayment.setBeneficiaryAccountNumber(requestTodo.getBeneficiaryAccountNumber());
        expensePayment.setBeneficiaryMobileNumber(requestTodo.getBeneficiaryMobileNumber());
        expensePayment.setBeneficiaryAddress(requestTodo.getBeneficiaryAddress());
        expensePayment.setBeneficiaryIBAN(requestTodo.getBeneficiaryIban());
        expensePayment.setBeneficiaryEidCopy(requestTodo.getBeneficiaryEid());
        expensePayment.setBeneficiaryInternational(requestTodo.getInternational());
        expensePayment.setBeneficiarySwift(requestTodo.getSwift());
        expensePayment.setTaxable(requestTodo.getTaxable());
        expensePayment.setNotes(requestTodo.getNotes());
        String instructions = requestTodo.getNotes();
        if (requestTodo.getExpenseRequestType().equals(ExpenseRequestType.TICKETING)) {
            instructions += " " + requestTodo.getPaymentLink();
        }
        expensePayment.setInstructions(instructions);
        expensePayment.setDescription(requestTodo.getDescription());
        List<ExpenseRequestTodo> expenseRequestTodos = new ArrayList<>();
        expenseRequestTodos.add(requestTodo);
        expensePayment.setExpenseRequestTodos(expenseRequestTodos);
        if (!requestTodo.getPaymentAlreadyPaid())
            expensePayment.setStatus(ExpensePaymentStatus.PENDING);
        else {
            if (requestTodo.getExpenseRequestType() != null && requestTodo.getExpenseRequestType().equals(ExpenseRequestType.VIP)) {
                expensePayment.setStatus(ExpensePaymentStatus.PAID);

                expensePayment.setConfirmed(true);
            } else {
                if (requestTodo.isInvoiceMessingOrTaxInvoiceMessing())
                    expensePayment.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
                else
                    expensePayment.setStatus(ExpensePaymentStatus.PAID);
                expensePayment.setConfirmed(false);
            }
        }
        expensePayment.setFromBucket(requestTodo.getBucket());

        if (requestTodo.getPaymentMethod() != null && requestTodo.getPaymentMethod().equals(ExpensePaymentMethod.SALARY))
            expensePayment.setSalaryAdditionType(requestTodo.getExpense().getSalaryAdditionType());

        for (Attachment a : requestTodo.getAttachments()) {
            Attachment newA = Storage.cloneTemporary(a, a.getTag());
            expensePayment.addAttachment(newA);
        }

        return expensePayment;
    }

    public String getRelatedToName(ExpenseRelatedTo.ExpenseRelatedToType relatedToType, Long relatedToId) {
        if (relatedToType == null) return null;
        if (relatedToId == null) return null;

        switch (relatedToType) {
            case MAID:
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(relatedToId);
                return housemaid.getName();
            case OFFICE_STAFF:
                OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);
                OfficeStaff officeStaff = officeStaffRepository.getOne(relatedToId);
                return officeStaff.getName();
            case APPLICANT:
                MaidsAtCandidateWARepository maidsAtCandidateWARepository = Setup.getRepository(MaidsAtCandidateWARepository.class);
                return maidsAtCandidateWARepository.getOne(relatedToId).getName();

            case TEAM:
                PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
                return picklistItemRepository.getOne(relatedToId).getName();
        }
        return null;
    }

    // ACC-3577
    public void initTransactionRelatedToData(Transaction transaction, ExpenseRelatedTo.ExpenseRelatedToType relatedToType, Long relatedToId) {
        if (relatedToType == null || relatedToId == null) return;

        switch (relatedToType) {
            case MAID:
                transaction.setTransactionType(TransactionEntityType.HOUSEMAID);
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).getOne(relatedToId);
                HousemaidTransaction housemaidTransaction = new HousemaidTransaction();
                housemaidTransaction.setHousemaid(housemaid);
                transaction.getHousemaids().add(housemaidTransaction);
                break;
            case APPLICANT:
                transaction.setTransactionType(TransactionEntityType.APPLICANT);
                MaidsAtCandidateWA applicant = Setup.getRepository(MaidsAtCandidateWARepository.class).getOne(relatedToId);
                MaidsAtCandidateWATransaction applicantTransaction = new MaidsAtCandidateWATransaction();
                applicantTransaction.setApplicant(applicant);
                transaction.getApplicants().add(applicantTransaction);
                break;
            case OFFICE_STAFF:
                transaction.setTransactionType(TransactionEntityType.OFFICE_STAFF);
                OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class).getOne(relatedToId);
                OfficeStaffTransaction officeStaffTransaction = new OfficeStaffTransaction();
                officeStaffTransaction.setOfficeStaff(officeStaff);
                transaction.getOfficeStaffs().add(officeStaffTransaction);
                break;
        }
    }

    // ACC-6831 Email #1
    public void sendEmailToCashierForScheduledCollection(Bucket b, Double oldBalance, Double newBalance) {
        if (b == null) return;;

        if (!b.isTransGuard() ||
                newBalance <= b.getTransGuardCeilingAmount() ||
                oldBalance > b.getTransGuardCeilingAmount()) return;

        logger.info("bucket balance: " + b.getBalance() +
                "; transGuardCeilingAmount: " + b.getTransGuardCeilingAmount());

        Map<String, String> parameters = new HashMap<String, String>() {{
            put("bucket_holder_name", b.getHolder() == null ? "" : b.getHolder().getFullName());
            put("transguard_ceiling_amount", b.getTransGuardCeilingAmount() == null ?
                    "" : PaymentHelper.df.format(b.getTransGuardCeilingAmount()));
        }};

        Template t = TemplateUtil.getTemplate("cashier_schedule_collection_account_ceiling_exceeded");
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaffWithCc(b.getHolder(),
                        t.getName(), parameters,
                        b.getHolderEmail(),
                        Collections.singletonList(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_ACCOUNT_CEILING_EXCEEDED_CC_EMAIL)),
                        t.getSubject());
    }

    public boolean bucketOrExpenseSecureByPageCode(Boolean expenseIsSecure, Boolean fromBucketIsSecure, Boolean toBucketIsSecure) {

        String pageCode = Setup.getCurrentHttpRequest().getHeader("pageCode");
        return pageCode != null && pageCode.equalsIgnoreCase("ACCOUNTING__ExpensesRequests") &&
                bucketOrExpenseSecure(expenseIsSecure, fromBucketIsSecure, toBucketIsSecure);
    }

    // ACC-8094
    public boolean bucketOrExpenseSecure(Boolean expenseIsSecure, Boolean fromBucketIsSecure, Boolean toBucketIsSecure) {

        return ((expenseIsSecure != null && expenseIsSecure) ||
                (fromBucketIsSecure != null && fromBucketIsSecure) ||
                (toBucketIsSecure != null && toBucketIsSecure)) &&
                (CurrentRequest.getUser() == null || !CurrentRequest.getUser().hasPosition(AccountingModule.POSITION_READ_SECURE_EXPENSE));
    }

    public ExpensePayment payCreditCardPayment(ExpensePayment entity, ExpensePayment paymentReq, User user) {

        if (paymentReq.getVatAmount() != null)
            entity.setVatAmount(paymentReq.getVatAmount());

        if (paymentReq.getFromBucket()!= null)
            entity.setFromBucket(paymentReq.getFromBucket());

        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        for (Attachment a : paymentReq.getAttachments()) {
            entity.addAttachment(attachementRepository.findOne(a.getId()));
        }

        //ACC-3931
        //if invoice is attached and there is not vat attachment then clone the invoice attachment into new tag
        Boolean attachedValidVatInvoice = paymentReq.getAttachedValidVatInvoice() != null ? paymentReq.getAttachedValidVatInvoice() : false;

        if (attachedValidVatInvoice && entity.getAttachment(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString()) == null) {
            Attachment paymentInvoice = entity.getAttachment(AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
            if (paymentInvoice != null) {
                Attachment paymentVatInvoice = Storage.cloneTemporary(paymentInvoice, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
                entity.addAttachment(paymentVatInvoice);
            }
        }


        entity.setPaidBy(user);
        entity.setPaymentDate(new java.util.Date());

        Setup.getApplicationContext()
                .getBean(ExpensePaymentInCreditCardHolderStep.class)
                .onDone(entity);
       return expensePaymentRepository.save(entity);
    }

    public Map<String, Object> getMaidsLuggageCompensationExpenseRequestData(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        List<Housemaid> housemaids = houseMaidService.getHousemaids(mobileNumber, eidNumber, firstName, middleName, lastName, contractId);
        Map<String, Object> results = new HashMap<>();

        if (housemaids.isEmpty()) {
            results.put("no_maids", true);
            return results;
        } else if (housemaids.size() > 1) {
            results.put("multipleMaids", true);
        }

        List<ExpenseRequestTodo> expenses = expenseRequestTodoRepository.findByBeneficiaryIdAndBeneficiaryTypeAndExpense_Code(
                housemaids.get(0).getId(),
                Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_LUGGAGE_COMPENSATION_EXPENSE_CODE));

        if (expenses.isEmpty()) {
            results.put("luggageCompensation", false);
            return results;
        }

        StringBuilder luggageCompensationDetails = new StringBuilder("HM has luggage compensation");
        int index = 1;

        for (ExpenseRequestTodo todo : expenses) {
            if (index > 1) {
                luggageCompensationDetails.append(", and another one");
            }
            luggageCompensationDetails.append(" of status ")
                    .append(todo.getStatus()).append(" on ")
                    .append(new LocalDate(todo.getCreationDate()).toString("yyyy-MM-dd"));
            index++;
        }
        luggageCompensationDetails.append(".");

        results.put("luggageCompensationDetails", luggageCompensationDetails.toString());
        results.put("luggageCompensation", true);
        return results;
    }

    public Map<String, Object> getMaidExpenses(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        List<Housemaid> housemaids = houseMaidService.getHousemaids(mobileNumber, eidNumber, firstName, middleName, lastName, contractId);
        Map<String, Object> results = new HashMap<>();

        if (housemaids.isEmpty()) {
            results.put("no_maids", true);
            return results;
        } else if (housemaids.size() > 1) {
            results.put("multipleMaids", true);
        }

        List<Object[]> expenses = expenseRequestTodoRepository.findByRelatedToIdAndRelatedToType(housemaids.get(0).getId());

        StringBuilder maidExpenses = new StringBuilder();

        for (int i = 0; i < expenses.size(); i++) {
            Object[] row = expenses.get(i);
            if (i > 0) {
                maidExpenses.append("///");
            }
            maidExpenses.append("Expense Type:").append(row[0])
                    .append(", Date:").append(new LocalDate(row[1]).toString("yyyy-MM-dd"))
                    .append(", Amount:").append(((Double) row[2]).intValue())
                    .append(", Loan:").append(row[3] != null ? ((Double) row[3]).intValue() : 0)
                    .append(", payment method:").append(row[4])
                    .append(", status:").append(row[5])
                    .append(", notes:").append(row[6] != null ? row[6] : "");
        }

        if (!expenses.isEmpty()) {
            maidExpenses.append(".");
        }

        results.put("maidExpenses", maidExpenses);

        return results;
    }
}
