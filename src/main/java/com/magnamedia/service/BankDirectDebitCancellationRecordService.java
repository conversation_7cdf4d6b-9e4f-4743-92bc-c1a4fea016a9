package com.magnamedia.service;

import com.magnamedia.controller.BankDirectDebitCancelationFileController;
import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.BankDirectDebitCancelationFileRepository;
import com.magnamedia.repository.BankDirectDebitCancelationRecordRepository;
import com.magnamedia.repository.DirectDebitCancelationToDoRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;


@Service
public class BankDirectDebitCancellationRecordService {

    protected static final Logger logger = Logger.getLogger(BankDirectDebitCancellationRecordService.class.getName());

    @Autowired
    private BankDirectDebitCancelationRecordRepository bankDirectDebitCancelationRecordRepository;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private BankDirectDebitCancelationFileRepository bankDirectDebitCancelationFileRepository;

    public void confirmMatchedRecords(List<BankDirectDebitCancelationRecord> records) {
        StringBuilder error = new StringBuilder();

        for (BankDirectDebitCancelationRecord record : records) {
            boolean proceed = record.getDirectDebitFile() == null || directDebitCancellationService
                    .validateDirectDebitCancellation(record.getDirectDebitFile());
            if(!proceed) {
                error.append(record.getDirectDebitFile().getApplicationId()).append(", ");
                continue;
            }

            switch (record.getStatus()) {
                case CONFIRMED:
                    createBGTForConfirmOneDD(record, true);
                    break;
                case REJECTED:
                    createBGTForApprovalOnRejectionBank(record, true);
                    break;
            }
        }

        if(error.length() > 0) {
            throw new BusinessException("DD '" + error + "' are under RPA process, they cannot be cancelled");
        }
    }

    @Transactional
    public void approveRejectionByBankForOneDD(Long recordId, Boolean fromRPA) {
        logger.info("record id: " + recordId);
        BankDirectDebitCancelationRecord record = bankDirectDebitCancelationRecordRepository.findOne(recordId);
        record.setConfirmed(true);
        record.setConfirmedByRPA(fromRPA);
        bankDirectDebitCancelationRecordRepository.save(record);

        // ACC-9383
        if (record.getDirectDebitFile() == null) return;
        if (QueryService.existsEntity(DirectDebitCancelationToDo.class,
                "e.directDebitFile = :p0 and e.completed = false and e.stopped = false",
                new Object[]{record.getDirectDebitFile()})) {
            return;
        }

        DirectDebitCancelationToDo toDo = directDebitCancelationToDoRepository.findFirstByDirectDebitFileAndCompletedTrueAndStoppedFalseOrderByCreationDateDesc(record.getDirectDebitFile());
        if (toDo == null) {
            toDo = directDebitCancelationToDoRepository.findFirstByDirectDebitFileOrderByCreationDateDesc(record.getDirectDebitFile());
        }
        DirectDebitCancelationToDo directDebitCancelationToDo = new DirectDebitCancelationToDo();
        directDebitCancelationToDo.setDirectDebitFile(record.getDirectDebitFile());
        directDebitCancelationToDo.setDirectDebit(record.getDirectDebitFile().getDirectDebit());
        directDebitCancelationToDo.setReason(toDo != null ? toDo.getReason() : null);
        directDebitCancelationToDo.setIgnoreDDRejectionFlow(true);

        directDebitCancelationToDoRepository.save(directDebitCancelationToDo);
    }

    @Transactional
    public boolean confirmOneDD(Long recordId, Boolean fromRPA) {
        logger.info("record id: " + recordId);
        BankDirectDebitCancelationRecord bankDirectDebitCancelationRecord =
                bankDirectDebitCancelationRecordRepository.findOne(recordId);

        if (!bankDirectDebitCancelationRecord.isConfirmed() &&
                bankDirectDebitCancelationRecord.getDirectDebitFile() != null &&
                bankDirectDebitCancelationRecord.getDirectDebitFile().getDdStatus()
                        .equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)) {

            DirectDebitFile ddf = bankDirectDebitCancelationRecord.getDirectDebitFile();
            ddf.setDdStatus(DirectDebitStatus.CANCELED);
            ddf.setCancellationDate(new Date());
            directDebitFileRepository.save(ddf);
        }

        bankDirectDebitCancelationRecord.setConfirmed(true);
        bankDirectDebitCancelationRecord.setConfirmedByRPA(fromRPA);
        bankDirectDebitCancelationRecordRepository.save(bankDirectDebitCancelationRecord);
        return true;
    }

    public void createBGTForApprovalOnRejectionBank(BankDirectDebitCancelationRecord record, boolean fromRPA) {
        String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD);
        backgroundTaskService.create(new BackgroundTask.builder(
                "BankDirectDebitCancellationFile_Confirm_DD_" + record.getBankDirectDebitCancelationFile().getId(),
                "accounting",
                "bankDirectDebitCancellationRecordService",
                "approveRejectionByBankForOneDD")
                .withRelatedEntity(record.getEntityType(), record.getId())
                .withParameters( new Class<?>[] { Long.class, Boolean.class },
                        new Object[] { record.getId(), fromRPA })
                .withQueue(BackgroundTaskQueues.valueOf(queue))
                .build());
    }

    public void createBGTForConfirmOneDD(BankDirectDebitCancelationRecord record, boolean fromRPA) {
        String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD);
        backgroundTaskService.create(new BackgroundTask.builder(
                "BankDirectDebitCancellationFile_Confirm_DD_" + record.getBankDirectDebitCancelationFile().getId(),
                "accounting",
                "bankDirectDebitCancellationRecordService",
                "confirmOneDD")
                .withRelatedEntity(record.getEntityType(), record.getId())
                .withParameters( new Class<?>[] { Long.class, Boolean.class },
                        new Object[] { record.getId(), fromRPA })
                .withQueue(BackgroundTaskQueues.valueOf(queue))
                .build());
    }

    //ACC-9005
    public void sendReportInEmailByRPA(Long fileId) {
        BankDirectDebitCancelationFile file = bankDirectDebitCancelationFileRepository.findOne(fileId);
        List<BankDirectDebitCancelationRecord> prevMatchedRecords = directDebitCancellationService
                .getBankCancellationRecords(file, BankDirectDebitCancelationFileController.RecordMatched.PREV_MATCHED, null)
                        .getContent();

        List<BankDirectDebitCancelationRecord> unMatchedRecords = directDebitCancellationService
                .getBankCancellationRecords(file, BankDirectDebitCancelationFileController.RecordMatched.NOT_MATCHED, null)
                        .getContent();

        long matchedAcceptedCount = prevMatchedRecords.stream()
                .filter(r -> DirectDebitStatus.CANCELED.equals(r.getDirectDebitFile().getDdStatus())).count();
        long matchedRejectedCount = prevMatchedRecords.stream()
                .filter(r -> BankDirectDebitCancelationRecord.status.REJECTED.equals(r.getStatus()) && r.isConfirmed()).count();

        Map<String, String> parameters = new HashMap<>();

        parameters.put("report_name", file.getAttachments().get(0).getName());
        parameters.put("upload_date", new DateTime(file.getDate()).toString("yyyy-MM-dd HH:mm:ss"));
        parameters.put("matched_accepted_count", Long.toString(matchedAcceptedCount));
        parameters.put("matched_rejected_count", Long.toString(matchedRejectedCount));
        parameters.put("unmatched_count", Long.toString(unMatchedRecords.stream().count()));
        parameters.put("details_link", Setup.getApplicationContext()
                .getBean(Utils.class)
                .shorteningUrl(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/" +
                        "accounting/importing-bank-dd-cancellation-file/" + file.getId()));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_400_cancellation_report", parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DDS_CANCELLATION_REPORT_RPA_MAIL),
                        "DD 400 Cancellation Report");
    }
}