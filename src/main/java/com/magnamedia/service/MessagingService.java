package com.magnamedia.service;

import com.google.common.base.Strings;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.TemplateAllowedParameterRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.core.type.template.NotificationTarget;
import com.magnamedia.entity.*;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.UaePhoneNormlizer;
import com.magnamedia.repository.AccTemplateRepository;
import com.magnamedia.repository.DisablePushNotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by hp on 3/29/2021.
 */

@Service
public class MessagingService {
    private final Logger logger = Logger.getLogger(MessagingService.class.getName());

    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private TemplateAllowedParameterRepository templateAllowedParameterRepository;

    @Autowired
    private TemplateUtil templateUtil;

    public enum ButtonType {
        PRIMARY,
        SECONDARY
    }

    public void sendMessageToClient(
            Contract contract,
            Map<String, String> params,
            Map<String, AppAction> cta,
            Long ownerId,
            String ownerType,
            Template t) {

        sendMessageToClient(
                contract,
                params,
                cta,
                ownerId,
                ownerType,
                t,
                null);
    }

    public void sendMessageToClient(
            Contract contract,
            Map<String, String> params,
            Map<String, AppAction> cta,
            Long ownerId,
            String ownerType,
            Template t,
            DDMessagingContract ddMessagingContract) {

        logger.info("Sending message for Client: " + contract.getClient().getId() +
                "; Message Template Name: " + (t == null ? "null" : t.getName()) +
                "; number: " + contract.getClient().getNormalizedMobileNumber());

        if (t == null) return;

        StringBuilder mobile = new StringBuilder(contract.getClient().getNormalizedMobileNumber() != null ?
                contract.getClient().getNormalizedMobileNumber() : "");
        StringBuilder wa = new StringBuilder(contract.getClient().getNormalizedWhatsappNumber() != null ?
                contract.getClient().getNormalizedWhatsappNumber() : "");

        if (wa.toString().isEmpty() && !mobile.toString().isEmpty()) {
            wa.append(mobile);
        }

        if (ddMessagingContract != null && ddMessagingContract.getSendToSpouse()) {
            if (contract.getClient().getNormalizedSpouseMobileNumber() != null) {
                if (!mobile.toString().isEmpty()) mobile.append(";");
                mobile.append(contract.getClient().getNormalizedSpouseMobileNumber());
            }

            if (contract.getClient().getNormalizedSpouseWhatsappNumber() != null) {
                if (!wa.toString().isEmpty()) wa.append(";");
                wa.append(contract.getClient().getNormalizedSpouseWhatsappNumber());
            } else if (contract.getClient().getNormalizedSpouseMobileNumber() != null) {
                if (!wa.toString().isEmpty()) wa.append(";");
                wa.append(contract.getClient().getNormalizedSpouseMobileNumber());
            }
        }

        if (cta != null && !cta.isEmpty()) {
            boolean isFirst = true;

            for (Map.Entry<String, AppAction> entry : cta.entrySet()) {

                if (!entry.getValue().getType().equals(AppActionType.BUTTON)) continue;

                if (isFirst) {
                    entry.getValue().getAppRouteArguments().put("buttonType", ButtonType.PRIMARY.toString());
                    isFirst = false;
                } else {
                    entry.getValue().getAppRouteArguments().put("buttonType", ButtonType.SECONDARY.toString());
                }
            }
        }

        templateUtil.send(t,
                "en",
                Collections.singletonList(getClientTarget(
                        contract,
                        mobile.toString(),
                        wa.toString(),
                        ownerId, ownerType, t, ddMessagingContract == null || ddMessagingContract.getDdMessaging() == null ?
                                null : ddMessagingContract.getDdMessaging().getEmailSubject())),
                contract,
                cta,
                params);
        }

    public void sendClientSms(
            Contract contract,
            Template t,
            Map<String, String> params,
            Map<String, AppAction> cta,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType) {

        sendClientMessageByType(
                contract, t, params, cta,
                mobileNumber, whatsappNumber, ownerId,
                ownerType, ChannelSpecificSettingType.SMS);

    }

    public void sendClientMessageByType(
            Contract contract,
            Template t,
            Map<String, String> params,
            Map<String, AppAction> cta,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType,
            ChannelSpecificSettingType channelType) {

        logger.info("Sending " + channelType + " for Client: " + contract.getClient().getId() +
                "; Message Template Name: " + (t == null ? "null" : t.getName()) +
                "; number: " + mobileNumber);

        if (t == null) return;

        templateUtil.send(t,
                "en",
                channelType,
                Collections.singletonList(getClientTarget(contract, mobileNumber,
                        whatsappNumber == null || whatsappNumber.isEmpty() ?
                        mobileNumber : whatsappNumber,
                        ownerId, ownerType, t, null)),
                contract,
                cta,
                params);
    }

    private NotificationTarget getClientTarget(
            Contract contract,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType,
            Template t,
            String emailSubject) {

        return new NotificationTarget() {
            @Override
            public Long getId() {
                return contract.getClient().getId();
            }

            @Override
            public String getEntityType() {
                return contract.getClient().getEntityType();
            }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public String getReceiverName() { return contract.getClient().getName(); }

            @Override
            public SmsReceiverType getSmsReceiverType() {
                return SmsReceiverType.Client;
            }

            @Override
            public String getSmsReceiverName() { return  contract.getClient().getName(); };

            @Override
            public String getWhatsappNumber() { return whatsappNumber; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return EmailReceiverType.Client; }

            @Override
            public String getEmail() { return contract.getClient().getEmail(); }

            @Override
            public boolean isAppendTrailingSentence() {
                return t.isChannelExist(ChannelSpecificSettingType.SMS) &&
                        t.getChannelSetting(ChannelSpecificSettingType.SMS)
                                .getTrailingSentence() != null;
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public String getEmailSubject() {
                return emailSubject != null && !emailSubject.isEmpty() ? emailSubject : null;
            }

            @Override
            public Long getRelatedEntityId1() {return contract.getId();}

            @Override
            public String getRelatedEntityType1() {return contract.getEntityType();}

            @Override
            public Map<String, String> getAdditionalInfo() {
                if(contract.getHousemaid() == null) return null;
                Map<String, String> maidInfo = new HashMap<>();
                maidInfo.put("maidId", contract.getHousemaid().getId().toString());
                return maidInfo;
            }
        };
    }

    public void sendEmailToClient(
            Contract contract,
            String templateName,
            Map<String, String> params,
            String emails,
            String emailSubject) {

        sendEmail(contract.getClient(), contract, templateName, params, contract.getClient().getNormalizedMobileNumber(),
                contract.getClient().getNormalizedWhatsappNumber(), emails, SmsReceiverType.Client,
                EmailReceiverType.Client, new ArrayList<>(), new ArrayList<>(),
                new ArrayList<>(), emailSubject);
    }

    public void sendEmailToOfficeStaff(
            String templateName,
            Map<String, String> params,
            String emails,
            String emailSubject) {

        sendEmailToOfficeStaffWithAttachments(templateName, params, emails, new ArrayList<>(), emailSubject);
    }

    public void sendEmailToOfficeStaffWithAttachments(
            String templateName,
            Map<String, String> params,
            String emails,
            List<Attachment> attachments,
            String emailSubject) {

        sendEmail(null, null, templateName, params, null,
                null, emails, SmsReceiverType.Office_Staff,
                EmailReceiverType.Office_Staff, attachments, new ArrayList<>(),
                new ArrayList<>(), emailSubject);
    }

    public void sendEmailToOfficeStaffWithCc(
            User recipient,
            String templateName,
            Map<String, String> params,
            String emails,
            List<String> ccEmails,
            String emailSubject) {

        List<EmailRecipient> cc = new ArrayList<>();
        if (!ccEmails.isEmpty()) {
            ccEmails.forEach(e -> cc.addAll(EmailHelper.getRecipients(e)));
        }

        sendEmail(recipient, null, templateName, params, recipient == null ? null : recipient.getMobileNumber(),
                recipient == null ? null : recipient.getWhatsappNumber(), emails, SmsReceiverType.Office_Staff,
                EmailReceiverType.Office_Staff, new ArrayList<>(), cc,
                new ArrayList<>(), emailSubject);
    }

    public void sendEmail(
            BaseEntity recipient,
            Contract contract,
            String templateName,
            Map<String, String> params,
            String mobileNumber,
            String whatsappNumber,
            String emails,
            SmsReceiverType smsReceiverType,
            EmailReceiverType emailReceiverType,
            List<Attachment> attachments,
            List<EmailRecipient> cc,
            List<EmailRecipient> bcc,
            String emailSubject) {

        logger.info("Sending Email email Template Name: " + templateName +
                "; emails: " + emails);

        if (Strings.isNullOrEmpty(emails)) return;

        Template t = TemplateUtil.getTemplate(templateName);
        if (t == null) return;

        List<EmailRecipient> recipients = EmailHelper.getEmails(emails)
                .stream()
                .map(e -> new Recipient(e, recipient != null && recipient.getLabel() != null ? recipient.getLabel() : e))
                .collect(Collectors.toList());

        List<NotificationTarget> targets = Collections.singletonList(getEmailTarget(recipient, mobileNumber, whatsappNumber, contract != null ? contract.getId() : null,
                contract != null ? contract.getEntityType() : null, smsReceiverType, emailReceiverType, recipients.get(0).getEmail(),
                recipients.get(0),
                recipients.size() > 1 ? recipients : new ArrayList<>(),
                attachments, cc, bcc, emailSubject, true));

        templateUtil.send(t,
                ChannelSpecificSettingType.Email,
                targets,
                contract,
                new HashMap<>(),
                params);
    }

    private NotificationTarget getEmailTarget(
            BaseEntity recipient,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType,
            SmsReceiverType smsReceiverType,
            EmailReceiverType emailReceiverType,
            String email,
            EmailRecipient emailRecipient,
            List<EmailRecipient> emailRecipients,
            List<Attachment> attachments,
            List<EmailRecipient> cc,
            List<EmailRecipient> bcc,
            String emailSubject,
            boolean isEmailHml) {

        return new NotificationTarget() {
            @Override
            public Long getId() { return recipient != null ? recipient.getId() : null; }

            @Override
            public String getEntityType() {
                return recipient != null ? recipient.getEntityType() : null;
            }

            @Override
            public String getReceiverName() {
                if(getEntityType() == null) return "";

                switch(getEntityType()) {
                    case "Client":
                        return ((Client)recipient).getName();
                    case "OfficeStaff":
                        return ((OfficeStaff)recipient).getName();
                }
                return "";
            }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public SmsReceiverType getSmsReceiverType() {
                return smsReceiverType;
            }

            @Override
            public String getWhatsappNumber() { return whatsappNumber; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return emailReceiverType; }

            @Override
            public String getEmail() { return email; }

            @Override
            public boolean isAppendTrailingSentence() {
                return NotificationTarget.super.isAppendTrailingSentence();
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public EmailRecipient getEmailRecipient() { return emailRecipient; }

            @Override
            public List<Attachment> getEmailAttachments() {
                return attachments == null ? new ArrayList<>() : attachments;
            }

            @Override
            public List<EmailRecipient> getCc() {
                return cc;
            }

            @Override
            public List<EmailRecipient> getBcc() {
                return bcc;
            }

            @Override
            public String getEmailSubject() {
                return emailSubject;
            }

            @Override
            public boolean isEmailHml() {
                return isEmailHml;
            }

            // ACC-7020
            @Override
            public List<EmailRecipient> getEmailRecipients() { return emailRecipients; }
            @Override
            public Long getRelatedEntityId1() {return ownerId;}

            @Override
            public String getRelatedEntityType1() {return ownerType;}
        };
    }

    public void sendMessageToMaid(
            Contract contract,
            Housemaid housemaid,
            Template t,
            Map<String, String> params,
            Long ownerId,
            String ownerType) {

        logger.info("Sending message for Maid: " + housemaid.getId() +
                "; Message Template Name: " + (t == null ? "NULL" : t.getName()) +
                "; number: " + housemaid.getNormalizedPhoneNumber());

        if (t == null) return;
        templateUtil.send(t,
                housemaid.getYayaAppNotificationLang(),
                Collections.singletonList(getMaidTarget(contract, housemaid, ownerId, ownerType)),
                contract,
                new HashMap<>(),
                params);
    }

    public void sendMaidSms(
            Contract contract,
            Housemaid housemaid,
            Template t,
            Map<String, String> params,
            Long ownerId,
            String ownerType) {

        logger.info("Sending SMS for Maid: " + housemaid.getId() +
                "; Message Template Name: " + (t == null ? "NULL" : t.getName()) +
                "; number: " + housemaid.getNormalizedPhoneNumber());

        if(housemaid.getNormalizedPhoneNumber() == null ||
                housemaid.getNormalizedPhoneNumber().isEmpty() || t == null) return;

        templateUtil.send(t,
                housemaid.getYayaAppNotificationLang(),
                ChannelSpecificSettingType.SMS,
                Collections.singletonList(getMaidTarget(contract, housemaid, ownerId, ownerType)),
                contract,
                new HashMap<>(),
                params);
    }

    private NotificationTarget getMaidTarget(
            Contract contract,
            Housemaid recipient,
            Long ownerId,
            String ownerType) {

        return new NotificationTarget() {
            @Override
            public Long getId() { return recipient.getId(); }

            @Override
            public String getEntityType() { return recipient.getEntityType(); }

            @Override
            public String getMobileNumber() {
                return UaePhoneNormlizer.NormalizePhoneNumber(recipient.getNormalizedPhoneNumber());
            }

            @Override
            public String getReceiverName() { return recipient.getName(); }

            @Override
            public SmsReceiverType getSmsReceiverType() { return SmsReceiverType.Housemaid; }

            @Override
            public String getWhatsappNumber() {
                return recipient.getNormalizedWhatsAppPhoneNumber() != null &&
                        !recipient.getNormalizedWhatsAppPhoneNumber().isEmpty() ?
                    recipient.getNormalizedWhatsAppPhoneNumber() : getMobileNumber();
            }

            @Override
            public EmailReceiverType getEmailReceiverType() { return EmailReceiverType.Housemaid; }

            @Override
            public String getEmail() {
                return NotificationTarget.super.getEmail();
            }

            @Override
            public boolean isAppendTrailingSentence() {
                return NotificationTarget.super.isAppendTrailingSentence();
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public Long getRelatedEntityId1() {return contract.getId();}

            @Override
            public String getRelatedEntityType1() {return contract.getEntityType();}
        };
    }

    public void sendMessageToOfficeStaff(
            Contract contract,
            OfficeStaff recipient,
            Template t,
            Map<String, String> params,
            Long ownerId,
            String ownerType,
            String mobileNumber) {

        if (t == null) return;

        templateUtil.send(t,
                "en",
                ChannelSpecificSettingType.SMS,
                Collections.singletonList(getOfficeStaff(contract, recipient, ownerId, ownerType, mobileNumber)),
                null,
                new HashMap<>(),
                params);
    }

    private NotificationTarget getOfficeStaff(
            Contract contract,
            OfficeStaff recipient,
            Long ownerId,
            String ownerType,
            String mobileNumber) {

        return new NotificationTarget() {
            @Override
            public Long getId() { return recipient.getId(); }

            @Override
            public String getEntityType() { return recipient.getEntityType(); }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public String getReceiverName() { return recipient.getName(); }

            @Override
            public SmsReceiverType getSmsReceiverType() { return SmsReceiverType.Office_Staff; }

            @Override
            public String getWhatsappNumber() { return null; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return EmailReceiverType.Office_Staff; }

            @Override
            public String getEmail() { return recipient.getEmail(); }

            @Override
            public boolean isAppendTrailingSentence() {
                return NotificationTarget.super.isAppendTrailingSentence();
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public Long getRelatedEntityId1() {return contract.getId();}

            @Override
            public String getRelatedEntityType1() {return contract.getEntityType();}
        };
    }
    
    public void disableNotifications(String reason, String notificationsIds) {
        logger.log(Level.INFO, "Disabling notifications for reason: " + reason);
        logger.log(Level.INFO, "Disabling notifications ids: " + notificationsIds);

        List<Long> notificationIds =
                Arrays.stream(notificationsIds.split(";"))
                        .map(id -> Long.valueOf(id))
                        .collect(Collectors.toList());

        disablePushNotificationRepository.findAll(notificationIds)
                .stream()
                .filter(n -> !n.isDisabled())
                .forEach(n -> {
                    n.setDisabled(true);
                    n.setReceived(true);
                    n.setLocation(NotificationLocation.INBOX);
                    disablePushNotificationRepository.save(n);
                });
    }

    public void createDisableNotificationBGT(List<Long> notificationIds, String reason) {
        if (notificationIds.isEmpty()) return;

        String notificationsIds = notificationIds.stream().map(id -> id.toString()).collect(Collectors.joining(";"));

        BackgroundTaskService backgroundTaskService =
                Setup.getApplicationContext().getBean(BackgroundTaskService.class);

        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "disableNotifications", "messagingService", "accounting",
                "disableNotifications", null, null, true, false,
                new Class[]{ String.class, String.class },
                new Object[] { reason, notificationsIds });
    }

    public void moveToInbox(String reason, String notificationsIds) {
        logger.log(Level.INFO, "Move to inbox notifications for reason: " + reason);
        logger.log(Level.INFO, "Move to inbox notifications ids: " + notificationsIds);

        List<Long> notificationIds =
            Arrays.stream(notificationsIds.split(";"))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        disablePushNotificationRepository.findAll(notificationIds)
            .stream()
            .filter(n -> !n.getLocation().equals(NotificationLocation.INBOX))
            .forEach(n -> {
                n.setLocation(NotificationLocation.INBOX);
                n.setReceived(true);
                disablePushNotificationRepository.save(n);
            });
    }

    public void createMoveToInboxBgt(List<Long> notificationIds, String reason) {
        if (notificationIds.isEmpty()) return;

        String notificationsIds = notificationIds.stream()
            .map(Object::toString)
            .collect(Collectors.joining(";"));

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
            .create(new BackgroundTask.builder(
                "createMoveToInboxBgt" + new java.util.Date().getTime(),
                "accounting",
                "messagingService",
                "moveToInbox")
                .withRelatedEntity("PushNotification", null)
                .withParameters(
                    new Class[]{ String.class, String.class },
                    new Object[] { reason, notificationsIds })
                .withQueue(BackgroundTaskQueues.SequentialQueue)
                .build());
    }

    public void addGenderAndWorkerTypeForTemplates(){
        Setup.getRepository(AccTemplateRepository.class)
                .getTemplateForGenerateGenderExpressions(Setup.getCurrentModule().getId())
                .forEach(t -> {
                    generateGenderExpressions(t,"her_him","her","him");
                    generateGenderExpressions(t,"her_his","her","his");
                    generateGenderExpressions(t,"she_he","she","he");
                    generateGenderExpressions(t,"She_He_Capitalized","She","He");
                    generateWorkerTypeExpression(t);
                });
    }

    public void generateGenderExpressions(
            Template template,
            String name,
            String v1,
            String v2){

        String exp = "contract == null || contract.getHousemaid() == null ? \"" + v1 + "\" : " +
                "contract.getHousemaid().getGender() != null && " +
                    "contract.getHousemaid().getGender().equals(T(com.magnamedia.extra.Gender).Female) ? " +
                "\"" + v1 + "\" : " +
                "\"" + v2 + "\"" ;

        TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
        allowedParameter.setName(name);
        allowedParameter.setExpression(true);
        allowedParameter.setValue(exp);
        allowedParameter.setTemplate(template);

        templateAllowedParameterRepository.save(allowedParameter);
    }

    public void generateWorkerTypeExpression(Template template){
        String exp = "contract == null ? " + "\"maid\" : " +
                "contract.getWorkerType() != null && " +
                    "contract.getWorkerType().getCode().equals(\"private_driver\") ? " +
                "\"driver\" : " +
                "\"maid\" ";

        TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
        allowedParameter.setName("worker_type");
        allowedParameter.setExpression(true);
        allowedParameter.setValue(exp);
        allowedParameter.setTemplate(template);

        templateAllowedParameterRepository.save(allowedParameter);
    }
}