package com.magnamedia.service;

import com.magnamedia.controller.BankDirectDebitActivationFileController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.BankDirectDebitActivationFile;
import com.magnamedia.entity.BankDirectDebitActivationRecord;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitBankRejectionReason;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.report.NewDDRejectionReasonsReport;
import com.magnamedia.repository.BankDirectDebitActivationFileRepository;
import com.magnamedia.repository.BankDirectDebitActivationRecordRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.controller.BankDirectDebitActivationFileController.BANK_RESPONSE_ACCEPTED_STATUS;
import static com.magnamedia.controller.BankDirectDebitActivationFileController.BANK_RESPONSE_REJECTED_STATUS;

@Service
public class BankDirectDebitActivationRecordService {
    private static final Logger logger = Logger.getLogger(BankDirectDebitActivationRecordService.class.getName());

    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private BankDirectDebitActivationRecordRepository bankDirectDebitActivationRecordRepository;
    @Autowired
    private BankDirectDebitActivationFileRepository bankDirectDebitActivationFileRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    public String confirmDDsFromActivationFile(List<BankDirectDebitActivationRecord> records, boolean fromRPA) {
        if (records == null || records.isEmpty()) return "records is being processed. We will send you an email once done.!";

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "BankDirectDebitActivationFile");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();
        if (result != null && !result.isEmpty()) {
            throw new RuntimeException("another request is under processing right now");
        }

        long time = new Date().getTime();
        for (BankDirectDebitActivationRecord r : records) {
            r.setProcessing(true);
            r.setProcessingTimeStamp(time);
            bankDirectDebitActivationRecordRepository.saveAndFlush(r);
        }
        //CALL ASYNC FUNCTION
        preConfirmDDsApiAsync(records, fromRPA,
                CurrentRequest.getUser() != null ? CurrentRequest.getUser().getEmail() : null);

        return "records is being processed. We will send you an email once done.!";
    }

    // ACC-9004
    public void sendReportInEmailByRPA(Long fileId) {
        BankDirectDebitActivationFile file = bankDirectDebitActivationFileRepository.findOne(fileId);

        List<BankDirectDebitActivationRecord> prevMatchedRecords =
                getRecords(file, BankDirectDebitActivationFileController.recordMatched.PREV_MATCHED, false, null).getContent();

        List<BankDirectDebitActivationRecord> unMatchedRecords = new ArrayList<>(getRecords(file,
                BankDirectDebitActivationFileController.recordMatched.NOT_MATCHED, false, null).getContent());
        unMatchedRecords.addAll(getRecords(file, BankDirectDebitActivationFileController.recordMatched.NOT_MATCHED, true, null).getContent());

        long matchedAcceptedRecordsCount = prevMatchedRecords.stream()
                .filter(r -> "ACCEPTED".equals(r.getStatus())).count();

        long matchedRejectedRecordsCount = prevMatchedRecords.stream()
                .filter(r -> "REJECTED".equals(r.getStatus())).count();

        Map<String, String> parameters = new HashMap<>();

        parameters.put("report_name", file.getAttachments().get(0).getName());
        parameters.put("upload_date", new LocalDate(file.getDate()).toString("yyyy-MM-dd"));
        parameters.put("matched_accepted_count", Long.toString(matchedAcceptedRecordsCount));
        parameters.put("matched_rejected_count", Long.toString(matchedRejectedRecordsCount));
        parameters.put("unmatched_count", Integer.toString(unMatchedRecords.size()));
        parameters.put("details_link", Setup.getApplicationContext()
                .getBean(Utils.class)
                .shorteningUrl(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/" +
                        "accounting/payments-automation/importing-file/ddFile/" + file.getId()));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_300_activation_report", parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DDS_ACTIVATION_REPORT_RPA_MAIL),
                        "DD 300 Activation Report");
    }

    public Page<BankDirectDebitActivationRecord> getRecords(BankDirectDebitActivationFile file, BankDirectDebitActivationFileController.recordMatched matched, boolean rejected,
                                                             Pageable pageable) {
        SelectQuery<BankDirectDebitActivationRecord> query =
                new SelectQuery<>(BankDirectDebitActivationRecord.class);
        query.filterBy(CurrentRequest.getSearchFilter());
        query.filterBy("bankDirectDebitActivationFile", "=", file);
        if (matched == null)
            matched = BankDirectDebitActivationFileController.recordMatched.MATCHED;
        switch (matched) {
            case MATCHED:
                query.filterBy("status", "LIKE", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                query.filterBy("directDebitFileId", "IS NOT NULL", null);
                query.filterBy("ddStatus", "=", DirectDebitStatus.PENDING);
                query.sortBy("ddMethod", true, true);
                query.sortBy("id", true, true);
                break;
            case NOT_MATCHED:
                query.filterBy("status", "LIKE", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                query.filterBy("directDebitFileId", "IS NULL", null);

                break;
            case PREV_MATCHED:
                query.filterBy("directDebitFileId", "IS NOT NULL", null);
                query.filterBy("ddStatus", "!=", DirectDebitStatus.PENDING);
                query.sortBy("ddMethod", true, true);
                query.sortBy("id", true, true);
                break;
        }

        if (pageable == null) {
            return new PageImpl(query.execute());
        } else {
            return query.execute(pageable);
        }
    }

    @Async
    @Transactional
    public void preConfirmDDsApiAsync(List<BankDirectDebitActivationRecord> records, Boolean fromRPA, String email) {
        preConfirmDDsAPI(records, fromRPA, email);
    }

    @Transactional
    public void preConfirmDDsAPI(List<BankDirectDebitActivationRecord> records, Boolean fromRPA, String email) {

        if (records == null || records.isEmpty()) return;

        //sort by contract asc and nullsLast true
        records.sort(Comparator.comparing(BankDirectDebitActivationRecord::getContract));

        String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_ACTIVATION_RECORD);

        List<BankDirectDebitActivationRecord> processingRecords = new ArrayList<>();
        BankDirectDebitActivationRecord lastRecord = null;
        for (BankDirectDebitActivationRecord record : records){
            if (lastRecord == null ||
                    lastRecord.getDirectDebitFile() == null ||
                    lastRecord.getDirectDebitFile().getDirectDebit() == null ||
                    lastRecord.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() == null ||
                    lastRecord.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() == null ||
                    lastRecord.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId() == null ||
                    record.getDirectDebitFile() == null ||
                    record.getDirectDebitFile().getDirectDebit() == null ||
                    record.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() == null ||
                    record.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() == null ||
                    record.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId() == null ||
                    !lastRecord.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId().equals(
                            record.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId())){

                if (!processingRecords.isEmpty()) {
                    backgroundTaskService.create(new BackgroundTask.builder(
                            "BankDirectDebitActivationFile_Confirm_DD_" + record.getBankDirectDebitActivationFile().getId(),
                            "accounting",
                            "bankDirectDebitActivationFileController",
                            "confirmDDs")
                            .withRelatedEntity("BankDirectDebitActivationFile", record.getBankDirectDebitActivationFile().getId())
                            .withParameters(
                                    new Class[] { List.class, Boolean.class, String.class },
                                    new Object[] { processingRecords.stream().map(x -> x.getId().toString()).collect(Collectors.toList()), fromRPA, email })
                            .withQueue(BackgroundTaskQueues.valueOf(queue))
                            .withDelay(processingRecords.stream().anyMatch(b -> "REJECTED".equals(b.getStatus())) ? 3 * 60 * 1000L : 0L)
                            .build());
                }
                processingRecords = new ArrayList<>();
            }
            processingRecords.add(record);
            lastRecord = record;
        }

        if (!processingRecords.isEmpty()) {
            backgroundTaskService.create(new BackgroundTask.builder(
                    "BankDirectDebitActivationFile_Confirm_DD_" + lastRecord.getBankDirectDebitActivationFile().getId(),
                    "accounting",
                    "bankDirectDebitActivationFileController",
                    "confirmDDs")
                    .withRelatedEntity("BankDirectDebitActivationFile", lastRecord.getBankDirectDebitActivationFile().getId())
                    .withParameters(
                            new Class[] { List.class, Boolean.class, String.class },
                            new Object[] { processingRecords.stream().map(x -> x.getId().toString()).collect(Collectors.toList()), fromRPA, email })
                    .withQueue(BackgroundTaskQueues.valueOf(queue))
                    .withDelay(processingRecords.stream().anyMatch(b -> "REJECTED".equals(b.getStatus())) ? 3 * 60 * 1000L : 0L)
                    .build());
        }
    }

    // ACC-330
    public void parseRecords(BankDirectDebitActivationFile bankDirectDebitActivationFile) throws IOException {
        if (bankDirectDebitActivationFile.getAttachments() == null || bankDirectDebitActivationFile.getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");

        Attachment att = bankDirectDebitActivationFile.getAttachments().get(0);
        List<BankDirectDebitActivationRecord> records = new ArrayList<>();
        List<String> newRejectionReasons = new ArrayList();

        // Prepare rejection reasons data once before processing
        List<String> knownDDRejectionPickListItems = Setup.getRepository(PicklistItemRepository.class)
                .findByListOrderByNameAsc(
                        Setup.getRepository(PicklistRepository.class)
                                .findByCode(AccountingModule.PICKLIST_KNOWN_DD_REJECTION_REASONS))
                .stream()
                .map(PicklistItem::getCode)
                .collect(Collectors.toList());

        BufferedReader br = new BufferedReader(new InputStreamReader(Storage.getStream(att)));
        // Configure CSVReader with optimal settings for performance
        CSVReader csvReader = new CSVReaderBuilder(br)
                .withCSVParser(new CSVParserBuilder().withSeparator(',').build()) // Use comma as separator
                .withSkipLines(5) // Skip first 5 lines manually before creating the CSV reader
                .withKeepCarriageReturn(false) // Improve performance by not keeping carriage returns
                .withVerifyReader(false) // Disable reader verification for better performance
                .build();

        String[] cellsValues;
        // Read and process records in batches
        while ((cellsValues = csvReader.readNext()) != null) {
            // Skip empty rows
            if (cellsValues.length == 0) continue;
            BankDirectDebitActivationRecord activationRecord = new BankDirectDebitActivationRecord();
            try {
                // Parse the CSV record into an activation record
                parseRecordFields(cellsValues, activationRecord);

                // Process rejection reason if present
                if (activationRecord.getRejectionReason() != null) {
                    processRejectionReason(activationRecord);
                }
            } catch (Exception ex) {
                handleRecordException(ex, activationRecord);
            } finally {
                // Set the parent file reference
                activationRecord.setBankDirectDebitActivationFile(bankDirectDebitActivationFile);
                if (activationRecord.getRejectionReasonEdited() == null ||
                        !activationRecord.getRejectionReasonEdited()
                                .equalsIgnoreCase("duplicatecombinationofdardr(07+08+11+12+17)itwasalreadyprocessedearlier")) {
                    // ACC-2777
                    if (activationRecord.getRejectionReason() != null && !activationRecord.getRejectionReason().isEmpty() &&
                            !knownDDRejectionPickListItems.contains(activationRecord.getRejectionReason()
                                    .toLowerCase().replaceAll(" ", "_")) &&
                            !newRejectionReasons.contains(activationRecord.getRejectionReason())) {
                            newRejectionReasons.add(activationRecord.getRejectionReason());
                    }
                    records.add(activationRecord);
                }
            }
        }

        records = bankDirectDebitActivationRecordRepository.save(records);

        int unProcessRecordsSize = records.size();
        Integer pageNb = 0;
        BankDirectDebitActivationRecordService bankDirectDebitActivationRecordService =
                Setup.getApplicationContext().getBean(BankDirectDebitActivationRecordService.class);
        while (unProcessRecordsSize > 0) {
            bankDirectDebitActivationRecordService.validateRecordsAfterInsert(pageNb++, 200, bankDirectDebitActivationFile.getId());
            unProcessRecordsSize -= 200;
        }

        notifyAccountantAboutNewRejectionReasons(records, newRejectionReasons);
        br.close();
    }

    private void parseRecordFields(String[] cellsValues, BankDirectDebitActivationRecord activationRecord) throws ParseException {

        activationRecord.setRowIndex(Integer.parseInt(getCellValue(cellsValues[0])));
        activationRecord.setPresentmentDate(parseDate(cellsValues, 1));
        activationRecord.setBank(getStringValue(cellsValues, 3));
        activationRecord.setContract(getStringValue(cellsValues, 4));
        activationRecord.setAccount(getStringValue(cellsValues, 6));
        activationRecord.setDdaRefNo(getStringValue(cellsValues, 9));
        activationRecord.setStatus(getStringValue(cellsValues, 11));
        activationRecord.setRejectionReason(getStringValue(cellsValues, 12));
        activationRecord.setIban(getStringValue(cellsValues, 13));
        activationRecord.setAmount(cellsValues[14] != null && !cellsValues[14].isEmpty() ? Double.parseDouble(getCellValue(cellsValues[14])) : null);
        activationRecord.setStartDate(parseDate(cellsValues, 16));
        activationRecord.setExpiryDate(parseDate(cellsValues, 17));
    }

    private String getStringValue(String[] cellsValues, int index) {
        if (cellsValues[index] == null || cellsValues[index].isEmpty()) return null;
        return getCellValue(cellsValues[index]);
    }

    private java.sql.Date parseDate(String[] cellsValues, int index) throws ParseException {
        if (cellsValues[index] == null || cellsValues[index].isEmpty()) return null;
        return new java.sql.Date(DateUtil.parseDateSlashed(getCellValue(cellsValues[index])).getTime());
    }

    private String getCellValue(String value) {
        if (value.startsWith("\'"))
            return value.substring(1);
        return value;
    }

    private void processRejectionReason(BankDirectDebitActivationRecord activationRecord) {

        // Normalize rejection reason in one step
        String bankReason = activationRecord.getRejectionReason().replaceAll("[ ,.]", "").toLowerCase();
        activationRecord.setRejectionReasonEdited(bankReason);
        activationRecord.setRejectCategory(DirectDebitBankRejectionReason.fromString(bankReason));
    }

    private void handleRecordException(Exception ex, BankDirectDebitActivationRecord activationRecord) {
        ex.printStackTrace();
        if (ex instanceof ParseException) {
            activationRecord.setErrorMessage("Date Parsing Exception: " + ex.getMessage());
        } else if (ex instanceof NumberFormatException) {
            activationRecord.setErrorMessage("Number Parsing Exception: " + ex.getMessage());
        } else {
            activationRecord.setErrorMessage("Exception: " + ex.getMessage());
        }
    }

    @Transactional
    public void validateRecordsAfterInsert(Integer pageNb, Integer pageSize, Long fileId) {
        try {
            List<Map<String, Object>> l = bankDirectDebitActivationRecordRepository
                    .findRecordWithMatchedDdfByBankDirectDebitActivationFileId(fileId, PageRequest.of(pageNb, pageSize));
            if (l.isEmpty()) return;

            l.forEach(m -> {
                BankDirectDebitActivationRecord record = (BankDirectDebitActivationRecord) m.get("record");
                Long ddfId = (Long) m.get("ddfId");
                logger.info("record id  " + record.getId() + "ddfId: " + ddfId);

                record.setDirectDebitFileId(ddfId);
            });

            bankDirectDebitActivationRecordRepository.saveAll(l.stream()
                    .map(m -> (BankDirectDebitActivationRecord) m.get("record"))
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ACC-2777
    public void notifyAccountantAboutNewRejectionReasons(List<BankDirectDebitActivationRecord> records, List<String> newRejectionReasons) {

        List<String> saveNewRejectionReasons = new ArrayList<>();
        for (String rejectionReason : newRejectionReasons) {
            List<BankDirectDebitActivationRecord> matchedRecords = records.stream()
                    .filter(record -> record.getRejectionReason() != null &&
                            record.getRejectionReason().equals(rejectionReason))
                    .collect(Collectors.toList());
            if (prepareRejectionReasonsReportAndSendEmailToAccountant(rejectionReason, matchedRecords) &&
                    rejectionReason.length() <= 200) {
                saveNewRejectionReasons.add(rejectionReason);
            }
        }

        if (!saveNewRejectionReasons.isEmpty()) {
            PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
            Picklist ddRejectionReasonsList = picklistRepository.findByCode(AccountingModule.PICKLIST_KNOWN_DD_REJECTION_REASONS);
            for (String rejectionReason : saveNewRejectionReasons) {
                ddRejectionReasonsList.addItem(rejectionReason);
            }
            picklistRepository.save(ddRejectionReasonsList);
        }
    }

    private boolean prepareRejectionReasonsReportAndSendEmailToAccountant(String rejectionReason, List<BankDirectDebitActivationRecord> records) {
        try {
            if (records == null || records.isEmpty()) return false;

            NewDDRejectionReasonsReport report = new NewDDRejectionReasonsReport(records);

            Map<String, String> parameters = new HashMap();
            parameters.put("rejection_reason", rejectionReason);
            parameters.put("html_table", report.render());

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("new_rejection_reason",
                            parameters, Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_GEORGE_EMAIL_NEW_DD_REJECTION_REAOSNS_REPORT),
                            "New Rejection reason.");

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}