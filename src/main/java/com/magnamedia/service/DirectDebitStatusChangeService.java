package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitARejectionWaitingBankResponseStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBBouncedRejectionWaitingBankResponseStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBCaseDRejectionWaitingBankResponseStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBRejectionWaitingBankResponseStep;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Hachem
 */

@Service
public class DirectDebitStatusChangeService {

    private final Logger logger = Logger.getLogger(DirectDebitStatusChangeService.class.getName());

    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;

    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;
    @Autowired
    private SwitchingBankAccountService switchingBankAccountService;

    @Autowired
    private DirectDebitCancelationToDoRepository dDCancelationToDoRepository;

    @Autowired
    private SwitchingNationalityService switchingNationalityService;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractService contractService;

    public void ddFileApproved(DirectDebitFile entity) {
        if (entity.getDdMethod() == null) return;

        if (entity.getDdMethod().equals(DirectDebitMethod.AUTOMATIC)) {
            automaticDDConfirmed(entity);
        } else if (entity.getDdMethod().equals(DirectDebitMethod.MANUAL)) {
            manualDDConfirmed(entity);
        }

        DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());

        if (switchingBankAccountService.isClientSwitchingBankAccount(dd)) {
            switchingBankAccountService.handleSwitchingBankAccount(dd);
        }

        if (dd.getCategory().equals(DirectDebitCategory.A) &&
                dd.getImageForDD() != null && dd.getImageForDD().getId() != null) {//ACC-4031

            switchingBankAccountService.handleConfirmNewDDASwitchingBankAccount(dd);
        }

        // ACC-2956
        closeRelatedNotifications(dd);

        // ACC-4715
        Contract contract = entity.getDirectDebit().getContractPaymentTerm().getContract();
        if (contract.isPayingViaCreditCard() && dd.getCategory().equals(DirectDebitCategory.B)) {

            logger.log(Level.INFO, "dd approved set paying via credit card false, ddf id: {0}", entity.getId());
            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .updatePayingViaCreditCardFlag(contract, false);

            // ACC-5183
            Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class)
                    .generateDdPassedInClientPayingViaCreditCard(contract);

            // ACC-6646
            if (contract.getScheduledDateOfTermination() != null &&
                    contract.getStatus().equals(ContractStatus.ACTIVE)) {
                contractService.retractContractTermination(contract);
            }

        }
    }

    private void automaticDDConfirmed(DirectDebitFile entity) {
        logger.log(Level.SEVERE, "automaticDDFileConfirmed " + entity.getId());

        DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());

        if (!dd.getStatus().equals(DirectDebitStatus.PENDING)) return;

        dd.setStatus(DirectDebitStatus.CONFIRMED);
        dd.setAutoDdfFile(entity);

        directDebitCancellationService.cancelOtherAutoApprovedDDs(dd);

        boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(dd.getDirectDebitFiles());

        logger.log(Level.SEVERE, "automaticDDFileConfirmed allManualRejected: " + allManualRejected);
        logger.log(Level.SEVERE, "automaticDDFileConfirmed dd.getMStatus(): " + dd.getMStatus());


        if (dd.getMStatus() == DirectDebitStatus.CONFIRMED || !dd.isGenerateManualDDFsFromConfig()) {  // at least one manual is approved -> one auto is approved and one manual is approved
            if (dd.getDirectDebitRejectionToDo() != null) {
                DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                rejectTodo.setCompleted(true);
                directDebitRejectionToDoRepository.save(rejectTodo);
            }
            // ACC-2784 removed commented code

        } else if (allManualRejected) { // all manual rejected -> one auto is approved all manual rejected
            logger.info("allManualRejected");
            if (dd.getDdConfiguration().isIncludeManualInDDBFlow()) {
                logger.info("DD Configuration, isIncludeManualInDDBFlow");
                directDebitRejectionFlowService.startRejectionFlowUponDDStatusChange(dd);
            } else if (dd.getDirectDebitRejectionToDo() != null) {
                DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                rejectTodo.setCompleted(true);
                directDebitRejectionToDoRepository.save(rejectTodo);

                dd.setMStatus(DirectDebitStatus.REJECTED);
                dd.setManualDdfFile(null);
            }
        }

        // do nothing because there is no approved manual file yet and not all manuals are rejected

        directDebitRepository.saveAndFlush(dd);
    }

    private void manualDDConfirmed(DirectDebitFile entity) {
        logger.log(Level.SEVERE, "manualDDFileConfirmed " + entity.getId());

        DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());

        if (!dd.getMStatus().equals(DirectDebitStatus.PENDING)) return;

        dd.setMStatus(DirectDebitStatus.CONFIRMED);
        dd.setManualDdfFile(entity);

        //ACC-4941
        if (checkPendingDdResult(entity)) {
            directDebitRepository.saveAndFlush(dd);
            return;
        }

        directDebitCancellationService.cancelOtherManualApprovedDDs(dd);

        if (dd.getCategory() == DirectDebitCategory.A) {
            if (dd.getDirectDebitRejectionToDo() != null) {
                DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                rejectTodo.setStopped(true);
                rejectTodo.setCompleted(true);

                directDebitRejectionToDoRepository.save(rejectTodo);
            }
        } else if (dd.getCategory() == DirectDebitCategory.B) {
            boolean allAutoRejected = directDebitRejectionFlowService.allAutoFilesRejected(dd.getDirectDebitFiles());

            if (dd.getStatus() == DirectDebitStatus.CONFIRMED) {  // at least one Auto is approved -> one manual is approved one auto is approved
                if (dd.getDirectDebitRejectionToDo() != null) {
                    DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                    rejectTodo.setStopped(true);
                    rejectTodo.setCompleted(true);

                    directDebitRejectionToDoRepository.save(rejectTodo);
                }
            } else if (allAutoRejected) { // all Auto rejected -> one manual is approved all auto is rejected
                directDebitRejectionFlowService.startRejectionFlowUponDDStatusChange(dd);
            }

            // do nothing because there is no approved Auto file yet and not all Auto are rejected
        }

        directDebitRepository.saveAndFlush(dd);

        // ACC-4053 process bounced payment reminders for both A & B DDs
        List<Payment> bouncedPayments = paymentRepository.findByDirectDebitIdAndStatusAndReplaced(
                dd.getId(), PaymentStatus.BOUNCED, false);

        for (Payment bouncedPayment : bouncedPayments) {
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule bounced payment id: " + bouncedPayment.getId());
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule bounced payment trials: " + bouncedPayment.getTrials());//Acc=6301 Async
            bouncedPayment.setTrials(bouncedPayment.getTrials() > 0 ? 0 : -1);
            try {
                paymentService.forceUpdatePayment(bouncedPayment);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, null, ex);
                throw new RuntimeException("Problem with accounting Payment Update" + ex.getMessage());
            }
        }
    }

    private void closeRelatedNotifications(DirectDebit directDebit) {
        if (directDebit.getRejectCategory() != null &&
                directDebit.getRejectCategory().equals(DirectDebitRejectCategory.Authorization)) {

            Contract contract = directDebit.getContractPaymentTerm().getContract();

            PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);

            List<PushNotification> notifications = pushNotificationHelper.getByOwnerTypeAndId("OnCloseTaxiWorkOrder", contract.getId());
            notifications.addAll(pushNotificationHelper.getByOwnerTypeAndId("Contract", contract.getId()));

            pushNotificationHelper.stopDisplaying(notifications);
        }
    }

    public DirectDebitFile ddFileRejected(DirectDebitFile entity) {
        if (entity.getDdMethod() == null) return entity;

        HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery<>(DirectDebitFile.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("ddStatus");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<DirectDebitFile> oldDdf = historyQuery.execute();

        if (oldDdf.isEmpty()) return entity;

        if (!oldDdf.get(0).getDdStatus().equals(DirectDebitStatus.REJECTED)
                && entity.getDdStatus().equals(DirectDebitStatus.REJECTED)) {

            //ACC-4941
            if (entity.getDirectDebit().getMStatus().equals(DirectDebitStatus.PENDING)
                    && entity.getDirectDebit().getStatus().equals(DirectDebitStatus.PENDING)
                    && checkPendingDdResult(entity)) return entity;

            Map<String, Object> map = ddFileHandelRejectedCanceled(entity);

            if (map != null && map.get("ddStatus").equals(DirectDebitStatus.CANCELED)) {
                entity.setDdStatus(DirectDebitStatus.CANCELED);
            }
        }

        return entity;
    }

    public Map<String, Object> ddFileHandelRejectedCanceled(DirectDebitFile entity) {
        // IF DD HAS CANCELLATION TODO NOT RELATED TO SWITCHING NATIONALITY -> CANCEL DD IMMEDIATELY
        List<DirectDebitCancelationToDo> dDCancelationToDos = dDCancelationToDoRepository
                .findByDirectDebitFileAndCompletedFalseAndStoppedFalse(entity);

        if (!dDCancelationToDos.isEmpty()) {
            // ACC-2800
            DirectDebitCancelationToDo ddCToDo = dDCancelationToDos.get(0);

            if (ddCToDo.getHidden() != null && ddCToDo.getHidden() &&
                    entity.getDdStatus() != null && entity.getDdStatus().equals(DirectDebitStatus.REJECTED) &&
                    ddCToDo.getReason() != null &&
                    (Arrays.asList(DirectDebitCancellationToDoReason.SWITCHING_TO_VAT,
                            DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY).contains(ddCToDo.getReason())) &&
                    ddCToDo.getDirectDebit() != null &&
                    switchingNationalityService.doesDDCoverSwitchingPeriod(ddCToDo.getDirectDebit().getId())) {

                logger.log(Level.INFO, "DD Cancellation todo related to Switching Nationality");
            } else {
                logger.log(Level.INFO, "cancelling DD");

                ddCToDo.setStopped(true);
                dDCancelationToDoRepository.save(ddCToDo);

                Map<String, Object> map = new HashMap<>();
                map.put("id", ddCToDo.getDirectDebitFile().getId());
                map.put("ddStatus", DirectDebitStatus.CANCELED);
                return map;
            }
        }

        // IF DD IS RELATED TO NEW BANK ACCOUNT AFTER SWITCHING, AND PAYMENT OF OLD BANK ACCOUNT DD IS ALREADY RECEIVED -> CANCEL NEW DD
        DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());
        logger.log(Level.INFO, "dd Category: " + dd.getCategory() + "; dd id: " + dd.getId());

        if (dd.getImageForDD() != null) {
            DirectDebit oldDD = directDebitRepository.findOne(dd.getImageForDD().getId());
            logger.log(Level.INFO, "old dd id: {0}", oldDD.getId());

            if (switchingBankAccountService.isClientSwitchingBankAccount(oldDD) &&
                    oldDD.getCategory().equals(DirectDebitCategory.A)) {

                logger.info("Client is Switching Bank Account");
                Payment oldPayment = PaymentHelper.getPaymentOfDD(oldDD.getId(), dd.getContractPaymentTerm().getCreationDate());

                if (oldPayment != null) {
                    logger.log(Level.INFO, "Switch month payment id: " + oldPayment.getId() +
                            "; status is " + oldPayment.getStatus());

                    if (oldPayment.getStatus().equals(PaymentStatus.RECEIVED)) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", entity.getId());
                        map.put("ddStatus", DirectDebitStatus.CANCELED);
                        return map;
                    }
                }
            }
        }

        if (switchingBankAccountService.isClientSwitchingBankAccount(dd) &&
                dd.getCategory().equals(DirectDebitCategory.A)) {

            switchingBankAccountService.handleSwitchingBankAccount(dd);

            Map<String, Object> map = new HashMap<>();
            map.put("id", entity.getId());
            map.put("ddStatus", DirectDebitStatus.CANCELED);

            return map;
        }

        if (dd.getStatus() != null &&
                ((entity.getDdMethod() == DirectDebitMethod.AUTOMATIC && dd.getStatus().equals(DirectDebitStatus.PENDING)) ||
                        (entity.getDdMethod() == DirectDebitMethod.MANUAL && dd.getMStatus().equals(DirectDebitStatus.PENDING)))) {

            dd.getDirectDebitFiles().forEach(f -> logger.info("ID: " + f.getId() + "; status: " + f.getDdStatus()));

            // all files canceled so we do nothing
            if (!dd.getDirectDebitFiles().isEmpty() &&
                    dd.getDirectDebitFiles().stream()
                            .allMatch(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.CANCELED))) {

                logger.info("all child DDs canceled -> exiting");
                return null;
            }

            if (dd.getCategory() == DirectDebitCategory.A)
                handleRejectedCanceledDDA(dd);
            else
                handleRejectedCanceledDDB(dd);
        }

        return null;
    }

    public void handleRejectedCanceledDDA(DirectDebit dd) {
        boolean allRejected = false;

        if (!dd.getDirectDebitFiles().isEmpty()
                && dd.getDirectDebitFiles().stream().allMatch(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)
                || ddf.getDdStatus().equals(DirectDebitStatus.CANCELED)))
            allRejected = true;

        List<DirectDebitFile> rejectedDds =
                dd.getDirectDebitFiles().stream().filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)).collect(Collectors.toList());

        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category A files : " + allRejected);

        if (allRejected) {
//            if (rejectedDds.get(0).getRejectCategory() == DirectDebitRejectCategory.EID
//                    || rejectedDds.get(0).getRejectCategory() == DirectDebitRejectCategory.Account) {
//
//                logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute reject is : " + rejectedDds.get(0).getRejectCategory());
//
//                for (DirectDebitFile directDebitFile : dd.getDirectDebitFiles()) {
//                    logger.log(Level.SEVERE, "DirectDebitFileBusinessRule deleting FILE_TAG_DD_ACTIVATION for: " + directDebitFile.getId());
//                    AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
//                    Attachment attachment = directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);
//                    if (attachment != null)
//                        attachementRepository.delete(attachment);
//                }
//            } else {
                dd.setMStatus(DirectDebitStatus.REJECTED);
                dd.setManualDdfFile(null);
//            }

            dd.setRejectCategory(rejectedDds.get(0).getRejectCategory());
            dd.setRejectionReason(rejectedDds.get(0).getRejectionReason());
            directDebitRepository.save(dd);

            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category A reject category  : " + rejectedDds.get(0).getRejectCategory());


            DirectDebitRejectionToDo directDebitRejectionToDo = getDirectDebitToDo(dd);
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category A reject category1  : " + dd.getDirectDebitFiles().get(0).getRejectCategory());
            if (directDebitRejectionToDo != null && !directDebitRejectionToDo.isStopped() && !directDebitRejectionToDo.isCompleted()) {
                List<String> currentTasks = directDebitRejectionToDo.getCurrentTasks();
                if (!currentTasks.isEmpty()) {
                    DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                    switch (step) {
                        case WAITING_BANK_RESPONSE: {
                            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category A reject category WAITING_BANK_RESPONSE : " + dd.getDirectDebitFiles().get(0).getRejectCategory());
                            DirectDebitARejectionWaitingBankResponseStep waitingBankResponseStep = Setup.getApplicationContext().getBean(DirectDebitARejectionWaitingBankResponseStep.class);
                            waitingBankResponseStep.onDone(directDebitRejectionToDo);
                            break;
                        }
                    }
                }
            }
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category A reject category2  : " + dd.getDirectDebitFiles().get(0).getRejectCategory());

            if (switchingNationalityService.relatesToSwitching(dd.getId(), false)) {
                switchingNationalityService.processCancellationToDos_fromDDRejection(dd);
            }
        }
    }

    public void handleRejectedCanceledDDB(DirectDebit dd) {
        // all auto files canceled so we do noting here
        List<DirectDebitFile> automaticDdf = dd.getDirectDebitFiles().stream()
                .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.AUTOMATIC)
                .collect(Collectors.toList());
        if (!automaticDdf.isEmpty() && automaticDdf.stream()
                .allMatch(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.CANCELED)))
            return;

        // all manual files canceled so we do noting here
        List<DirectDebitFile> manualDdf = dd.getDirectDebitFiles().stream()
                .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL)
                .collect(Collectors.toList());
        ;
        if (!manualDdf.isEmpty() && manualDdf.stream()
                .allMatch(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.CANCELED)))
            return;


        boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(dd.getDirectDebitFiles());
        boolean allAutoRejected = directDebitRejectionFlowService.allAutoFilesRejected(dd.getDirectDebitFiles());

        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category B auto files : " + allAutoRejected);
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category B manual files : " + allManualRejected);

        List<DirectDebitFile> rejectedDds =
                dd.getDirectDebitFiles().stream().filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)).collect(Collectors.toList());

        List<Payment> bouncedPayments = paymentRepository.findByDirectDebitIdAndStatus(dd.getId(), PaymentStatus.BOUNCED);
        List<Payment> bouncedNotReplacedPayments = bouncedPayments == null ? null :
                bouncedPayments.stream().filter(payment -> payment.getReplaced() == null || !payment.getReplaced())
                        .collect(Collectors.toList());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category A bouncedPayments : {0}", bouncedPayments == null ? bouncedPayments.size() : "0");


        boolean thereIsAForBouncingFile = dd.getDirectDebitFiles()
                .stream().anyMatch(ddf -> ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment());

        boolean thereIsANonForBouncingFile = dd.getDirectDebitFiles()
                .stream().anyMatch(ddf -> ddf.getForBouncingPayment() == null || !ddf.getForBouncingPayment());

        DirectDebitConfiguration ddConfiguration = dd.getDdConfiguration();
        logger.info("DD Configuration, isIncludeManualInDDBFlow: " + ddConfiguration.isIncludeManualInDDBFlow());

        if ((allAutoRejected && allManualRejected)
                || (allAutoRejected && !dd.isGenerateManualDDFsFromConfig())) { // all auto and manual is rejected

            logger.info("allAutoRejected && allManualRejected");
            if (Setup.getApplicationContext().getBean(OecAmendDDsService.class)
                    .handleOecFlow(dd)) {

                dd.setStatus(DirectDebitStatus.REJECTED);
                dd.setMStatus(DirectDebitStatus.REJECTED);
                dd.setAutoDdfFile(null);
                dd.setManualDdfFile(null);
                dd.setRejectionReason(rejectedDds.get(0).getRejectionReason());
                dd.setRejectCategory(rejectedDds.get(0).getRejectCategory());
                directDebitRepository.save(dd);

                DirectDebitRejectionToDo todo = dd.getDirectDebitRejectionToDo();

                if (todo != null) {
                    todo.setStopped(true);
                    todo.setCompleted(true);
                    directDebitRejectionToDoRepository.save(todo);
                }
            } else {
//                if (rejectedDds.get(0).getRejectCategory() == DirectDebitRejectCategory.EID
//                        || rejectedDds.get(0).getRejectCategory() == DirectDebitRejectCategory.Account) {
//
//                    if (!dd.isGenerateManualDDFsFromConfig() || !ddConfiguration.isIncludeManualInDDBFlow()) {
//                        dd.setMStatus(DirectDebitStatus.REJECTED);
//                        dd.setManualDdfFile(null);
//                    }
//                    logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute B reject is : " + rejectedDds.get(0).getRejectCategory());
//
//                    for (DirectDebitFile directDebitFile : dd.getDirectDebitFiles()) {
//                        if ((!dd.isGenerateManualDDFsFromConfig() || !ddConfiguration.isIncludeManualInDDBFlow())
//                                && directDebitFile.getDdMethod().equals(DirectDebitMethod.MANUAL)) {
//                            logger.info("Manual DDF -> step over it");
//                            continue;
//                        }
//
//                        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule B deleting FILE_TAG_DD_ACTIVATION for: " + directDebitFile.getId());
//                        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
//                        Attachment attachment = directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);
//                        attachementRepository.delete(attachment);
//                    }
//                } else {
                    dd.setStatus(DirectDebitStatus.REJECTED);
                    dd.setMStatus(DirectDebitStatus.REJECTED);
                    dd.setAutoDdfFile(null);
                    dd.setManualDdfFile(null);
//                }

                dd.setRejectionReason(rejectedDds.get(0).getRejectionReason());
                dd.setRejectCategory(rejectedDds.get(0).getRejectCategory());

                logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute category B case d reject category  : " + rejectedDds.get(0).getRejectCategory());

                startRejectionFlow(dd);

                if (switchingNationalityService.relatesToSwitching(dd.getId(), false)) {
                    switchingNationalityService.processCancellationToDos_fromDDRejection(dd);
                }
            }
        } else if ((allAutoRejected && dd.getMStatus() == DirectDebitStatus.CONFIRMED)) { // all auto rejected one manual at least is approved
            logger.info("allAutoRejected && dd.getMStatus() == DirectDebitStatus.CONFIRMED)");

            dd.setRejectionReason(rejectedDds.get(0).getRejectionReason());
            dd.setRejectCategory(rejectedDds.get(0).getRejectCategory());

            startRejectionFlow(dd);
        } else if (allManualRejected && thereIsAForBouncingFile && !bouncedPayments.isEmpty()) {

            if (bouncedNotReplacedPayments != null && !bouncedNotReplacedPayments.isEmpty()) {
                dd.setRejectionReason(rejectedDds.get(0).getRejectionReason());
                dd.setRejectCategory(rejectedDds.get(0).getRejectCategory());

                if (switchingNationalityService.relatesToSwitching(dd.getId(), true) && switchingNationalityService.doesDDCoverSwitchingPeriod(dd.getId())) {
                    logger.log(Level.SEVERE, "Related to Switching");
                    handleSwitchingNationalityBouncingRejection(dd, bouncedNotReplacedPayments);
                } else {
                    handleBouncingRejection(bouncedPayments, dd);
                }
            } else {
                logger.log(Level.SEVERE, "All Bounced Payments are Replaced");
            }
        } else if (allManualRejected && dd.getStatus() == DirectDebitStatus.CONFIRMED && thereIsANonForBouncingFile) {// all manual rejected one auto at least is approved and no bounced payments
            logger.log(Level.SEVERE, "At least 1 Automatic DD approved AND NO Manual DD approved and no Bouncing File");

            if (ddConfiguration.isIncludeManualInDDBFlow()) {
                logger.info("DD Configuration, isIncludeManualInDDBFlow");

                dd.setRejectionReason(rejectedDds.get(0).getRejectionReason());
                dd.setRejectCategory(rejectedDds.get(0).getRejectCategory());

                startRejectionFlow(dd);
            } else {
                dd.setMStatus(DirectDebitStatus.REJECTED);
                dd.setManualDdfFile(null);

                DirectDebitRejectionToDo directDebitRejectionToDo = dd.getDirectDebitRejectionToDo();

                if (directDebitRejectionToDo != null) {
                    directDebitRejectionToDo.setCompleted(true);
                    directDebitRejectionToDoRepository.save(directDebitRejectionToDo);
                }
            }
        }
        directDebitRepository.save(dd);
    }

    private DirectDebitRejectionToDo getDirectDebitToDo(DirectDebit entity) {
        DirectDebitRejectionToDo directDebitRejectionToDo = entity.getDirectDebitRejectionToDo();

        if (directDebitRejectionToDo == null) {
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute getDirectDebitToDo : creating todo");

            directDebitRejectionToDo = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class)
                    .startFlow(entity);

            if (directDebitRejectionToDo != null && directDebitRejectionToDo.isDdAddedByOecFlow()
                    && !directDebitRejectionToDo.isCompleted() && !directDebitRejectionToDo.isStopped()) {

                logger.info("getDirectDebitToDo isDdAddedByOecFlow " + directDebitRejectionToDo.getId());
                List<DirectDebit> dds = directDebitRejectionToDo.getDirectDebits();

                if (!dds.isEmpty()) {
                    DirectDebit lastDirectDebit = dds.get(dds.size() - 1);
                    logger.info("getDirectDebitToDo lastDirectDebit ID is " + lastDirectDebit.getId());

                    lastDirectDebit.setAddedByOecFlow(entity.isAddedByOecFlow());
                    lastDirectDebit.setOecSalary(entity.getOecSalary());
                    Setup.getRepository(DirectDebitRepository.class).save(lastDirectDebit);
                }
            }

            return null;  // that's mean we should do nothing
        } else if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()) {
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute getDirectDebitToDo : todo exist");
            return null;
        }

        directDebitRejectionToDo = directDebitRejectionToDoRepository.findOne(directDebitRejectionToDo.getId());
        directDebitRejectionToDo.setLastDirectDebit(entity);
        return directDebitRejectionToDo;
    }

    private void startRejectionFlow(DirectDebit directDebit) {
        DirectDebitRejectionToDo directDebitRejectionToDo = getDirectDebitToDo(directDebit);
        if (directDebitRejectionToDo != null && !directDebitRejectionToDo.isStopped() && !directDebitRejectionToDo.isCompleted()) {
            List<String> currentTasks = directDebitRejectionToDo.getCurrentTasks();
            if (!currentTasks.isEmpty()) {
                DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                switch (step) {
                    case WAITING_BANK_RESPONSE_B: {
                        Setup.getApplicationContext().getBean(DirectDebitBRejectionWaitingBankResponseStep.class)
                                .onDone(directDebitRejectionToDo);
                        break;
                    }
                    case WAITING_BANK_RESPONSE_B_CASE_D: {
                        Setup.getApplicationContext().getBean(DirectDebitBCaseDRejectionWaitingBankResponseStep.class)
                                .onDone(directDebitRejectionToDo);
                        break;
                    }
                }
            }
        }
    }

    private void handleBouncingRejection(List<Payment> bouncedPayments, DirectDebit dd) {
        Payment bouncedPayment = bouncedPayments != null ?
                Collections.max(bouncedPayments, Comparator.comparing(p -> p.getTrials())) : null;

        if (bouncedPayment == null) {
            logger.log(Level.SEVERE, "category B bouncedPayment == null");
            return;
        }

        int newTrial = bouncedPayment.getTrials() + 1;
        bouncedPayment.setTrials(newTrial);

        Contract contract = dd.getContractPaymentTerm().getContract();
        int paperModeThreshold = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_BOUNCING_REJECTION_SIGNING_PAPER_MODE_THRESHOLD_TRIAL));
        if (newTrial >= paperModeThreshold) {
            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .updatePaperModeAsync(dd.getContractPaymentTerm(), true);
        }

        int scheduleForTerminationThreshold = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_BOUNCING_No_MANUAL_DD_SCHEDULE_CONTRACT_FOR_TERMINATION_MAX_TRIAL));

        if (newTrial >= scheduleForTerminationThreshold) {
            logger.info("category B newTrial >= " + scheduleForTerminationThreshold);

            bouncedPayment.setContractScheduleDateOfTermination(
                    Setup.getApplicationContext()
                            .getBean(ContractService.class)
                            .setContractForTermination(contract, "Due bounced payment"));

            DirectDebitRejectionToDo directDebitRejectionToDo = dd.getDirectDebitBouncingRejectionToDo();
            if (directDebitRejectionToDo != null) {
                directDebitRejectionToDo.setStopped(true);
                directDebitRejectionToDo.setContractScheduleDateOfTermination(bouncedPayment.getContractScheduleDateOfTermination());
                directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

                directDebitRejectionFlowService.stopAllRejectionFlows(dd.getContractPaymentTerm(), directDebitRejectionToDo.getId());
            }
        } else {
            List<DirectDebitFile> manualsForBouncing = dd.getDirectDebitFiles()
                    .stream()
                    .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL
                            && ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)
                            && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                    .collect(Collectors.toList());

            if (!manualsForBouncing.isEmpty()) {
                dd.setBouncingRejectCategory(manualsForBouncing.get(manualsForBouncing.size() - 1).getRejectCategory());
            }

            DirectDebitRejectionToDo directDebitBouncingRejectionToDo = dd.getDirectDebitBouncingRejectionToDo();
            if (directDebitBouncingRejectionToDo == null || directDebitBouncingRejectionToDo.isStopped() ||
                    directDebitBouncingRejectionToDo.isCompleted()) {

                Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class)
                        .startTypeBBouncedFlow(dd);
            } else {
                directDebitBouncingRejectionToDo.setLastDirectDebit(dd);
                List<String> currentTasks = directDebitBouncingRejectionToDo.getCurrentTasks();
                if (!currentTasks.isEmpty()) {
                    DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                    if (step == DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED) {
                        Setup.getApplicationContext().getBean(DirectDebitBBouncedRejectionWaitingBankResponseStep.class)
                                .onDone(directDebitBouncingRejectionToDo);
                    }
                }
            }
        }

        paymentService.forceUpdatePayment(bouncedPayment);
    }

    private void handleSwitchingNationalityBouncingRejection(DirectDebit dd, List<Payment> bouncedPayments) {
        ContractPaymentTerm cpt = dd.getContractPaymentTerm();

        DateTime ddAStartDate = new DateTime();
        PicklistItem oneTimePaymentType = Setup.getItem("TypeOfPayment", "monthly_payment");
        try {
            ContractPaymentTerm nextCPT = switchingNationalityService.getNextCPT(dd.getContractPaymentTerm());

            Payment switchingMonthPayment = bouncedPayments.stream().filter(payment ->
                            PaymentHelper.doesPaymentCoverDate(payment, nextCPT.getSwitchOrReplaceNationalityDate()))
                    .findFirst().orElse(null);
            boolean doesSwitchingMonthPaymentBounced = switchingMonthPayment != null;

            boolean doesSwitchingNextMonthPaymentBounced = bouncedPayments.stream().anyMatch(payment ->
                    PaymentHelper.doesPaymentCoverDate(payment, new DateTime(nextCPT.getSwitchOrReplaceNationalityDate()).plusMonths(1).toDate()));

            boolean shouldCoverTwoMonths = doesSwitchingMonthPaymentBounced && doesSwitchingNextMonthPaymentBounced;

            Double ddAmount = shouldCoverTwoMonths ? (2 * dd.getAmount()) :
                    dd.getAmount();
            contractPaymentTermServiceNew.addNewDD(cpt.getContract(), ddAStartDate.toDate(), ddAStartDate.toDate(),
                    null, null, null,
                    ddAmount, null, DirectDebitType.ONE_TIME,
                    oneTimePaymentType, true, null, false, false, true, null, true);

            if (shouldCoverTwoMonths || !DDUtils.doesDDCoverDate(dd, new DateTime(nextCPT.getSwitchOrReplaceNationalityDate()).plusMonths(1).toDate())) {
                directDebitCancellationService.cancelWholeDD(dd, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
                switchingNationalityService.showCancellationToDos(dd);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Exception while creating ONE_TIME DD");
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    //ACC-4941
    private boolean checkPendingDdResult(DirectDebitFile directDebitFile) {
        logger.log(Level.INFO, "ddf id : {0}", directDebitFile.getId());
        DirectDebit directDebit = directDebitFile.getDirectDebit();

        if (directDebit.getDirectDebitRejectionToDo() == null ||
                directDebit.getDirectDebitRejectionToDo().getDirectDebits().stream().noneMatch(dd ->
                        dd.getCategory().equals(DirectDebitCategory.B)) ||
                (!directDebit.getDirectDebitRejectionToDo().isCompleted() &&
                        !directDebit.getDirectDebitRejectionToDo().isStopped())) return false;

        Contract contract = directDebit.getContractPaymentTerm().getContract();
        DateTime paidEndDate = paymentService.getLastReceivedMonthlyPaymentDate(contract);

        if (paidEndDate == null) return false;
        paidEndDate = paidEndDate.dayOfMonth().withMaximumValue();

        logger.log(Level.INFO, "dd id : {0}; startDate : {1}; expiryDate : {2}; ddf status {3}; paidEndDate : {4}",
                new Object[]{directDebit.getId(), directDebit.getStartDate(),
                        directDebit.getExpiryDate(), directDebitFile.getStatus(), paidEndDate});

        //ACC-4941 #2 -> 2 ->1
        if (new DateTime(directDebit.getExpiryDate()).isBefore(paidEndDate.plusHours(1))) {
            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class).cancelWholeDD(
                directDebit, DirectDebitCancellationToDoReason.CONTRACT_ADJUSTED_END_DATE_UPDATED);
                return true;
        }

        //ACC-4941 #2 -> 2 -> 3
        if (directDebitFile.getStatus().equals(DirectDebitFileStatus.APPROVED)) {
            SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
            query.filterBy("contract.id", "=", contract.getId());
            query.filterBy("status", "in", Arrays.asList(PaymentStatus.PDC, PaymentStatus.PRE_PDP));
            query.filterBy("dateOfPayment", "<=", paidEndDate.toDate());
            query.filterBy("methodOfPayment", "=", PaymentMethod.DIRECT_DEBIT);
            query.filterBy("typeOfPayment.code", "=", "monthly_payment");

            query.execute().forEach(p -> {
                logger.log(Level.INFO, "processing payment ID: {0}", p.getId());
                paymentService.deletePayment(p.getId());
            });

            return false;
        }

        //ACC-4941 #2 -> 2 -> 2
        if (paidEndDate.isAfter(new DateTime().plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay()) ||
                paymentRepository.paymentReceivedWithinDateAndMethodOfPaymentNotDirectDebit(contract,
                        new DateTime().getYear(), new DateTime().getMonthOfYear())) {

            logger.log(Level.INFO, "dd status {0}; dd MStatus: {1}; covers partial period",
                    new Object[]{directDebit.getStatus(), directDebit.getMStatus()});

            if (!directDebit.getStatus().equals(DirectDebitStatus.CANCELED) &&
                    !directDebit.getMStatus().equals(DirectDebitStatus.CANCELED)) {

                directDebit.setMStatus(DirectDebitStatus.REJECTED);
                directDebit.setStatus(DirectDebitStatus.REJECTED);
                directDebit.setRejectCategory(directDebitFile.getRejectCategory());
                directDebit.setRejectionReason(directDebitFile.getRejectionReason());
                directDebitRepository.save(directDebit);

                directDebit = directDebitRepository.findOne(directDebit.getId());

                Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class)
                        .generatePlansAfterCancelMonthlyPaymentDd(directDebit.getContractPaymentTerm(), paidEndDate,
                                Collections.singletonList(directDebit));
            }

            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class).cancelWholeDD(
                directDebit, DirectDebitCancellationToDoReason.CONTRACT_ADJUSTED_END_DATE_UPDATED);

            return true;
        }

        return false;
    }
}