package com.magnamedia.service;

import com.magnamedia.controller.ClientRefundTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.ManualDDFToSend;
import com.magnamedia.entity.projection.ManualDDFCsvProjection;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.ManualDDFBean;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.magnamedia.controller.DirectDebitFileController.MANUAL_DD_BATCH_FILE;
import com.magnamedia.controller.PaymentController;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Nov 02, 2020
 *         Jirra ACC-2739
 */

@Service
public class JobExecutionService {

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private ManualDDFToSendRepository manualDDFToSendRepository;

    @Autowired
    private PaymentController paymentController;

    @Autowired
    private BackgroundTaskService backgroundTaskService;

    @Autowired
    private PaymentRequestPurposeRepository paymentRequestPurposeRepository;
    
    @Transactional
    public void runReferralDiscountScheduledJob(Long contractID) {
        // get discount amount from sales module
        ClientRefundTodoController clientRefundController = Setup.getApplicationContext().getBean(ClientRefundTodoController.class);

        Double amount = Double.parseDouble(Setup.getParameter(Setup.getModule("sales"), AccountingModule.SM_DISCOUNT_CODE_VALUE));
        List<PaymentRequestPurpose> paymentRequestPurposes = paymentRequestPurposeRepository.findByForClientAndNameEquals(true, "Discount Code");
        if (paymentRequestPurposes == null || paymentRequestPurposes.isEmpty()) return;

        PaymentRequestPurpose paymentRequestPurpose = paymentRequestPurposes.get(0);

        Contract contract = contractRepository.findOne(contractID);
        Client client = clientRepository.findFirstOneByDiscountCode(contract.getDiscountCode());
        if (client == null) return;

        Contract contract1 = contract;
        if (client.getId() != contract.getClient().getId())
            contract = contractRepository.findFirstOneByClientAndStatusInOrderByCreationDateDesc(client,
                    new ContractStatus[]{ContractStatus.ACTIVE, ContractStatus.PLANNED_RENEWAL});
        // Last record created with the same type on 2021-04-05
        ClientRefundToDo clientRefundToDo = new ClientRefundToDo();
        clientRefundToDo.setAutomaticRefund(true);
        clientRefundToDo.setFlowTriggered("Contract has a discount code");
        clientRefundToDo.setPurpose(paymentRequestPurpose);
        clientRefundToDo.setRequestType(ClientRefundRequestType.DISCOUNT_REFUND);
        clientRefundToDo.setContract(contract);

        client.setContractPaymentTermInfo(contract);
        clientRefundToDo.setClient(client);
        clientRefundToDo.setMethodOfPayment(ClientRefundPaymentMethod.BANK_TRANSFER);
        clientRefundToDo.setAmount(amount);
        clientRefundToDo.setEid(client.getEid());
        clientRefundToDo.setIban(client.getClientIBAN());
        clientRefundToDo.setAccountName(client.getAccountName());

        clientRefundToDo.setIgnoreRequestedByConstraint(true);

        clientRefundController.createEntity(clientRefundToDo);

        contract1.setRefundSentToExpensify(true);
        contractRepository.save(contract1);
    }

    @Transactional
    public File runSendingApprovedManualDDToBankScheduledJob(Map<String, List<ManualDDFBean>> map, String key, List<ManualDDFBean> manualDDFBeanList, String[] columns) throws Exception {
        if (manualDDFBeanList == null || manualDDFBeanList.isEmpty()) return null;

        ManualDDFToSend manualDDFToSend = new ManualDDFToSend();
        manualDDFToSend.setFileName(key + ".ddr");
        String ids = "";
        for (ManualDDFBean manualDDFBean : map.get(key))
            ids += manualDDFBean.getId() + ",";
        manualDDFToSend.setIds(ids.substring(0, ids.length() - 1));
        manualDDFToSend.setGeneratingDate(new Date());

        File csvFile = CsvHelper.generateCsv(manualDDFBeanList, ManualDDFCsvProjection.class, columns, key, ".DDR");

        Attachment attachment = Storage.storeTemporary(key + ".DDR", new FileInputStream(csvFile), MANUAL_DD_BATCH_FILE, true);
        manualDDFToSend.addAttachment(attachment);
        manualDDFToSendRepository.save(manualDDFToSend);

        for (Long ddfID : Arrays.stream(manualDDFToSend.getIds().split(","))
                .filter(id -> !id.isEmpty())
                .map(Long::parseLong).collect(Collectors.toList())) {
            DirectDebitFile ddf = directDebitFileRepository.findOne(ddfID);
            if (ddf != null) {
                ddf.setManualDDFToSendId(manualDDFToSend.getId());
                directDebitFileRepository.silentSave(ddf);
            }
        }

        // mark payments as sent to bank
        for (ManualDDFBean manualDDFBean : map.get(key)) {
            Setup.getApplicationContext().getBean(PaymentService.class)
                    .setPaymentSentToBank(manualDDFBean.getRelatedPayment().getId());
        }


        return csvFile;
    }

    public void createBackGroundTask(String jobName, String targetedBean, String targetedMethod, BaseEntity entity) {
        Long time = new Date().getTime();

        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "AccountingJob_" + jobName + "_" + entity.getId() + "_" + time,
                targetedBean,
                "accounting",
                targetedMethod,
                entity.getEntityType(),
                entity.getId(),
                true,
                false,
                new Class<?>[]{Long.class},
                new Object[]{entity.getId()});
    }
}
