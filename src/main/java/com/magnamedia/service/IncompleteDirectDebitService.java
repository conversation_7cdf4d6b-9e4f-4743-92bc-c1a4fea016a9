package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitSignatureRepository;
import com.magnamedia.repository.DisablePushNotificationRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


@Service
public class IncompleteDirectDebitService {
    private static final Logger logger = Logger.getLogger(IncompleteDirectDebitService.class.getName());

    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private DirectDebitService directDebitService;

    public void startIncompleteFlowMissingBankInfo(ContractPaymentTerm cpt) {
        // the new trigger of the flow is having an Incomplete DD not only due to missing Signatures but due to the miss of any needed bank info
        // and there is no “Incomplete flow / Data entry rejection” running on those DDs
        if (cpt.getIsIBANRejected() ||
                cpt.getIsEidRejected() ||
                cpt.getIsAccountHolderRejected()) return;

        Map<String, Object> m = validateFlowStopping(cpt.getContract(), null);
        if ((boolean) m.get("stopped") || (boolean) m.get("completed")) return;

        if (cpt.getContract().isSigningPaperMode()) {
            List<DirectDebitSignature> signatures = Setup.getApplicationContext()
                    .getBean(DirectDebitSignatureService.class)
                    .getSignatureSortedList(cpt.getContract().getClient(), cpt.getEid());

            if (!signatures.isEmpty()) {
                signatures.forEach(d -> d.setDisable(true));
                Setup.getRepository(DirectDebitSignatureRepository.class).save(signatures);
            }
        }

        logger.info("start incomplete flow cpt id: " + cpt.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("trials", 1);
        map.put("reminders", 0);
        map.put("lastExecutionDate", new Date());

        flowProcessorService.createFlowProcessor(
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO,
                FlowSubEventConfig.FlowSubEventName.MISSING_BANK_INFO,
                cpt,
                map);
    }

    public Map<String, Object> validateFlowStopping(Contract c, FlowProcessorEntity f) {
        Map<String, Object> m = new HashMap<>();
        m.put("completed", false);
        m.put("stopped", false);

        if (c.isPayingViaCreditCard()) { // ACC-6703
            logger.log(Level.INFO, "entity id: {0}; Flow stopping because the contract flagged as paying via credit card", (f == null ? "Null" : f.getId()));
            m.put("stopped", true);
            return m;
        }

        if (f != null && flowProcessorService.clientProvidesSignatureAndBankInfo(c, f.getCreationDate())) {
            logger.log(Level.INFO, "entity id: {0}; Flow stopping because client provides missing bank info", f.getId());
            m.put("completed", true);
            return m;
        }

        // ACC-8526
        if (!directDebitService.existsByDdStatuses(c.getActiveContractPaymentTerm(), Collections.singletonList(DirectDebitStatus.IN_COMPLETE))) {
            logger.info("entity id: " + (f == null ? "NULL" : f.getId()) + "; Flow stopping because there isn't any incomplete DDs");
            m.put("stopped", true);
            return m;
        }
        return m;
    }

    public String getSignDdLinkForIncompleteFlowMissingBankInfo(Map<String, Object> map, boolean withShortLink) {

        StringBuilder currentFlow = new StringBuilder("&currentFlow=incomplete_flow_missing_bank_info");
        List<String> l = Setup.getApplicationContext()
                        .getBean(DirectDebitService.class)
                                .getMissingBankInfo(((ContractPaymentTerm) map.get("cpt")));

        for (String s : l) {
            switch (s){
                case "Signatures":
                    currentFlow.append("&signatureIsMissing=true");
                    break;
                case "IBAN":
                    currentFlow.append("&ibanIsMissing=true");
                    break;
                case "Account name":
                    currentFlow.append("&accountNameIsMissing=true");
                    break;
                case "EID":
                    currentFlow.append("&eidIsMissing=true");
                    break;
            }
        }

        if (currentFlow.toString().equals("&currentFlow=incomplete_flow_missing_bank_info")) {
            currentFlow.append("&signatureIsMissing=true");
        }

        return AccountingLinkService.getSignDdLink(map, currentFlow.toString(), withShortLink);
    }

    public void stopMissingBankInfoFlow(Contract c) {
        stopMissingBankInfoFlow(c, false);
    }

    public void stopMissingBankInfoFlow(Contract c, boolean isCompleted) {
        PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);
        FlowProcessorEntityRepository flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);

        FlowProcessorEntity f = flowProcessorService.getFirstRunningFlow
                (c, FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);
        if (f == null) return;

        logger.info("flow id: " + f.getId());
        if (isCompleted) {
            f.setCompleted(true);
        } else {
            f.setStopped(true);
        }
        flowProcessorEntityRepository.save(f);

        pushNotificationHelper.stopDisplaying(
                Setup.getRepository(DisablePushNotificationRepository.class)
                        .findActiveNotificationsByDDMessagingType(
                                c.getClient().getId().toString(),
                                DDMessagingType.IncompleteDDClientHasNoApprovedSignature,
                                c.getId()));
    }

    public boolean validateAndStartIncompleteFlowMissingBankInfo(List<DirectDebit> dds, ContractPaymentTerm cpt) {

        if (dds.stream().anyMatch(dd -> !dd.isHidden() && cpt.getContract().getStatus().equals(ContractStatus.ACTIVE) &&
                (dd.getDirectDebitRejectionToDo() == null && dd.getDirectDebitBouncingRejectionToDo() == null) &&
                ((dd.getCategory().equals(DirectDebitCategory.A) && dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) ||
                        (dd.getCategory().equals(DirectDebitCategory.B) && dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE))))) {

            logger.info("startIncompleteFlowMissingBankInfo for ddc Id: " + cpt.getDdcId());
            Setup.getApplicationContext()
                    .getBean(IncompleteDirectDebitService.class)
                    .startIncompleteFlowMissingBankInfo(dds.get(0).getContractPaymentTerm());
            return true;
        }
        return false;
    }
}