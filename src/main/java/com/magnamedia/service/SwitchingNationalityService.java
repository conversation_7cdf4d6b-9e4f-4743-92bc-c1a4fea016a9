package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TechnicalException;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.controller.ContractPaymentTermController.FILE_TAG_PAYMENTS_RECEIPT;

/**
 * <AUTHOR> Masod <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 23, 2020
 *          ACC-1689
 * 
 */

@Service
public class SwitchingNationalityService {
    protected static final Logger logger = Logger.getLogger(SwitchingNationalityService.class.getName());

    public enum SwitchingNationalityType {
        UPGRADING, DOWNGRADING, SAME_GRADE
    }

    private static Map<Long, Boolean> addVatDDsForContracts = new HashMap();
    private static Map<Long, Boolean> fromCCAPP_MAP = new HashMap();

    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private ContractRepository contractRep;
    @Autowired
    private HousemaidRepository housemaidRep;
    @Autowired
    private DirectDebitRepository ddRepo;
    @Autowired
    private DirectDebitFileRepository ddfRepo;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRep;
    @Autowired
    private ContractPaymentTermController cptController;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;
    @Autowired
    private DirectDebitCancelationToDoRepository ddCToDoRepo;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private SwitchingNationalityService service;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private  AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;

    public void oldCCSwitchingNationalityFlow(
            Contract contract,
            ContractPaymentTerm currentContractPaymentTerm,
            ContractPaymentTerm newCPT,
            DateTime replacementDate,
            Boolean keepCurrentDDs) throws Exception {

        logger.log(Level.SEVERE, "oldCCSwitchingNationalityFlow");
        List<DirectDebit> fullDDs;

        logger.log(Level.SEVERE, "replacementDate: " + replacementDate);

        List<DirectDebitStatus> statusesToMove = new ArrayList<>();
        statusesToMove.add(DirectDebitStatus.IN_COMPLETE);
        statusesToMove.add(DirectDebitStatus.PENDING_DATA_ENTRY);
        statusesToMove.add(DirectDebitStatus.PENDING);
        statusesToMove.add(DirectDebitStatus.CONFIRMED);

        logger.log(Level.SEVERE, "SWITCHING MAID ABD doesClientPayVat(contract): " + doesClientPayVat(contract));
        logger.log(Level.SEVERE, "SWITCHING MAID ABD hasToPayVat(): " + hasToPayVat(contract.getId()));
        logger.log(Level.SEVERE, "SWITCHING MAID ABD keepCurrentDDs: " + keepCurrentDDs);

        if (doesClientPayVat(contract) || keepCurrentDDs) {

            // move dds
            // ACC-1615
            fullDDs = ddRepo.findByContractPaymentTerm(currentContractPaymentTerm);

            logger.log(Level.SEVERE, "SWITCHING MAID ABD 1 fullDDs: " + fullDDs.size());

            List<ContractPayment> contractPayments = new ArrayList<>();
            for (DirectDebit directDebit : fullDDs) {

                logger.log(Level.SEVERE, "SWITCHING MAID ABD 1 directDebit id: " + directDebit.getId());
                logger.log(Level.SEVERE, "SWITCHING MAID ABD 1 directDebit category: " + directDebit.getCategory());
                logger.log(Level.SEVERE, "SWITCHING MAID ABD 1 directDebit status: " + directDebit.getStatus());
                logger.log(Level.SEVERE, "SWITCHING MAID ABD 1 directDebit MStatus: " + directDebit.getMStatus());

                directDebit.setContractPaymentTerm(newCPT);
                contractPaymentRep.findByDirectDebit(directDebit).forEach(contractPayment -> {
                    contractPayment.setContractPaymentTerm(newCPT);
                    contractPayments.add(contractPayment);
                });
            }

            ddRepo.save(fullDDs);
            contractPaymentRep.save(contractPayments);
            // case of: full payments are same and moved to the new contract payment term

        } else if (hasToPayVat(contract.getId())) {
            boolean after_SW_EOF = isAfter_SW_EOF(contract.getId(), replacementDate);
            Date ddGenerationDate = after_SW_EOF ? replacementDate.plusMonths(2).withDayOfMonth(1).toDate() : replacementDate.toDate();

            fullDDs = ddRepo.getDDForFullPaymentsToMove(currentContractPaymentTerm, ddGenerationDate);
            logger.info("fullDDs: " + fullDDs.size());

            int ddIndex = 0;
            for (DirectDebit dd : fullDDs) {
                logger.info("ID: " + dd.getId() +
                        "; category: " + dd.getCategory() +
                        "; status: " + dd.getStatus() +
                        "; MStatus: " + dd.getMStatus());

                if (dd.getCategory() == DirectDebitCategory.A &&
                        !statusesToMove.contains(dd.getMStatus())) {

                    logger.info("continue A");
                    continue;
                } else if (dd.getCategory() == DirectDebitCategory.B
                        && !statusesToMove.contains(dd.getStatus()) &&
                        !statusesToMove.contains(dd.getMStatus())) {

                    logger.info("continue B");
                    continue;
                }

                boolean isStartDateGreater = dd.getStartDate().getTime() > replacementDate.toDate().getTime();
                double vat = Math.ceil(DiscountsWithVatHelper.getVatAmount(dd.getAmount()));

                //ACC-2731, dd start-date
                switchingNationalityAddNewDD(contract, ddIndex == 0 && after_SW_EOF ?
                                replacementDate.plusMonths(2).withDayOfMonth(1).toDate() :
                                (isStartDateGreater ? dd.getStartDate() : replacementDate.toDate()),
                        dd.getExpiryDate(), null, null, null,
                        dd.getAmount() + vat, null, dd.getType(), null,
                        false, null, true, false, true,
                        null, true);

                ddIndex++;
            }
        }
    }

    public void indefiniteCCDifferentNationalityFlow(
            Contract contract,
            ContractPaymentTerm currentCPT,
            ContractPaymentTerm newCPT,
            DateTime replacementDate,
            SwitchingNationalityType switchingNationalityType,
            boolean saveAndRollback) throws Exception {

        logger.log(Level.SEVERE, "switchingNationalityType: " + switchingNationalityType);

        if (switchingNationalityType.equals(SwitchingNationalityType.UPGRADING)) {
            executeUpgradeNationalityFlow(contract, currentCPT, newCPT, replacementDate, saveAndRollback);
        } else if (switchingNationalityType.equals(SwitchingNationalityType.DOWNGRADING)) {
            executeDowngradeNationalityFlow(contract, currentCPT, newCPT, replacementDate, saveAndRollback);
        }
    }

    public void handleSwitchingNationalitySameGrade(
            Long contractId, Long newHousemaidId,
            Long replacementId, String replacementDate,
            Boolean keepCurrentDDs) throws Exception {

        executeSameGradeFlow(
                contractRep.findOne(contractId),
                housemaidRep.findOne(newHousemaidId),
                Setup.getRepository(ReplacementRepository.class).findOne(replacementId),
                DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(replacementDate),
                keepCurrentDDs);
    }

    // move old dd to new cpt and set the new housemaid
    public void executeSameGradeFlow(
            Contract contract, Housemaid newHousemaid,
            Replacement replacement, DateTime replacementDate, boolean keepCurrentDDs) throws Exception {

        logger.log(Level.INFO, "contract id: {0}; new housemaid id: {1}",
                new Object[]{contract.getId(), newHousemaid.getId()});

        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID, contract);
        if (a != null) {
            a.setIsDeleted(true);
            accountingEntityPropertyRepository.save(a);
        }

        ContractPaymentTerm newCPT = Setup.getApplicationContext().getBean(SwitchingNationalityService.class)
                .switchMaid(contract.getId(), newHousemaid.getId(), replacementDate,
                        false, keepCurrentDDs, false, replacement.isAddVatDDs());

        newCPT.setReplacement(replacement);
        Setup.getRepository(ContractPaymentTermRepository.class).save(newCPT);
    }

    // ACC-2800
    private void executeUpgradeNationalityFlow(
            Contract contract,
            ContractPaymentTerm currentCPT,
            ContractPaymentTerm newCPT,
            DateTime replacementDate,
            boolean saveAndRollback) throws Exception {

        logger.info("UpgradeNationalityFlow Started.");
        Date todayDate = new Date();
        DateTime eomDate = replacementDate.dayOfMonth().withMaximumValue();
        int daysToEOM = Days.daysBetween(replacementDate, eomDate).getDays() + 1;

        logger.info("new CPT created, ID: " + newCPT.getId());
        List<DirectDebitStatus> excludeStatuses = Arrays.asList(DirectDebitStatus.CANCELED,
                DirectDebitStatus.REJECTED, DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED);

        PicklistItem upgradingType = Setup.getItem("TypeOfPayment", "upgrading_nationality");
        PicklistItem monthly = Setup.getItem("TypeOfPayment", "monthly_payment");

        // if Related DD is rejected incomplete or pending not sent -> create ONE_TIME DD on the new CPT
        DateTime replacementMonth = new DateTime(replacementDate.toDate()).withDayOfMonth(1).withTimeAtStartOfDay();
        boolean replacementMonthMonthReceived = paymentRepository.paymentReceived(currentCPT.getContract(),
                monthly, replacementMonth.toDate(), replacementMonth.plusMonths(1).toDate());
        DirectDebit replacementMonthDD = getRelatedDD(currentCPT, replacementDate.toDate(), excludeStatuses);

        if(!replacementMonthMonthReceived) {
            handleUpgradingNationalityRequiredPayment(currentCPT, replacementDate, replacementMonthDD, true, saveAndRollback);
        }

        DateTime nextMonth = new DateTime(replacementDate.plusMonths(1).toDate())
                .withDayOfMonth(1).withTimeAtStartOfDay();
        boolean nextMonthReceived = paymentRepository.paymentReceived(currentCPT.getContract(),
                monthly, nextMonth.toDate(), nextMonth.plusMonths(1).toDate());
        DirectDebit replacementNextMonthDD = getRelatedDD(currentCPT, replacementDate.plusMonths(1).toDate(), excludeStatuses);

        // if is pro + 1 -> it should be covered with the previous payment
        if (!(DDUtils.doesDDCoverDate(replacementMonthDD, replacementDate.plusMonths(1).toDate()) &&
                shouldCreateOneTimeDD(replacementMonthDD)) && !nextMonthReceived) {

            logger.info("handleUpgradingNationalityRequiredPayment Next Month Payment");
            handleUpgradingNationalityRequiredPayment(currentCPT, replacementDate.plusMonths(1), replacementNextMonthDD, false, saveAndRollback);
        }

        //New Amount > Current Amount
        Double replacementMonthCurrentAmount = getCPTAmountAtTime(replacementDate.toDate(), currentCPT, false);
        Double replacementMonthNewAmount = getCPTAmountAtTime(replacementDate.toDate(), newCPT, true);

        logger.info("CurrentAmount: " + replacementMonthCurrentAmount + "; NewAmount: " + replacementMonthNewAmount);

        DateTime oldCPTEndAdditionalDiscountDate = new DateTime(getAdditionalDiscountEndDateInMillis(
                contract.getStartOfContract(), contract.getIsProRated(), currentCPT));

        double additionalDiscount = currentCPT.getAdditionalDiscount() != null &&
                replacementDate.isBefore(oldCPTEndAdditionalDiscountDate) ?
                        currentCPT.getAdditionalDiscount() : 0.0;
        double additionalDiscountPerPayment = currentCPT.getAdditionalDiscountMonthsCount(
                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) != 0 && additionalDiscount > 0 ?
                Math.round(additionalDiscount / currentCPT.getAdditionalDiscountMonthsCount(
                        AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE)) : 0.0;
        logger.info("additional discount: " + additionalDiscountPerPayment);

        // ACC-2200
        double vatPercent = DiscountsWithVatHelper.getVatPercent();

        Double replacementMonthCurrentAmountAfterDiscount = DiscountsWithVatHelper
                .getFinalAmountAfterAddDiscount(replacementMonthCurrentAmount, additionalDiscountPerPayment, vatPercent);

        Double replacementMonthNewAmountAfterDiscount = DiscountsWithVatHelper
                .getFinalAmountAfterAddDiscount(replacementMonthNewAmount, additionalDiscountPerPayment, vatPercent);

        if (additionalDiscountPerPayment != 0.0 && !replacementDate.plusMonths(1).isBefore(oldCPTEndAdditionalDiscountDate)) {
            additionalDiscountPerPayment = 0.0;
        }

        Double replacementNextMonthCurrentAmount = getCPTAmountAtTime(replacementDate.plusMonths(1).toDate(), currentCPT, false);
        Double replacementNextMonthCurrentAmountAfterDiscount = DiscountsWithVatHelper
                .getFinalAmountAfterAddDiscount(replacementNextMonthCurrentAmount, additionalDiscountPerPayment, vatPercent);

        Double replacementNextMonthNewAmount = getCPTAmountAtTime(replacementDate.plusMonths(1).toDate(), newCPT, true);
        Double replacementNextMonthNewAmountAfterDiscount = DiscountsWithVatHelper
                .getFinalAmountAfterAddDiscount(replacementNextMonthNewAmount, additionalDiscountPerPayment, vatPercent);

        Integer replacementMonthDays = replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();

        Double thisMonthDdAAmount = (NumberFormatter.twoDecimalPoints(((replacementMonthNewAmountAfterDiscount - replacementMonthCurrentAmountAfterDiscount) / replacementMonthDays) * daysToEOM));
        Double nextMonthDdAAmount = replacementNextMonthNewAmountAfterDiscount;

        boolean nextMonthCovered = replacementNextMonthDD != null;
        boolean applyUpgradingFee = nextMonthReceived || nextMonthCovered;

        logger.info("next month payment received: " + applyUpgradingFee);
        PicklistItem nextMonthType = monthly; // ACC-3544
        if (applyUpgradingFee) {
            nextMonthDdAAmount -= replacementNextMonthCurrentAmountAfterDiscount;
            nextMonthType = upgradingType;
        }

        // if replacement was previous month -> current month DDA will hold upgrading fee of previous month
        boolean mergeNewDDAs = replacementDate.getMonthOfYear() != new DateTime().getMonthOfYear();
        if (mergeNewDDAs) {
            Double ddAAmount = thisMonthDdAAmount + nextMonthDdAAmount;

            if (ddAAmount > 0.0) {
                switchingNationalityAddNewDD(contract, todayDate, todayDate,
                        null, null, null, Math.floor(ddAAmount), null, DirectDebitType.ONE_TIME,
                        nextMonthType, !saveAndRollback, null, false, false, !saveAndRollback, null, !saveAndRollback);
            }
        } else {
            if (thisMonthDdAAmount > 0.0) {
                Date ddAStartDate = new Date();
                switchingNationalityAddNewDD(contract, ddAStartDate, ddAStartDate,
                        null, null, null, Math.floor(thisMonthDdAAmount), null, DirectDebitType.ONE_TIME,
                        upgradingType, !saveAndRollback, null, false, false, !saveAndRollback, null, !saveAndRollback);
            }

            if (nextMonthDdAAmount > 0.0) {
                Date ddAStartDate = new DateTime().plusMonths(1).dayOfMonth().withMinimumValue().toDate();
                switchingNationalityAddNewDD(contract, ddAStartDate, ddAStartDate,
                        null, null, null,
                        Math.floor(nextMonthDdAAmount), null, DirectDebitType.ONE_TIME,
                        nextMonthType, !saveAndRollback, null, false, false, !saveAndRollback, null, !saveAndRollback);
            }
        }

        Date ddCreationDate = replacementDate.plusMonths(2).dayOfMonth().withMinimumValue().toDate();
        logger.info("ddCreationDate: " + ddCreationDate);

        startDDsGenerationFlow(ddCreationDate, currentCPT, newCPT, contract, replacementDate, true, saveAndRollback);
        logger.info("UpgradeNationalityFlow Ended.");
    }

    // ACC-2933
    private void executeDowngradeNationalityFlow(Contract contract, ContractPaymentTerm currentCPT, ContractPaymentTerm newCPT,
                                                 DateTime replacementDate, boolean saveAndRollback) throws Exception {
        logger.log(Level.SEVERE, "DowngradeNationalityFlow Started.");

        if (!contract.getStatus().equals(ContractStatus.CANCELLED)) {

            List<DirectDebitStatus> excludeStatuses = Arrays.asList(DirectDebitStatus.CANCELED, DirectDebitStatus.REJECTED, 
                    DirectDebitStatus.PENDING_FOR_CANCELLATION, DirectDebitStatus.EXPIRED);

            List<DirectDebit> oldDDs = getDDsOfCPT(currentCPT);

            List<DirectDebit> oneTimeDDs = oldDDs != null
                    ? oldDDs.stream().filter(dd -> dd.getCategory().equals(DirectDebitCategory.A) && !excludeStatuses.contains(dd.getMStatus())).collect(Collectors.toList())
                    : new ArrayList();

            List<DirectDebit> unReceivedOneTimeDDs = new ArrayList();
            for (DirectDebit directDebit : oneTimeDDs) {
                List<Payment> ddPayments = getPaymentsOfDD(directDebit, null, null, null, null);
                if (ddPayments != null && !ddPayments.isEmpty() &&
                        ddPayments.stream().anyMatch(payment -> payment.getStatus().equals(PaymentStatus.PRE_PDP) || payment.getStatus().equals(PaymentStatus.PDC) ||
                                (payment.getStatus().equals(PaymentStatus.BOUNCED) && (payment.getReplaced() == null || !payment.getReplaced())))) {
                    unReceivedOneTimeDDs.add(directDebit);
                }
            }

            for (DirectDebit directDebit : unReceivedOneTimeDDs) {
                directDebit.setContractPaymentTerm(newCPT);
                directDebit.setRequiredAfterSwitching(true);
                ddRepo.save(directDebit);
            }

            PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");
            List<Payment> bouncedPayments = getPaymentsOfCPT(currentCPT, null, Arrays.asList(PaymentStatus.BOUNCED), PaymentMethod.DIRECT_DEBIT, null);
            List<Payment> bouncedDDBNotReplacedPayments = bouncedPayments != null
                    ? bouncedPayments.stream().filter(payment -> payment.getDirectDebit() != null && payment.getDirectDebit().getCategory().equals(DirectDebitCategory.B) &&
                    payment.getDirectDebit().getStatus().equals(DirectDebitStatus.CONFIRMED) &&
                    payment.getReplaced() == null || !payment.getReplaced()).collect(Collectors.toList())
                    : new ArrayList();

            for (Payment bouncedPayment : bouncedDDBNotReplacedPayments) {
                logger.info("Bounced not Replaced Payment#" + bouncedPayment.getId());

                DirectDebit directDebit = bouncedPayment.getDirectDebit();

                //Is there an approved manual DD
                logger.log(Level.SEVERE, "no Approved Manual");
                DirectDebit newDD = switchingNationalityAddNewDD(contract, new Date(), new Date(), null, null, null,
                        bouncedPayment.getAmountOfPayment(), null, DirectDebitType.ONE_TIME,
                        monthlyPayment, !saveAndRollback, null, false, false, !saveAndRollback, null, !saveAndRollback);

                newDD.setRequiredAfterSwitching(true);
                ddRepo.save(newDD);

                // ACC-3001
                bouncedPayment.setDirectDebitId(newDD.getId());
                bouncedPayment.setDirectDebitFileId(null);
                paymentService.forceUpdatePayment(bouncedPayment);

                if (!saveAndRollback) {
                    directDebitCancellationService.cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
                }
            }


            if (unReceivedOneTimeDDs.isEmpty() && bouncedDDBNotReplacedPayments.isEmpty() && !saveAndRollback) {
                downgradeFlowAddClientRefund(currentCPT, newCPT, replacementDate, contract);
            }

            logger.info("Create DDBs");
            int ddGenerationMonthsShift = 1;

            DateTime ddCreationDate = DateTime.now().plusMonths(ddGenerationMonthsShift).dayOfMonth().withMinimumValue();
            logger.log(Level.SEVERE, "ddCreationDate: " + ddCreationDate);

            Integer defaultContractDuration = getDefaultPaymentsDuration(contract);
            Double nextMonthNewAmount = getCPTAmountAtTime(ddCreationDate.toDate(), newCPT, false);
            if (nextMonthNewAmount > 0) {
            switchingNationalityAddNewDD(contract, ddCreationDate.toDate(), ddCreationDate.plusMonths(defaultContractDuration - 1).toDate(),
                        null, null, null, nextMonthNewAmount,
                        null, DirectDebitType.MONTHLY, null, !saveAndRollback,
                        null, false, false, !saveAndRollback, null, !saveAndRollback);
            }

            if (!saveAndRollback) {
                List<DirectDebit> oldDDBs = getDDsOfCPT(currentCPT)
                        .stream()
                        .filter(dd -> dd.getCategory().equals(DirectDebitCategory.B))
                        .collect(Collectors.toList());

                for (DirectDebit directDebit : oldDDBs) {
                    directDebitCancellationService.cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
                }
            }
        } else if (!saveAndRollback && downgradeFlowCheckContractCancelledBeforeAddRefund(contract, replacementDate, false)) {
                
            downgradeFlowAddClientRefund(currentCPT, newCPT, replacementDate, contract);
        }

        logger.log(Level.SEVERE, "DowngradeNationalityFlow Ended.");
    }

    public void cancelDDsAfterUpgrading(DateTime replacementDate, ContractPaymentTerm currentCPT, List<DirectDebit> directDebits) {
        Date switchingMonthPaymentDate = replacementDate.toDate();
        Date switchingNextMonthPaymentDate = replacementDate.plusMonths(1).toDate();

        PicklistItem monthlyPaymentType = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");

        Payment switchingMonthPayment = getPaymentOfCPTByDate(currentCPT, switchingMonthPaymentDate, Arrays.asList(PaymentStatus.RECEIVED, PaymentStatus.BOUNCED),
                PaymentMethod.DIRECT_DEBIT, monthlyPaymentType);

        Payment switchingNextMonthPayment = getPaymentOfCPTByDate(currentCPT, switchingNextMonthPaymentDate, Arrays.asList(PaymentStatus.RECEIVED, PaymentStatus.BOUNCED),
                PaymentMethod.DIRECT_DEBIT, monthlyPaymentType);

        boolean isSwitchingMonthPaymentReceived = switchingMonthPayment != null && (switchingMonthPayment.getStatus().equals(PaymentStatus.RECEIVED) ||
                (switchingMonthPayment.getStatus().equals(PaymentStatus.BOUNCED) && BooleanUtils.toBoolean(switchingMonthPayment.getReplaced())));

        boolean isSwitchingNextMonthPaymentReceived = switchingNextMonthPayment != null && (switchingNextMonthPayment.getStatus().equals(PaymentStatus.RECEIVED) ||
                (switchingNextMonthPayment.getStatus().equals(PaymentStatus.BOUNCED) && BooleanUtils.toBoolean(switchingNextMonthPayment.getReplaced())));

        for (DirectDebit directDebit : directDebits) {
            boolean hidden = false;

            if (DDUtils.doesDDCoverDate(directDebit, switchingMonthPaymentDate) && !isSwitchingMonthPaymentReceived)
                hidden = true;
            if (!hidden && DDUtils.doesDDCoverDate(directDebit, switchingNextMonthPaymentDate) && !isSwitchingNextMonthPaymentReceived)
                hidden = true;

            directDebitCancellationService.cancelWholeDD(directDebit, hidden, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
        }
    }

    private List<Payment> getPaymentsOfCPT(ContractPaymentTerm contractPaymentTerm, Date date, List<PaymentStatus> paymentStatuses, PaymentMethod method, PicklistItem type) {
        List<DirectDebit> cptDDs = getDDsOfCPT(contractPaymentTerm);
        logger.log(Level.SEVERE, "cptDDs Size: " + cptDDs.size());
        List<Payment> payments = new ArrayList();

        if (cptDDs != null) {
            for (DirectDebit directDebit : cptDDs) {
                logger.log(Level.SEVERE, "directDebit ID#" + directDebit.getId());

                payments.addAll(getPaymentsOfDD(directDebit, date, paymentStatuses, method, type));
            }
        }

        return payments;
    }

    private Payment getPaymentOfCPTByDate(ContractPaymentTerm contractPaymentTerm, Date date, List<PaymentStatus> paymentStatuses, PaymentMethod method, PicklistItem type) {
        List<DirectDebit> cptDDs = getDDsOfCPT(contractPaymentTerm);
        logger.log(Level.SEVERE, "cptDDs Size: " + cptDDs.size());

        if (cptDDs != null) {
            for (DirectDebit directDebit : cptDDs) {
                logger.log(Level.SEVERE, "directDebit ID#" + directDebit.getId());

                List<Payment> payments = getPaymentsOfDD(directDebit, date, paymentStatuses, method, type);
                logger.log(Level.SEVERE, "payments Size: " + payments.size());

                for (Payment payment : payments) {
                    logger.log(Level.SEVERE, "Payment ID#" + payment.getId());

                    if (PaymentHelper.doesPaymentCoverDate(payment, date)) {
                        logger.log(Level.SEVERE, "Payment Match");
                        return payment;
                    }
                }
            }
        }

        return null;
    }

    public List<Payment> getPaymentsOfDD(DirectDebit directDebit, Date date, List<PaymentStatus> paymentStatuses, PaymentMethod method, PicklistItem type) {
        DateTime fromDate = date != null ? new DateTime(date).dayOfMonth().withMinimumValue().withTimeAtStartOfDay() : null;
        DateTime toDate = date != null ? new DateTime(date).dayOfMonth().withMaximumValue().withTimeAtStartOfDay() : null;

        SelectQuery<Payment> query = new SelectQuery(Payment.class);
        query.filterBy("directDebitId", "=", directDebit.getId());

        if (paymentStatuses != null && !paymentStatuses.isEmpty()) {
            query.filterBy("status", "in", paymentStatuses);
        }
        if (method != null) {
            query.filterBy("methodOfPayment", "=", method);
        }
        if (type != null && type.getId() != null) {
            query.filterBy("typeOfPayment.id", "=", type.getId());
        }
        if (fromDate != null) {
            query.filterBy("dateOfPayment", ">=", fromDate.minusMonths(1).toDate());
        }
        if (toDate != null) {
            query.filterBy("dateOfPayment", "<=", toDate.toDate());
        }

        return query.execute();
    }

    public boolean doesPaymentCoverSwitchingPeriod(Long paymentId) {
        if (paymentId == null) return false;
        Payment payment = paymentRepository.findOne(paymentId);
        if (payment == null) return false;
        ContractPaymentTerm cpt = payment.getDirectDebit().getContractPaymentTerm();
        if (cpt == null || cpt.isActive()) {
            logger.log(Level.SEVERE, "cpt is NULL or Active");
            return false;
        }
        ContractPaymentTerm nextCPT = getNextCPT(cpt);

        if (nextCPT == null) {
            logger.log(Level.SEVERE, "next CPT is null");
            return false;
        }

        DateTime replacementDate = new DateTime(nextCPT.getSwitchOrReplaceNationalityDate());
        boolean after_SW_EOF = isAfter_SW_EOF(nextCPT.getContract().getId(), replacementDate);

        DateTime targetDate = replacementDate.withDayOfMonth(1);

        return PaymentHelper.doesPaymentCoverDate(payment, targetDate.toDate()) || (after_SW_EOF && PaymentHelper.doesPaymentCoverDate(payment, targetDate.plusMonths(1).toDate()));
    }

    public ContractPaymentTerm getNextCPT(ContractPaymentTerm contractPaymentTerm) {
        return contractPaymentTermRepository.findFirstByContractAndIdGreaterThanOrderById(contractPaymentTerm.getContract(), contractPaymentTerm.getId());
    }

    public void startDDsGenerationFlow(Date ddStartDate, ContractPaymentTerm currentCPT, ContractPaymentTerm newCPT, Contract contract,
                                       DateTime replacementDate, boolean isUpgradingNationality, boolean saveAndRollBack) throws Exception {
        logger.log(Level.SEVERE, "indefiniteCCDifferentNationalityFlow Started.");
        logger.log(Level.SEVERE, "startDDsGenerationFlow Started.");
        logger.log(Level.SEVERE, "replacementDate: " + replacementDate);
        logger.log(Level.SEVERE, "ddStartDate: " + ddStartDate);

        DateTime startDiscountDate = new DateTime(getDiscountStartDateInMillis(contract.getStartOfContract(),
                contract.getIsProRated(), newCPT));

        Double currentCPTPremiumAmount = currentCPT.getMonthlyPayment();
        Double newCPTPremiumAmount = newCPT.getMonthlyPayment();

        Double currentCPTDiscountedAmount = currentCPT.getDiscountedMonthlyPayment();
        Double newCPTDiscountedAmount = newCPT.getDiscountedMonthlyPayment();

        Integer defaultContractDuration = getDefaultPaymentsDuration(contract);

        double additionalDiscount = currentCPT.getAdditionalDiscount() != null
                ? currentCPT.getAdditionalDiscount() : 0.0;
        double additionalDiscountPerPayment = currentCPT.getAdditionalDiscountMonthsCount(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) != 0
                && additionalDiscount > 0
                ? Math.round(additionalDiscount / currentCPT.getAdditionalDiscountMonthsCount(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                : 0.0;

        //DD Start Date during Premium period
        Date fullAmountStartDate = new Date(ddStartDate.getTime());
        DateTime oldCPTEndAdditionalDiscountDate = new DateTime(getAdditionalDiscountEndDateInMillis(contract.getStartOfContract(), contract.getIsProRated(), currentCPT));

        if (fullAmountStartDate.before(startDiscountDate.toDate())) {
            //New and Old Premium Amounts are different

            List<DirectDebit> currentFullDDs = ddRepo.getDDForFullPaymentsToMove(
                    currentCPT, startDiscountDate.toDate(), replacementDate.toDate());

            if (!currentCPTPremiumAmount.equals(newCPTPremiumAmount) || currentFullDDs == null || currentFullDDs.isEmpty() || isUpgradingNationality) {
                //if isUpgradingNationality, change discount date cuze in this case we are behaving like we are creation new contract
                int fullMonthlyPaymentMonthsDifferent = new DateTime(fullAmountStartDate).withDayOfMonth(1).compareTo(oldCPTEndAdditionalDiscountDate) == -1
                        ? Months.monthsBetween(new DateTime(fullAmountStartDate).withDayOfMonth(1), oldCPTEndAdditionalDiscountDate.dayOfMonth().withMaximumValue()).getMonths() : 0;
                Double newAdditionalDiscount = additionalDiscountPerPayment > 0
                        ? additionalDiscountPerPayment * fullMonthlyPaymentMonthsDifferent : null;

                // ACC-2200, for additional discount
                if (newAdditionalDiscount != null && newAdditionalDiscount > 0) {
                    if (oldCPTEndAdditionalDiscountDate.withDayOfMonth(1).toDate().before(startDiscountDate.withDayOfMonth(1).toDate())) {
                        generateDDsFlow(contract, currentCPT, fullAmountStartDate, oldCPTEndAdditionalDiscountDate.minusMonths(1).toDate(), newCPTPremiumAmount,
                                newAdditionalDiscount, saveAndRollBack);
                        fullAmountStartDate = oldCPTEndAdditionalDiscountDate.toDate();
                        newAdditionalDiscount = null;
                    } else {
                        fullMonthlyPaymentMonthsDifferent = new DateTime(fullAmountStartDate).withDayOfMonth(1).compareTo(startDiscountDate) == -1
                                ? Months.monthsBetween(new DateTime(fullAmountStartDate).dayOfMonth().withMinimumValue(), startDiscountDate.dayOfMonth().withMaximumValue()).getMonths() : 0;
                        newAdditionalDiscount = additionalDiscountPerPayment > 0
                                ? additionalDiscountPerPayment * fullMonthlyPaymentMonthsDifferent : null;
                    }
                }

                Date ddEndDate = startDiscountDate.minusMonths(1).dayOfMonth().withMaximumValue().toDate();
                // if starts before it's end :)
                if (ddEndDate.after(fullAmountStartDate) || ddEndDate.equals(fullAmountStartDate)) {
                    generateDDsFlow(contract, currentCPT, fullAmountStartDate, ddEndDate, newCPTPremiumAmount,
                            newAdditionalDiscount, saveAndRollBack);
                }
            }

            ddStartDate = startDiscountDate.toDate();
        }

        List<DirectDebit> currentDiscountedDDs = ddRepo.getDDForDiscountPaymentsToMove(currentCPT,
                replacementDate.toDate(), startDiscountDate.minusMonths(1).toDate());

        //New and Old Discounted Amounts are different
        if (!currentCPTDiscountedAmount.equals(newCPTDiscountedAmount) || currentDiscountedDDs == null || currentDiscountedDDs.isEmpty() || isUpgradingNationality) {
            DateTime nextDDStartDate = new DateTime(ddStartDate).dayOfMonth().withMinimumValue().withTimeAtStartOfDay();

            int discountMonthlyPaymentMonthsDifferent = nextDDStartDate.compareTo(oldCPTEndAdditionalDiscountDate) == -1
                    ? Months.monthsBetween(nextDDStartDate, oldCPTEndAdditionalDiscountDate).getMonths() : 0;
            Double newAdditionalDiscount = additionalDiscountPerPayment > 0
                    ? additionalDiscountPerPayment * discountMonthlyPaymentMonthsDifferent : null;

            if (newAdditionalDiscount != null && newAdditionalDiscount > 0) {
                generateDDsFlow(contract, newCPT, nextDDStartDate.toDate(), oldCPTEndAdditionalDiscountDate.minusMonths(1).toDate(),
                        newCPTDiscountedAmount, newAdditionalDiscount, saveAndRollBack);

                nextDDStartDate = oldCPTEndAdditionalDiscountDate;
            }

            generateDDsFlow(contract, newCPT, nextDDStartDate.toDate(), new DateTime(replacementDate).plusMonths(defaultContractDuration).toDate(),
                    newCPTDiscountedAmount, null, saveAndRollBack);
        }
    }

    private void generateDDsFlow(Contract contract, ContractPaymentTerm currentCPT, Date ddStartDate, Date ddEndDate, Double amount,
                                 Double additionalDiscount, boolean saveAndRollBack) throws Exception {
        Attachment additionalDiscountAttachment = additionalDiscount != null && additionalDiscount > 0 ? currentCPT.getAttachment("ADDITIONAL_DISCOUNT_ATTACHMENT") : null;
        Long additionalDiscountAttachmentId = additionalDiscountAttachment != null ? additionalDiscountAttachment.getId() : null;
        String additionalDiscountNotes = additionalDiscount != null && additionalDiscount > 0 ? currentCPT.getAdditionalDiscountNotes() : null;

        switchingNationalityAddNewDD(contract, ddStartDate, ddEndDate,
                additionalDiscount, additionalDiscountNotes, additionalDiscountAttachmentId,
                amount,
                null, DirectDebitType.MONTHLY, null, !saveAndRollBack, null, false, false, !saveAndRollBack, null, !saveAndRollBack);
    }

    public ContractPaymentTerm createNewTermForSwitchingNationalities(
            Contract contract, Housemaid newHousemaid, ContractPaymentTerm currentCPT,
            boolean keepCurrentDDs,
            DateTime replacementDate,
            SwitchingNationalityType switchingNationalityType,
            boolean saveAndRollback) {

        PaymentTermConfig newPaymentTermConfig = getPaymentTermConfigForNewCpt(
                newHousemaid, currentCPT, keepCurrentDDs,
                switchingNationalityType);

        ContractPaymentTerm newCPT = new ContractPaymentTerm();
        newCPT.setReason(ContractPaymentTermReason.REPLACEMENT);
        newCPT.setContract(contract);
        newCPT.setHousemaid(newHousemaid);
        newCPT.setPackageType(currentCPT.getPackageType());

        if(currentCPT.getContractPaymentTypes().stream().anyMatch(c -> !c.getType().getCode().equals("monthly_payment")) &&
                (switchingNationalityType.equals(SwitchingNationalityType.SAME_GRADE) || keepCurrentDDs)) {
            logger.info("keep same ptc");
            newCPT.setPaymentTermConfigWithValues(currentCPT.getPaymentTermConfig());
            newCPT.setDailyRateAmount(currentCPT.getDailyRateAmount());
        } else {
            newCPT.setPaymentTermConfigWithValues(newPaymentTermConfig);
        }

        newCPT.setNationality(newHousemaid.getNationality());

        // TODO: 6/25/2020 ask osamah, Comparison based on number of premium payments: whatever the number of premium payments
        // in the new term, always use the current one, it’s the term we agreed on with the client so we will keep it
        newCPT.setDiscountEffectiveAfter(newPaymentTermConfig.getDiscountEffectiveAfter());

        // ACC-2231
        boolean isSameNationality = currentCPT.getHousemaid().getNationality().getCode()
                .equals(newHousemaid.getNationality().getCode());
        if (!doesClientPayVat(contract) && hasToPayVat(contract.getId()) && !keepCurrentDDs && contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE) && isSameNationality) {
            double discountedValue = currentCPT.getMonthlyPayment() - currentCPT.getDiscount();
            Double workerSalary = contract.getContractProspectType().getCode()
                    .equalsIgnoreCase(AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE) ?
                    contract.getWorkerSalaryNew() : 0D;

            double vat = DiscountsWithVatHelper.getVatPercent();
            double discountedValueWithVat =  Math.ceil(DiscountsWithVatHelper.getAmountPlusVat(discountedValue, vat, workerSalary, contract.isWorkerSalaryVatted()));
            double fullValueWithVat = Math.ceil(DiscountsWithVatHelper.getAmountPlusVat(currentCPT.getMonthlyPayment(), vat));

            newCPT.setPaymentTermConfigWithValues(newPaymentTermConfig);
            newCPT.setDiscountEffectiveAfter(currentCPT.getDiscountEffectiveAfter());
            newCPT.setMonthlyPayment(fullValueWithVat);
            newCPT.setDiscount(fullValueWithVat - discountedValueWithVat);

            if (currentCPT.getPaymentTermConfig() == null)
                newCPT.setPaymentTermConfigWithValues(null);

        } else if (switchingNationalityType.equals(SwitchingNationalityType.SAME_GRADE) || keepCurrentDDs) {
            newCPT.setMonthlyPayment(newPaymentTermConfig.getMonthlyPayment());
            newCPT.setDiscount(newPaymentTermConfig.getDiscount());
            if (newCPT.getPaymentTermConfig() == null) {
                newCPT.setContractProspectType(currentCPT.getContractProspectType());
                newCPT.setType(currentCPT.getType());
                newCPT.setAgencyFee(currentCPT.getAgencyFee());
                newCPT.setIsRemote(currentCPT.getIsRemote());
            }

            // ACC-1615
            if (keepCurrentDDs) {
                newCPT.setFirstMonthPayment(currentCPT.getFirstMonthPayment());
            }

        }


        if (switchingNationalityType.equals(SwitchingNationalityType.SAME_GRADE)) {
            if (!doesClientPayVat(contract) && hasToPayVat(contract.getId()) && !keepCurrentDDs) {
                newCPT.setReason(ContractPaymentTermReason.REPLACEMENT_WITH_VAT);
            }
        } else {
            newCPT.setReason(ContractPaymentTermReason.SWITCHING);
            if (switchingNationalityType.equals(SwitchingNationalityType.UPGRADING)) {
                newCPT.setSwitchingToPhilipino(true);
            }
        }

        newCPT.setIsActive(true);
        if (!saveAndRollback) {
            currentCPT.getAttachments().forEach(att -> {
                if (!att.getTag().equals(FILE_TAG_PAYMENTS_RECEIPT))
                    newCPT.addAttachment(att);
            });
        }
        newCPT.setAccountName(currentCPT.getAccountName());
        newCPT.setBank(currentCPT.getBank());
        newCPT.setBankName(currentCPT.getBankName());
        newCPT.setEid(currentCPT.getEid());
        newCPT.setIsProRated(currentCPT.isIsProRated());
        newCPT.setAdditionalDiscount(currentCPT.getAdditionalDiscount());
        newCPT.setAdditionalDiscountMonths(currentCPT.getAdditionalDiscountMonths());
        newCPT.setAdditionalDiscountNotes(currentCPT.getAdditionalDiscountNotes());
        newCPT.setSpouseWillSignDD(currentCPT.getSpouseWillSignDD());
        newCPT.setIbanNumber(currentCPT.getIbanNumber());
        newCPT.setIsEidRejected(currentCPT.getIsEidRejected());
        newCPT.setEidRejectionReason(currentCPT.getEidRejectionReason());
        newCPT.setIsIBANRejected(currentCPT.getIsIBANRejected());
        newCPT.setIbanRejectionReason(currentCPT.getIbanRejectionReason());
        newCPT.setIsAccountHolderRejected(currentCPT.getIsAccountHolderRejected());
        newCPT.setAccountNameRejectionReason(currentCPT.getAccountNameRejectionReason());
        newCPT.setCreditNote(currentCPT.getCreditNote());
        newCPT.setCreditNoteMonths(currentCPT.getCreditNoteMonths());

        //-ACC-2694
        newCPT.setReceiptSent(currentCPT.getReceiptSent());

        if (contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE) && switchingNationalityType.equals(SwitchingNationalityType.SAME_GRADE)) {
            newCPT.setDiscountEffectiveAfter(currentCPT.getDiscountEffectiveAfter());
        }

        newCPT.setSwitchOrReplaceNationalityDate(replacementDate.toDate());
        return newCPT;
    }

    public SwitchingNationalityType getSwitchingNationalityType(Housemaid oldHousemaid, Housemaid newHousemaid) {

        return getSwitchingNationalityType(
                oldHousemaid.getNationality(), oldHousemaid.getLiveOut(),
                newHousemaid.getNationality(), newHousemaid.getLiveOut());
    }

    public SwitchingNationalityType getSwitchingNationalityType(PicklistItem oldNationality, boolean oldLiveOut, PicklistItem newNationality, boolean newLiveOut) {

        return (SwitchingNationalityType) getSwitchingNationalityTypeWithGroup(oldNationality, oldLiveOut, newNationality, newLiveOut).get("grade");
    }

    public Map<String, Object> getSwitchingNationalityTypeWithGroup(PicklistItem oldNationality, boolean oldLiveOut, PicklistItem newNationality, boolean newLiveOut) {

        logger.info("oldNationality: " + oldNationality.getId() + "newNationality: " + newNationality.getId() +
                "oldLiveOut: " + oldLiveOut + "newLiveOut: " + newLiveOut);

        if (oldNationality.getId().equals(newNationality.getId()) && oldLiveOut == newLiveOut) {

            return new HashMap<String, Object>() {{
                put("grade", SwitchingNationalityType.SAME_GRADE);
                // TODO Here we return Nationality name not Group
                put("oldNationalityGroup", oldNationality.getName());
                put("newNationalityGroup", newNationality.getName());
            }};
        }

        Map<String, Map<String, Object>> housemaids = getHousemaids(oldNationality.getId(), oldLiveOut, newNationality.getId(), newLiveOut,
                getHousemaidsNationalitiesDetailsFromSales(oldNationality.getId(), oldLiveOut, newNationality.getId(), newLiveOut));

        return new HashMap<String, Object>() {{
           put("grade", getSwitchingNationalityType(
                   Integer.parseInt(String.valueOf(housemaids.get("oldNationalityGrade").get("grade"))),
                   Integer.parseInt(String.valueOf(housemaids.get("newNationalityGrade").get("grade")))));
           put("oldNationalityGroup", housemaids.get("oldNationalityGrade").get("label"));
           put("newNationalityGroup", housemaids.get("newNationalityGrade").get("label"));
        }};
    }

    public SwitchingNationalityType getSwitchingNationalityType(int oldHousemaidGrade, int newHousemaidGrade) {

        SwitchingNationalityType switchingNationalityType = oldHousemaidGrade == newHousemaidGrade ?
                SwitchingNationalityType.SAME_GRADE :
                oldHousemaidGrade > newHousemaidGrade ?
                        SwitchingNationalityType.UPGRADING :
                        SwitchingNationalityType.DOWNGRADING;

        logger.log(Level.INFO,"switchingNationalityType: {0}", switchingNationalityType);
        return switchingNationalityType;
    }

    // ACC-7574
    public List<Map<String, Object>> getHousemaidsNationalitiesDetailsFromSales(
            Long oldNationalityId, boolean oldLiveOut, Long newNationalityId, boolean newLiveOut) {
        List<Map<String, Object>> body = new ArrayList<>();

        Map<String, Object> oldMaid = new HashMap<>();
        oldMaid.put("nationalityId", oldNationalityId);
        oldMaid.put("isLiveOut", oldLiveOut);
        body.add(oldMaid);

        Map<String, Object> newMaid = new HashMap<>();
        newMaid.put("nationalityId", newNationalityId);
        newMaid.put("isLiveOut", newLiveOut);
        body.add(newMaid);

        List<Map<String, Object>> response = (List<Map<String, Object>>) moduleConnector.postJson(
                "/sales/nationalitymatchingtype/getnationalitiesdetailsofhousemaids",
                body, List.class);
        logger.info("response: " + response);

//        String msg = "The passing parameters, oldNationalityId: " + oldNationalityId + ", oldLiveOut: " + oldLiveOut +
//                ", newNationalityId: " + newNationalityId + ", newLiveOut: " + newLiveOut;
//        validateIfNationalityFollowGroup(response,msg);

        return response;
    }

//    // ACC-7574
//    // Check if the Nationality does not follow any group => Throw exception When the grade comes empty from sales
//    public void validateIfNationalityFollowGroup(List<Map<String, Object>> response, String msg) {
//
//        StringBuilder exceptionMsg = new StringBuilder("These nationalities do not follow any group. ");
//        boolean throwException = false;
//        for (Map<String, Object> r : response) {
//            if (!r.containsKey("grade") || r.get("grade") == null || r.get("grade").toString().isEmpty()) {
//                exceptionMsg.append("nationality id: ").append(r.get("nationalityId"))
//                            .append(" and LiveOut: ").append(r.get("liveOutStatus"))
//                            .append(", ");
//                throwException = true;
//            }
//        }
//
//        if (throwException) {
//            throw new TechnicalException(exceptionMsg + msg);
//        }
//    }

    // ACC-7574
    public Map<String, Map<String, Object>> getHousemaids(
            Long oldNationalityId, boolean oldLiveOut, Long newNationalityId, boolean newLiveOut, List<Map<String, Object>> housemaidGrades) {

        Map<String, Map<String, Object>> result = new HashMap<>();
        for (Map<String, Object> housemaidGrade : housemaidGrades) {
            Long id = Long.parseLong(String.valueOf(housemaidGrade.get("nationalityId")));

            if (!result.containsKey("oldNationalityGrade") &&
                    oldNationalityId.equals(id) && housemaidGrade.get("liveOutStatus").equals(oldLiveOut)) {
                result.put("oldNationalityGrade", housemaidGrade);
            } else if (!result.containsKey("newNationalityGrade") &&
                    newNationalityId.equals(id) && housemaidGrade.get("liveOutStatus").equals(newLiveOut)) {
                result.put("newNationalityGrade", housemaidGrade);
            }
        }

        return result;
    }

    public boolean isSwitchingToSamePrice(
            Contract contract, Housemaid newHousemaid, DateTime replacementDate) {

        ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();
        ContractPaymentTerm newCPT = simulateNewCPTCreation(
                contract, currentCPT, newHousemaid.getNationality(), newHousemaid.getLiveOut(),
                true, null, replacementDate);

        Double currentCPTAmount = calculateDiscountsWithVatService.getCPTAmountAtTime(currentCPT, replacementDate.toDate());
        Double newCPTAmount = newCPT.getMonthlyPayment();

        logger.info("Current CPT Amount" + currentCPTAmount);
        logger.info("New CPT Amount" + newCPTAmount);

        return currentCPTAmount.equals(newCPTAmount);
    }

    public void createCancellationToDos(ContractPaymentTerm cpt, Date ddCancellationDate, boolean hidden, DirectDebitCancellationToDoReason reason,
                                        DateTime replacementDate) {
        logger.log(Level.SEVERE, "createCancellationToDos Started.");
        List<DirectDebit> cancelToDoDDs;

        List<DirectDebitStatus> ddStatuses = Arrays.asList(DirectDebitStatus.CONFIRMED, DirectDebitStatus.PENDING);

//        if (notToCancelDDs != null && !notToCancelDDs.isEmpty()) {
//            cancelToDoDDs = ddRepo.getByContractPaymentTermAndTypeInAndExpiryDateIsGreaterThanAndIdNotIn(cpt, Arrays.asList(DirectDebitType.MONTHLY, DirectDebitType.ONE_TIME), ddCancellationDate, notToCancelDDs)
//                    .stream().filter(dd -> ddStatuses.contains(dd.getMStatus()) || (dd.getType().equals(DirectDebitType.MONTHLY) && ddStatuses.contains(dd.getStatus())))
//                    .collect(Collectors.toList());
//        } else {
        cancelToDoDDs = ddRepo.getByContractPaymentTermAndTypeInAndExpiryDateIsGreaterThan(cpt, Arrays.asList(DirectDebitType.MONTHLY, DirectDebitType.ONE_TIME), ddCancellationDate)
                .stream().filter(dd -> ddStatuses.contains(dd.getMStatus()) || (dd.getType().equals(DirectDebitType.MONTHLY) && ddStatuses.contains(dd.getStatus())))
                .collect(Collectors.toList());
//        }

        logger.log(Level.SEVERE, "cancelToDoDDs Size: " + cancelToDoDDs.size());
        boolean after_SW_EOF = isAfter_SW_EOF(cpt.getContract().getId(), replacementDate);
        Date shouldCoverDate = after_SW_EOF ? replacementDate.plusMonths(1).withDayOfMonth(1).toDate() : replacementDate.withDayOfMonth(1).toDate();
        if (cancelToDoDDs != null) {
            for (DirectDebit directDebit : cancelToDoDDs) {
                logger.log(Level.SEVERE, "DD#" + directDebit.getId() + ", Hidden: " + hidden);
                boolean shouldToDoBeHidden = hidden && DDUtils.doesDDCoverDate(directDebit, shouldCoverDate);
                logger.log(Level.SEVERE, "shouldToDoBeHidden:" + shouldToDoBeHidden);
                directDebitCancellationService.cancelWholeDD(directDebit, shouldToDoBeHidden, reason);
            }
        }
    }

    public Double getCPTAmountAtTime(Date date, ContractPaymentTerm cpt, boolean swFromNONFToFIlipino) {
        return isDuringPremiumPeriod(date, cpt) ? 
                cpt.getMonthlyPayment() : 
                cpt.getDiscountedMonthlyPayment();
    }

    private boolean isDuringPremiumPeriod(Date date, ContractPaymentTerm cpt) {
        return date.before(getDiscountStartDateInMillis(cpt.getContract().getStartOfContract(), cpt.getContract().getIsProRated(), cpt));
    }

    private Date getDiscountStartDateInMillis(Date startDate, boolean isProRated, ContractPaymentTerm paymentTerm) {
        return new Date(calculateDiscountsWithVatService.getDiscountStartDateInMillis(startDate, isProRated, paymentTerm));
    }

    private Date getAdditionalDiscountEndDateInMillis(Date startDate, boolean isProRated, ContractPaymentTerm paymentTerm) {
        return calculateDiscountsWithVatService.getAdditionalDiscountEndDateInMillis(startDate, isProRated, paymentTerm);
    }

    public Integer getDefaultPaymentsDuration(Contract contract) {
        try {
            Map<String, Object> values = objectMapper.readValue(
                    Setup.getParameter(Setup.getModule("sales"), "contract_package_payments_duration"), Map.class);

            return (Integer) values.get(contract.getContractProspectType().getCode()
                    .equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) ? "NORMAL_LONG_TERM" : "MAID_VISA");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void processCancellationToDos_fromDDRejection(DirectDebit directDebit) {
        
        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "processCancellationToDos_fromDDRejection_" + new Date().getTime(),
                "switchingNationalityService",
                "accounting",
                "processCancellationToDos_fromDDRejection",
                "DirectDebit",
                directDebit.getId(),
                true,
                false,
                new Class<?>[]{Long.class},
                new Object[]{directDebit.getId()});
    }
    
    @Transactional
    public void processCancellationToDos_fromDDRejection(Long directDebitId) {
        
        DirectDebit directDebit = ddRepo.findOne(directDebitId);
        for (DirectDebitFile directDebitFile : directDebit.getDirectDebitFiles()) {
            directDebitFile.setIgnoreDDRejectionFlow(true);
            List<DirectDebitCancelationToDo> ddCToDos = ddCToDoRepo.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(directDebitFile);

            DirectDebitCancellationToDoReason cancellationReason = DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY;

            if (ddCToDos != null && !ddCToDos.isEmpty()) {
                DirectDebitCancelationToDo ddCToDo = ddCToDos.get(0);
                ddCToDo.setStopped(true);
                ddCToDo.setCompleted(true);

                cancellationReason = ddCToDo.getReason();
                ddCToDoRepo.save(ddCToDo);
            }

            if (directDebitFile.getDdStatus().equals(DirectDebitStatus.REJECTED)) {
                directDebitCancellationService.cancelDDf(directDebitFile, cancellationReason);
            }
        }
    }

    public void processCancellationToDos_fromReceivingPayment(DirectDebit directDebit) {

        for (DirectDebitFile directDebitFile : directDebit.getDirectDebitFiles()) {
            directDebitFile.setIgnoreDDRejectionFlow(true);

            if (directDebitFile.getDdStatus().equals(DirectDebitStatus.CONFIRMED) ||
                    (directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING) && directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT))) {
                List<DirectDebitCancelationToDo> ddCToDos = ddCToDoRepo.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(directDebitFile);
                if (ddCToDos != null && !ddCToDos.isEmpty()) {
                    DirectDebitCancelationToDo ddCToDo = ddCToDos.get(0);
                    ddCToDo.setHidden(false);
                    ddCToDoRepo.save(ddCToDo);
                } else {
                    directDebitCancellationService.cancelDDf(directDebitFile, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
                }
            } else {
                if (directDebitFile.getDdStatus().equals(DirectDebitStatus.REJECTED)) {
                    List<DirectDebitCancelationToDo> ddCToDos = ddCToDoRepo.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(directDebitFile);

                    DirectDebitCancellationToDoReason cancellationReason = DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY;

                    if (ddCToDos != null && !ddCToDos.isEmpty()) {
                        DirectDebitCancelationToDo ddCToDo = ddCToDos.get(0);
                        ddCToDo.setStopped(true);
                        ddCToDo.setCompleted(true);

                        cancellationReason = ddCToDo.getReason();

                        ddCToDoRepo.save(ddCToDo);
                    }

                    directDebitCancellationService.cancelDDf(directDebitFile, cancellationReason);
                } else {
                    directDebitCancellationService.cancelDDf(directDebitFile, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
                }
            }
        }
    }

    public boolean relatesToSwitching(Long directDebitId, boolean shouldBeUpgradingNationality) {
        if (directDebitId == null) return false;
        DirectDebit directDebit = ddRepo.findOne(directDebitId);
        if (directDebit == null) return false;
        ContractPaymentTerm cpt = directDebit.getContractPaymentTerm();
        if (cpt == null || cpt.isActive()) {
            logger.log(Level.SEVERE, "cpt is NULL or Active");
            return false;
        }

        ContractPaymentTerm nextCPT = getNextCPT(cpt);

        if (nextCPT == null) {
            logger.log(Level.SEVERE, "next CPT is null");
            return false;
        }

        return cpt.getSwitchedOrReplacedNationality() != null && cpt.getSwitchedOrReplacedNationality() &&
                (!shouldBeUpgradingNationality || (nextCPT.getSwitchingToPhilipino() != null && nextCPT.getSwitchingToPhilipino()));
    }

    public boolean doesDDCoverSwitchingPeriod(Long directDebitId) {
        if (directDebitId == null) return false;
        DirectDebit directDebit = ddRepo.findOne(directDebitId);
        if (directDebit == null) return false;
        ContractPaymentTerm cpt = directDebit.getContractPaymentTerm();
        if (cpt == null || cpt.isActive()) {
            logger.log(Level.SEVERE, "cpt is NULL or Active");
            return false;
        }
        ContractPaymentTerm nextCPT = contractPaymentTermRepository.findFirstByContractAndIdGreaterThanOrderById(cpt.getContract(), cpt.getId());

        if (nextCPT == null) {
            logger.log(Level.SEVERE, "next CPT is null");
            return false;
        }

        DateTime replacementDate = new DateTime(nextCPT.getSwitchOrReplaceNationalityDate());
        boolean after_SW_EOF = isAfter_SW_EOF(nextCPT.getContract().getId(), replacementDate);

        DateTime targetDate = replacementDate.withDayOfMonth(1);

        return DDUtils.doesDDCoverDate(directDebit, targetDate.toDate()) || (after_SW_EOF && DDUtils.doesDDCoverDate(directDebit, targetDate.plusMonths(1).toDate()));
    }

    public boolean doesDDCoverSwitchingBothMonths(Long directDebitId) {
        if (directDebitId == null) return false;
        DirectDebit directDebit = ddRepo.findOne(directDebitId);
        if (directDebit == null) return false;
        ContractPaymentTerm cpt = directDebit.getContractPaymentTerm();
        if (cpt == null || cpt.isActive()) {
            logger.log(Level.SEVERE, "cpt is NULL or Active");
            return false;
        }
        ContractPaymentTerm nextCPT = contractPaymentTermRepository.findFirstByContractAndIdGreaterThanOrderById(cpt.getContract(), cpt.getId());

        if (nextCPT == null) {
            logger.log(Level.SEVERE, "next CPT is null");
            return false;
        }

        DateTime replacementDate = new DateTime(nextCPT.getSwitchOrReplaceNationalityDate());
        boolean after_SW_EOF = isAfter_SW_EOF(nextCPT.getContract().getId(), replacementDate);

        DateTime targetDate = replacementDate.withDayOfMonth(1);

        return after_SW_EOF && DDUtils.doesDDCoverDate(directDebit, targetDate.toDate()) && DDUtils.doesDDCoverDate(directDebit, targetDate.plusMonths(1).toDate());
    }

    private List<DirectDebit> getDDsOfCPT(ContractPaymentTerm contractPaymentTerm) {
        return ddRepo.findByContractPaymentTerm(contractPaymentTerm);
    }

    private DirectDebit getRelatedDD(ContractPaymentTerm cpt, Date date, List<DirectDebitStatus> excludeStatuses) {
        ContractPayment contractPayment = getContractPaymentOfCPTByDate(cpt, date, excludeStatuses);
        if (contractPayment == null || contractPayment.getDirectDebit() == null) return null;

        return contractPayment.getDirectDebit();
    }

    private List<ContractPayment> getContractPaymentsOfCPT(ContractPaymentTerm contractPaymentTerm) {
        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");

        SelectQuery<ContractPayment> query = new SelectQuery(ContractPayment.class);
        query.filterBy("contractPaymentTerm", "=", contractPaymentTerm);
        query.filterBy("paymentType.id", "=", monthlyPayment.getId());

        return query.execute();
    }

    private ContractPayment getContractPaymentOfCPTByDate(
            ContractPaymentTerm contractPaymentTerm,
            Date date,
            List<DirectDebitStatus> excludeStatuses) {

        List<ContractPayment> contractPayments = getContractPaymentsOfCPT(contractPaymentTerm);
        if (contractPayments == null || contractPayments.isEmpty()) return null;

        DateTime targetedDate = new DateTime(date);

        for (ContractPayment cp : contractPayments) {
            if (ContractPaymentHelper.doesMonthlyContractPaymentCoverDate(
                    cp, targetedDate, excludeStatuses)) {
                return cp;
            }
        }

        return null;
    }

    public void handleUpgradingNationalityRequiredPayment(
            ContractPaymentTerm cpt,
            DateTime date,
            DirectDebit dd,
            boolean switchingMonthPayment,
            boolean saveAndRollBack) {

        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");

        if (dd == null) return;

        logger.info("DD Status: " + dd.getStatus());

        if (shouldCreateOneTimeDD(dd)) {
            DateTime replacementDate = new DateTime(getNextCPT(cpt).getSwitchOrReplaceNationalityDate());
            Double ddAmount = switchingMonthPayment &&
                    DDUtils.doesDDCoverDate(dd, replacementDate.plusMonths(1).toDate()) &&
                    dd.getCategory().equals(DirectDebitCategory.B) ? (dd.getAmount() * 2) : dd.getAmount();
            DirectDebit newDD;
            DirectDebitRejectionToDo oldRejectionToDo = dd.getDirectDebitRejectionToDo();
            boolean oldDdHasRejectionToDo = oldRejectionToDo != null && !BooleanUtils.toBoolean(oldRejectionToDo.isStopped()) && !BooleanUtils.toBoolean(oldRejectionToDo.isCompleted());

            try {
                newDD = switchingNationalityAddNewDD(cpt.getContract(), date.withDayOfMonth(1).toDate(),
                        date.withDayOfMonth(1).toDate(), null, null, null,
                        ddAmount, null, DirectDebitType.ONE_TIME,
                        monthlyPayment, !saveAndRollBack && !oldDdHasRejectionToDo, null, false, false, !saveAndRollBack, null, !saveAndRollBack);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Exception while creating ONE_TIME DD");
                logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
                throw new RuntimeException(e);
            }

            if(saveAndRollBack) return;

            if (oldDdHasRejectionToDo) {
                logger.info("Old DD Has Rejection Todo#" + oldRejectionToDo.getId());
                DirectDebitRejectionToDo newRejectionToDo = DDUtils.copyRejectionFlowToDDA(oldRejectionToDo);

                newDD.setDirectDebitRejectionToDo(newRejectionToDo);
                newDD.setMStatus(dd.getMStatus());
                newDD.setStatus(dd.getMStatus().equals(DirectDebitStatus.CONFIRMED) ?
                        DirectDebitStatus.PENDING : dd.getMStatus());
                newDD.setConfirmedBankInfo(dd.getConfirmedBankInfo());
                newDD.setNonCompletedInfo(dd.getNonCompletedInfo());
                newDD.setDataEntryNotes(dd.getDataEntryNotes());
                ddRepo.save(newDD);

                List<DirectDebitFile> directDebitFiles = ddfRepo.findByDirectDebitOrderByIdDesc(dd);
                int generatedDDFs = 0;

                DirectDebitService directDebitService = Setup.getApplicationContext().getBean(DirectDebitService.class);
                if (directDebitFiles != null) {
                    for (DirectDebitFile ddf : directDebitFiles) {
                        DirectDebitSignature signature = ddf.getDirectDebitSignature();

                        if (signature != null) {
                            DirectDebitFile newDDF = directDebitService.createDDFile(
                                    newDD, signature, !newDD.getNonCompletedInfo(),
                                    false, DirectDebitMethod.MANUAL, DirectDebitType.DAILY);

                            newDDF.setNeedAccountantReConfirmation(ddf.getNeedAccountantReConfirmation());
                            newDDF.setRejectCategory(ddf.getRejectCategory());
                            newDDF.setRejectionReason(ddf.getRejectionReason());

                            if (!directDebitService.createDirectDebitActivationAttachmentIfNotExist(newDDF)) ddfRepo.save(newDDF);

                            generatedDDFs++;
                            if (generatedDDFs == 3) break;
                        }
                    }
                }
            }

            dd = ddRepo.findOne(dd.getId());
                directDebitCancellationService.cancelWholeDD(dd, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
        } else {
            Payment monthlyBouncedPayment = getPaymentOfCPTByDate(cpt, date.toDate(), Arrays.asList(PaymentStatus.BOUNCED), PaymentMethod.DIRECT_DEBIT, monthlyPayment);

            if (monthlyBouncedPayment != null && !BooleanUtils.toBoolean(monthlyBouncedPayment.getReplaced())) {
                handleUpgradingNationalityRequiredBouncedPayment(dd, monthlyBouncedPayment, saveAndRollBack);
            }
        }
    }

    public void handleUpgradingNationalityRequiredBouncedPayment(
            DirectDebit directDebit,
            Payment payment,
            boolean saveAndRollBack) {

        logger.info("handleUpgradingNationalityRequiredBouncedPayment " +
                "; payment: ID" + (payment == null ? "NULL" : (payment.getId() + ", Status: " + payment.getStatus())) +
                "; directDebit: " + (directDebit == null ? "NULL" : directDebit.getId()));

        if (payment == null || !payment.getStatus().equals(PaymentStatus.BOUNCED) || 
                (payment.getReplaced() != null && payment.getReplaced()) || directDebit == null) {
            
            return;
        }

        //Is there an approved manual DD
        if (directDebit.getManualDdfFile() == null || !directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
            logger.log(Level.SEVERE, "no Approved Manual");
            //is sent to bank for approval?
            List<DirectDebitFile> pendingManualDDFs = directDebit.getDirectDebitFiles() != null ?
                    directDebit.getDirectDebitFiles().stream().filter(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                            ddf.getStatus().equals(DirectDebitFileStatus.SENT) && ddf.getDdStatus().equals(DirectDebitStatus.PENDING))
                            .collect(Collectors.toList()) : new ArrayList();
            boolean isSentToBankForApproval = !pendingManualDDFs.isEmpty();

            logger.info("isSentToBankForApproval: " + isSentToBankForApproval);

            if (isSentToBankForApproval) {
                for (DirectDebitFile directDebitFile : pendingManualDDFs) {
                    directDebitFile.setForBouncingPayment(true);
                    ddfRepo.save(directDebitFile);
                }
            } else {
                Date ddStartDate = new DateTime(payment.getDateOfPayment()).dayOfMonth().withMinimumValue().toDate();
                PicklistItem oneTimePaymentType = Setup.getItem("TypeOfPayment", "monthly_payment");
                ContractPaymentTerm cpt = directDebit.getContractPaymentTerm();

                try {
                    switchingNationalityAddNewDD(cpt.getContract(), ddStartDate, ddStartDate,
                            null, null, null,
                            payment.getAmountOfPayment(), null, DirectDebitType.ONE_TIME,
                            oneTimePaymentType, !saveAndRollBack, null, false, false, !saveAndRollBack, null, !saveAndRollBack);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Exception while creating ONE_TIME DD");
                    logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
                    throw new RuntimeException(e);
                }

                boolean isSwitchingNextMonthPayment = isSwitchingNextMonthPayment(payment.getId());

                if (isSwitchingNextMonthPayment || !DDUtils.doesDDCoverDate(directDebit, new DateTime(payment.getDateOfPayment()).plusMonths(1).toDate())) {
                    directDebitCancellationService.cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
                    showCancellationToDos(directDebit);
                }
            }
        }
    }

    public boolean isSwitchingNextMonthPayment(Long paymentId) {
        Payment payment = paymentRepository.findOne(paymentId);
        ContractPaymentTerm nextCPT = getNextCPT(payment.getDirectDebit().getContractPaymentTerm());

        if (nextCPT == null) return false;

        return PaymentHelper.doesPaymentCoverDate(payment, new DateTime(nextCPT.getSwitchOrReplaceNationalityDate()).plusMonths(1).toDate());
    }

    public boolean shouldCreateOneTimeDD(DirectDebit directDebit) {
        if (directDebit != null) {
            if (directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED) || directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED))
                return false;

            if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE) || directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                return true;

            if (DDUtils.isDDPendingNotSent(directDebit)) {
                return true;
            }
        }

        return false;
    }

    public void moveCptTypeA_DDsToNewCPT(ContractPaymentTerm oldCPT, ContractPaymentTerm newCPT) {
        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");

        List<DirectDebit> directDebits = ddRepo.findByContractPaymentTermAndCategory(oldCPT, DirectDebitCategory.A);
        if (directDebits == null) return;

        for (DirectDebit directDebit : directDebits) {
            if (directDebit.getPayments() != null && !directDebit.getPayments().isEmpty() &&
                    directDebit.getPayments().stream().allMatch(cp -> !cp.getPaymentType().getId().equals(monthlyPayment.getId()))) {
                
                directDebit.setContractPaymentTerm(newCPT);
                ddRepo.save(directDebit);
            }
        }
    }

    public void showCancellationToDos(DirectDebit directDebit) {
        if (directDebit.getDirectDebitFiles() == null) return;

        for (DirectDebitFile directDebitFile : directDebit.getDirectDebitFiles()) {
            List<DirectDebitCancelationToDo> ddCToDos = ddCToDoRepo.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(directDebitFile);
            if (ddCToDos != null && !ddCToDos.isEmpty()) {
                DirectDebitCancelationToDo ddCToDo = ddCToDos.get(0);
                ddCToDo.setHidden(false);
                ddCToDoRepo.save(ddCToDo);
            }
        }
    }

    public ContractPaymentTerm simulateNewCPTCreation(
            Contract contract, ContractPaymentTerm currentCPT,
            PicklistItem newNationality,
            boolean newLiveOut,
            boolean clientPaidVat,
            SwitchingNationalityType switchingNationalityType,
            DateTime replacementDate) {

        PaymentTermConfig newPaymentTermConfig = contractPaymentTermServiceNew.findSuitableConfig(
                newNationality, contract.getContractProspectType(),
                contractPaymentTermServiceNew.getContractPaymentTypeOfContract(contract, newLiveOut),
                currentCPT.getPackageType());

        ContractPaymentTerm newCPT = new ContractPaymentTerm();
        newCPT.setPaymentTermConfigWithValues(newPaymentTermConfig);
        newCPT.setContract(contract);
        newCPT.setSwitchingToPhilipino(switchingNationalityType != null && switchingNationalityType.equals(SwitchingNationalityType.UPGRADING));
        newCPT.setSwitchOrReplaceNationalityDate(replacementDate.toDate());
        newCPT.setSwitchedOrReplacedNationality(true);
        newCPT.setId(currentCPT.getId() + 1);
        newCPT.setDailyRateAmount(currentCPT.getDailyRateAmount());

        if (!clientPaidVat) {
            newCPT.setMonthlyPayment(DiscountsWithVatHelper.getAmountWithoutVat(newCPT.getMonthlyPayment()));
            newCPT.setDiscount(DiscountsWithVatHelper.getAmountWithoutVat(newCPT.getDiscount()));
        }

        return newCPT;
    }

    public List<DirectDebit> getSwitchMaidNewDDs(
            ContractPaymentTerm currentCPT,
            ContractPaymentTerm newCPT,
            DateTime replacementDate) {

        List<DirectDebit> directDebits = new ArrayList();

        Double oldPrice = calculateDiscountsWithVatService.getCPTAmountAtTime(currentCPT, replacementDate.toDate());
        Double newPrice = calculateDiscountsWithVatService.getCPTAmountAtTime(newCPT, replacementDate.toDate());
        logger.log(Level.SEVERE, "Start Simulating Generating DDS: oldPrice " +
                oldPrice + "; newPrice: " + newPrice);

        if (newPrice > oldPrice) {
            Integer replacementMonthDays = replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();
            Integer numOfDaysToEOF = this.getDaysToEoM(replacementDate);
            Integer ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
            logger.log(Level.SEVERE, "Start Simulating Generating DDS: replacementMonthDays " + replacementMonthDays);
            logger.log(Level.SEVERE, "Start Simulating Generating DDS: numOfDaysToEOF " + numOfDaysToEOF);
            
            DirectDebit oneTimeDD = new DirectDebit();
            oneTimeDD.setAmount(((newPrice - oldPrice) / replacementMonthDays) * numOfDaysToEOF);
            oneTimeDD.setCategory(DirectDebitCategory.A);
            oneTimeDD.setType(DirectDebitType.ONE_TIME);
            oneTimeDD.setStartDate(replacementDate.toDate());
            oneTimeDD.setExpiryDate(replacementDate.plusMonths(ontTimeDDMonthDuration).toDate());
            
            if (oneTimeDD.getAmount() >= 1.0) {
                directDebits.add(oneTimeDD);
            }
        }

        Map<String, Map> newPrices = newCPT.getPricesMap();
        logger.log(Level.SEVERE, "Start Simulating Generating DDS: replacementMonthDays " + newPrices);
        
        for (String key : newPrices.keySet()) {
            Map value = newPrices.get(key);
            DirectDebit monthlyTimeDD = new DirectDebit();
            monthlyTimeDD.setAmount((Double) value.get("price"));
            monthlyTimeDD.setCategory(DirectDebitCategory.B);
            monthlyTimeDD.setType(DirectDebitType.MONTHLY);
            monthlyTimeDD.setStartDate((Date) value.get("startDate"));
            monthlyTimeDD.setExpiryDate((Date) value.get("expiryDate"));

            directDebits.add(monthlyTimeDD);
        }

        return directDebits;
    }

    // to be called via BGT
    @Transactional
    public void switchMaidAsync(
            Long contractId, Long newHousemaidId,
            Long replacementId, Date replacementDate,
            Boolean keepCurrentDDs, Boolean fromCcApp, Boolean addVatDDs) throws Exception {

        ContractPaymentTerm newCPT = service.switchMaid(contractId, newHousemaidId, new DateTime(replacementDate), false, BooleanUtils.toBoolean(keepCurrentDDs),
                BooleanUtils.toBoolean(fromCcApp), BooleanUtils.toBoolean(addVatDDs));

        if (replacementId != null) {
            newCPT.setReplacement(Setup.getRepository(ReplacementRepository.class).findOne(replacementId));
            contractPaymentTermRepository.save(newCPT);
        }

        Contract contract = contractRep.findOne(contractId);
        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID, contract);
        if (a != null) {
            a.setIsDeleted(true);
            accountingEntityPropertyRepository.save(a);
        }
    }

    public boolean doesClientPayVat(Contract contract) {
        return contract.getClientPaidVat() != null && contract.getClientPaidVat();
    }

    public boolean hasToPayVat(Long contractId) {
        if (addVatDDsForContracts == null || !addVatDDsForContracts.containsKey(contractId)) return true;
        return addVatDDsForContracts.get(contractId);
    }

    public void removeEntryFromVatMap(Long contractId) {
        if (addVatDDsForContracts != null && addVatDDsForContracts.containsKey(contractId))
            addVatDDsForContracts.remove(contractId);
    }

    public void addToVatMap(Long contractId, boolean hasToPayVat) {
        if (addVatDDsForContracts == null) addVatDDsForContracts = new HashMap();

        addVatDDsForContracts.put(contractId, hasToPayVat);
    }

    public boolean isAfter_SW_EOF(Long contractId, DateTime replacementDate) {
        if (isFromCCAPP(contractId)) {
            return false;
        }

        return getDaysToEoM(replacementDate) <= Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.SW_EOF));
    }

    public int getDaysToEoM(DateTime fromDate) {
        return DDUtils.getDaysToEoM(fromDate);
    }

    public boolean isFromCCAPP(Long contractId) {
        if (fromCCAPP_MAP == null || !fromCCAPP_MAP.containsKey(contractId)) return false;
        return fromCCAPP_MAP.get(contractId);
    }

    public void removeEntryFromCCAPPMap(Long contractId) {
        if (fromCCAPP_MAP != null && fromCCAPP_MAP.containsKey(contractId))
            fromCCAPP_MAP.remove(contractId);
    }

    public void addToCCAPPMap(Long contractId, boolean fromCCAPP) {
        if (fromCCAPP_MAP == null) new HashMap();

        fromCCAPP_MAP.put(contractId, fromCCAPP);
    }

    public DirectDebit switchingNationalityAddNewDD(
            Contract contract, Date fromDate, Date toDate,
            Double additionalDiscount, String notes, Long attachmentId,
            Double amount, Double suggestedAmount,
            DirectDebitType directDebitType, PicklistItem oneTimePaymentType,
            boolean useOldSignatures, Payment replaceOfPayment, boolean finalAmount,
            boolean withIncomplete, boolean saveChanges,
            ContractPaymentTerm contractPaymentTerm, boolean checkConcurrentModification) throws Exception{

        DirectDebit newDD = contractPaymentTermServiceNew.addNewDD(contract, fromDate, toDate, additionalDiscount, notes,
                attachmentId, amount, suggestedAmount, directDebitType,
                oneTimePaymentType, useOldSignatures, replaceOfPayment,
                finalAmount, withIncomplete, saveChanges, contractPaymentTerm,
                checkConcurrentModification);

        logger.info("Switching nationality add new DD id " + newDD.getId());

        return newDD;
    }

    public boolean downgradeFlowCheckContractCancelledBeforeAddRefund(Contract contract, DateTime replacementDate, boolean paymentReceived){
        Boolean switchDateEqualCancelDate = contract.getDateOfTermination() != null &&
                replacementDate.getMonthOfYear() == new DateTime(contract.getDateOfTermination()).getMonthOfYear() &&
                replacementDate.getYear() == new DateTime(contract.getDateOfTermination()).getYear();

        logger.log(Level.SEVERE, "downgradeFlowCheckContractCancelledBeforeAddRefund switchDateEqualCancelDate "
             + switchDateEqualCancelDate);
        if (!switchDateEqualCancelDate) {
            List<Payment> payments = paymentRepository.findByContractAndTypeOfPayment_CodeAndStatus
                    (contract, "monthly_payment", PaymentStatus.RECEIVED);
            Boolean paymentOfCancellationMonthIsReceived = payments.stream()
                    .anyMatch(p-> new LocalDate(p.getDateOfPayment().getTime()).toString("yyyy-MM")
                    .equals(new LocalDate(contract.getDateOfTermination().getTime()).toString("yyyy-MM")));

            logger.log(Level.SEVERE, "downgradeFlowCheckContractCancelledBeforeAddRefund " +
                    "paymentOfCancellationMonthIsReceived " + paymentOfCancellationMonthIsReceived);
            
            if (paymentOfCancellationMonthIsReceived || paymentReceived) {
                Boolean alreadyRefundRequested = Setup.getRepository(ClientRefundTodoRepository.class)
                        .findFirstByContractAndPurpose(contract, "Partial Refunds for Cancellation") != null;

                logger.log(Level.SEVERE, "downgradeFlowCheckContractCancelledBeforeAddRefund " +
                        "alreadyRefundRequested "+ alreadyRefundRequested);
                return !alreadyRefundRequested;
            }
        }
        return false;
    }

    public void downgradeFlowAddClientRefund(ContractPaymentTerm currentCPT, ContractPaymentTerm newCPT, DateTime replacementDate, Contract contract){
        Double thisMonthCurrentAmount = getCPTAmountAtTime(replacementDate.toDate(), currentCPT, false);
        Double thisMonthNewAmount = getCPTAmountAtTime(replacementDate.toDate(), newCPT, false);
        Double amountToRefund = thisMonthCurrentAmount - thisMonthNewAmount;

        if (amountToRefund > 0) {
            logger.info("Add Client Refund, Amount: " + amountToRefund);
            ClientMessagingAndRefundService clientMessagingAndRefundService = Setup.getApplicationContext()
                    .getBean(ClientMessagingAndRefundService.class);
            clientMessagingAndRefundService.refundAfterReplacement(contract, amountToRefund,
                        AccountingModule.PAYMENT_REQUEST_PURPOSE_SWITCHING_TO_A_CHEAPER_NATIONALITY_REFUND,
                "Downgrading switching nationality flow");
        }
    }

    public void validateSwitchMaid(Contract contract, Housemaid newHousemaid) {
        if (contract == null || newHousemaid == null)
            throw new RuntimeException("invalid contract or housemaid");

        if (!contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)))
            throw new RuntimeException("Contract prospect type should be 'maids.cc'");

        Map contractPaymentTermInfo = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (contractPaymentTermInfo == null)
            throw new RuntimeException("There is no active terms for current contract");

        ContractPaymentTerm currentContractPaymentTerm = (ContractPaymentTerm) contractPaymentTermInfo.get("contractPaymentTerm");

        Housemaid currentHousemaid = currentContractPaymentTerm.getHousemaid();

        if (currentHousemaid == null)
            throw new RuntimeException("There is no housemaid in contract");

        if (newHousemaid.getNationality() == null || currentHousemaid.getNationality() == null ||
                newHousemaid.getNationality().getCode() == null || currentHousemaid.getNationality().getCode() == null)
            throw new RuntimeException("old or new maid hasn't nationality");

        DateTime replacementDate = DateTime.now();
        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType =
                getSwitchingNationalityType(currentHousemaid, newHousemaid);

        // ACC-1689
        //old contract, allow replacement if nationalities are in the same group, or they are the same nationalities
        if (!contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {
            if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE)) {
                return;
            } else {
                throw new RuntimeException("Invalid Replacement, Client should open a new Contract");
            }
        }

        PaymentTermConfig newPaymentTermConfig = contractPaymentTermServiceNew.findSuitableConfig(
                newHousemaid.getNationality(), contract.getContractProspectType(),
                contractPaymentTermServiceNew.getContractPaymentTypeOfContract(contract, newHousemaid.getLiveOut()),
                currentContractPaymentTerm.getPackageType());

        if (newPaymentTermConfig == null)
            throw new RuntimeException("There is no payment term config for new maid");

        if (contract.getContractType().equals(ContractType.SHORT_TERM)) {
            DateTime discountDate = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(contract.getStartOfContract(), contract.getIsProRated(),
                    newPaymentTermConfig));
            if (Months.monthsBetween(new DateTime().withDayOfMonth(1).withTimeAtStartOfDay(), discountDate.withDayOfMonth(1)).getMonths() <= 1)
                throw new RuntimeException("Short contract duration more than full monthly payments count of new payment term config");
        }
    }

    @Transactional
    public ContractPaymentTerm switchMaid(
            long contractId, long newHousemaidId,
            DateTime replacementDate, boolean saveAndRollback,
            Boolean keepCurrentDDs, boolean fromCCAPP, boolean addVatDDs) throws Exception {

        addToVatMap(contractId, addVatDDs);
        addToCCAPPMap(contractId, fromCCAPP);

        List<DirectDebit> toMoveDDs = new ArrayList();

        Contract contract = contractRep.findOne(contractId);
        Housemaid newHousemaid = housemaidRep.findOne(newHousemaidId);
        validateSwitchMaid(contract, newHousemaid);
        ContractPaymentTerm currentCPT = (ContractPaymentTerm) contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract)
                .get("contractPaymentTerm");

        Housemaid currentHousemaid = currentCPT.getHousemaid();

        //Fix payment term config null issue
        if (currentCPT.getPaymentTermConfig() == null) {
            currentCPT.setPaymentTermConfig(contractPaymentTermServiceNew.findSuitableConfig(
                    currentHousemaid.getNationality(), contract.getContractProspectType(),
                    contractPaymentTermServiceNew.getContractPaymentTypeOfContract(contract, newHousemaid.getLiveOut()),
                    currentCPT.getPackageType()));
        }

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType = getSwitchingNationalityType(
                currentHousemaid, newHousemaid);

        // add new term config to contract
        ContractPaymentTerm newCPT = createNewTermForSwitchingNationalities(contract, newHousemaid,
                currentCPT, keepCurrentDDs, replacementDate,
                switchingNationalityType,
                saveAndRollback);

        contractPaymentTermRepository.save(newCPT);
        newCPT = contractPaymentTermRepository.findOne(newCPT.getId());

        currentCPT.setIsActive(false);
        currentCPT.setSwitchedOrReplacedNationality(true);
        contractPaymentTermRepository.save(currentCPT);

        Date ddCancellationDate = null;
        boolean hidden = false;

        if (!contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {
            logger.log(Level.SEVERE, "SWITCHING MAID ABD");
            oldCCSwitchingNationalityFlow(contract, currentCPT, newCPT, replacementDate, keepCurrentDDs);
            logger.log(Level.SEVERE, "SWITCHING MAID ABD after oldCCSwitchingNationalityFlow");

            boolean after_SW_EOF = isAfter_SW_EOF(contractId, replacementDate);

            ddCancellationDate = after_SW_EOF && !doesClientPayVat(contract) && hasToPayVat(contractId)
                    ? replacementDate.plusMonths(1).dayOfMonth().withMinimumValue().toDate() : replacementDate.toDate();
            hidden = after_SW_EOF && !doesClientPayVat(contract) && hasToPayVat(contractId);

            logger.log(Level.SEVERE, "SWITCHING MAID ABD ddCancellationDate:" + ddCancellationDate);
            logger.log(Level.SEVERE, "SWITCHING MAID ABD hidden:" + hidden);

        } else {
            if (!keepCurrentDDs) {
                if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE)) {
                    // ACC-3417
                    keepCurrentDDs = true;
                } else {
                    indefiniteCCDifferentNationalityFlow(contract, currentCPT, newCPT, replacementDate,
                            switchingNationalityType, saveAndRollback);

                    // ACC-2933, cancellation is being Done inside service.executeDowngradeNationalityFlow
                    if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.UPGRADING)) {
                        // ACC-2800
                        moveCptTypeA_DDsToNewCPT(currentCPT, newCPT);

                        List<DirectDebit> remainingDDs = ddRepo.getByContractPaymentTermAndExpiryDateIsGreaterThan(currentCPT, replacementDate.toDate());

                        cancelDDsAfterUpgrading(replacementDate, currentCPT, remainingDDs);
                    }
                }
            }

            // move dds
            // ACC-1615
            if (keepCurrentDDs) {
                newCPT.setDdcId(currentCPT.getDdcId());  // ACC-7321
                contractPaymentTermRepository.save(newCPT);
                newCPT = contractPaymentTermRepository.findOne(newCPT.getId());
                toMoveDDs = ddRepo.findByContractPaymentTerm(currentCPT);
            }

            if (toMoveDDs != null && !toMoveDDs.isEmpty()) {
                List<ContractPayment> contractPayments = new ArrayList<>();
                ContractPaymentTerm finalNewCPT = newCPT;
                toMoveDDs.forEach(directDebit -> {
                    contractPaymentRep.findByDirectDebit(directDebit).forEach(contractPayment -> {
                        contractPayment.setContractPaymentTerm(finalNewCPT);
                        contractPayments.add(contractPayment);
                    });
                    directDebit.setContractPaymentTerm(finalNewCPT);
                });
                ddRepo.save(toMoveDDs);
                contractPaymentRep.save(contractPayments);
            }
        }

        // update contract if client not paid vat
        if (!saveAndRollback) {
            if (!doesClientPayVat(contract) && hasToPayVat(contractId)) {
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("id", contract.getId());
                parameters.put("clientPaidVat", true);
                moduleConnector.call(AccountingModule.COMPLAINTS_MODULE_CODE, "contractController",
                        "update", Object.class, new Class[]{Map.class}, parameters);
            }

            // this is being executed just in case of switching between different nationalities,same nationality case: cancellation process is happening inside the main service
            if (ddCancellationDate != null) {
                createCancellationToDos(currentCPT, ddCancellationDate, hidden,
                        DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY, replacementDate);
            }
        } else {
            Map result = new HashMap();
            if (isFromCCAPP(contractId)) {
                result.put("wholeDirectDebits", ddRepo.findByContractPaymentTerm(newCPT));
            } else {
                result.put("newDirectDebits", ddRepo.findByContractPaymentTermAndCreationDateGreaterThanEqual(newCPT
                        , replacementDate.minusSeconds(1).withMillisOfSecond(0).toDate()));
            }

            result.put("currentContractPaymentTerm", currentCPT);
            result.put("newContractPaymentTerm", newCPT);

            removeEntryFromVatMap(contractId);
            removeEntryFromCCAPPMap(contractId);
            throw new SaveAndRollbackException(result);
        }

        removeEntryFromVatMap(contractId);
        removeEntryFromCCAPPMap(contractId);
        return newCPT;
    }

    public PaymentTermConfig getPaymentTermConfigForNewCpt(
            Housemaid newHousemaid,
            ContractPaymentTerm currentCPT,
            boolean keepCurrentDDs,
            SwitchingNationalityType switchingNationalityType) {

        PaymentTermConfig newPaymentTermConfig;
        if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE) || keepCurrentDDs) {
            boolean isSameNationality = currentCPT.getHousemaid().getNationality().getCode()
                    .equals(newHousemaid.getNationality().getCode());
            newPaymentTermConfig = currentCPT.getNewTermConfig();

            if ((!doesClientPayVat(currentCPT.getContract()) && hasToPayVat(currentCPT.getContract().getId()) && !keepCurrentDDs) ||
                    !switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE)) {

                boolean isSameLiveStatus = currentCPT.getHousemaid().getLiveOut().equals(newHousemaid.getLiveOut());
                if (currentCPT.getContract().getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) { // new contract
                    if (!isSameNationality || !isSameLiveStatus) { // if not same nationality then take the new config
                        newPaymentTermConfig = findSuitableConfig(newHousemaid, currentCPT);

                    }
                } else { // old contract
                    newPaymentTermConfig = findSuitableConfig(newHousemaid, currentCPT);
                }
            }
        } else {
            newPaymentTermConfig = findSuitableConfig(newHousemaid, currentCPT);

            if (!doesClientPayVat(currentCPT.getContract()) && !hasToPayVat(currentCPT.getContract().getId()) && !keepCurrentDDs) {
                newPaymentTermConfig.setMonthlyPayment(DiscountsWithVatHelper.getAmountWithoutVat(newPaymentTermConfig.getMonthlyPayment()));
                newPaymentTermConfig.setDiscount(DiscountsWithVatHelper.getAmountWithoutVat(newPaymentTermConfig.getDiscount()));
            }
        }

        return newPaymentTermConfig;
    }

    public PaymentTermConfig findSuitableConfig(Housemaid newHousemaid, ContractPaymentTerm cpt) {
        return contractPaymentTermServiceNew.findSuitableConfig(
                newHousemaid.getNationality(), cpt.getContract().getContractProspectType(),
                contractPaymentTermServiceNew.getContractPaymentTypeOfContract(cpt.getContract(), newHousemaid.getLiveOut()),
                cpt.getPackageType());
    }

    // ACC-3368
    public Map getSwitchMaidInfoForCCAPP_V2(
            Contract contract,
            PicklistItem newNationality,
            boolean newLiveOut,
            String nationalityGroup) {

        Map response = new HashMap();
        boolean isSwitchingNationality;
        String message = "";

        ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();
        Housemaid oldHousemaid = currentCPT.getHousemaid();
        Housemaid currentHousemaid = contract.getHousemaid();

        Date upgradingNationalityDate = contract.getUpgradingNationalityDate();

        DateTime replacementDate;
        if (upgradingNationalityDate != null) {
            replacementDate = new DateTime(upgradingNationalityDate);
        } else {
            replacementDate = DateTime.now();
        }

        // ACC-7574
        Map<String, Object> map = getSwitchingNationalityTypeWithGroup(oldHousemaid.getNationality(), oldHousemaid.getLiveOut(),
                newNationality, newLiveOut);

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType = (SwitchingNationalityType) map.get("grade");

        String newNationalityName = nationalityGroup == null || nationalityGroup.isEmpty() ?
                newNationality.getName() :
                nationalityGroup;

        boolean isFirstReplacement = upgradingNationalityDate == null && contract.getDowngradingNationalityDate() == null;
        if (isFirstReplacement && switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE)) {
            isSwitchingNationality = false;

            if (!oldHousemaid.getNationality().getId().equals(newNationality.getId()) &&
                    !((String)map.get("oldNationalityGroup"))
                            .equalsIgnoreCase((String) map.get("newNationalityGroup"))) { // CMA-4208
                message = "You can replace your maid with " + (StringUtils.startsWithVowel(newNationalityName) ? "an " : "a ") + newNationalityName + " maid without any changes to your monthly payments";
            }
        } else {
            logger.log(Level.SEVERE, "Start Simulating Generating DDS");
            boolean isReplacingCurrentMaidWithSameGradeNewMaid = currentHousemaid != null && !currentHousemaid.getId().equals(oldHousemaid.getId()) &&
                    switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE);

            isSwitchingNationality = !switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE) &&
                    !isReplacingCurrentMaidWithSameGradeNewMaid;
            logger.log(Level.SEVERE, "Start Simulating Generating DDS: isSwitchingNationality " + isSwitchingNationality);

            if (isSwitchingNationality) {
                if (newNationalityName.equalsIgnoreCase("filipina"))
                    message = newNationalityName + " maids are only available for weekly contracts";

                LinkedHashMap newPaymentsBreakdowns = null;
                boolean clientPaidVat = contract.getClientPaidVat() != null && contract.getClientPaidVat();
                ContractPaymentTerm newCPT = simulateNewCPTCreation(contract, currentCPT, newNationality, newLiveOut,
                        clientPaidVat, switchingNationalityType, replacementDate);
                logger.log(Level.SEVERE, "Start Simulating Generating DDS: newCPT monthly amount: " + newCPT.getMonthlyPayment());
                logger.log(Level.SEVERE, "Start Simulating Generating DDS: newCPT Discounted monthly payment: " + newCPT.getDiscountedMonthlyPayment());

                List<DirectDebit> tempDDs = getSwitchMaidNewDDs(currentCPT, newCPT, replacementDate);
                logger.log(Level.SEVERE, "Start Simulating Generating DDS: tempDDs " + tempDDs.size());
                logger.log(Level.SEVERE, "Start Simulating Generating DDS: clientPaidVat " + clientPaidVat);

                Boolean isWeekly = newCPT.getPaymentTermConfig() != null && newCPT.getPaymentTermConfig().getWeeklyAmount() != null && newCPT.getPaymentTermConfig().getWeeklyAmount() > 0.0;
                newPaymentsBreakdowns = DDUtils.getPaymentsBreakdowns(
                        newCPT.getPaymentTermConfig(), isWeekly, newLiveOut, tempDDs, contract);
                if(isWeekly) {
                    double totalWeeklyAmount = Double.parseDouble(newPaymentsBreakdowns.get("totalAmount").toString()) / 4;
                    message = DDUtils.getDirectDebitsDescriptionForCCAPP_SwitchMaidForWeekly(totalWeeklyAmount, oldHousemaid.getNationality().getName(), newNationalityName);
                } else {
                    message = DDUtils.getDirectDebitsDescriptionForCCAPP_SwitchMaid(newCPT, currentCPT, tempDDs, newNationalityName);
                    logger.log(Level.SEVERE, "Start Simulating Generating DDS: isSwitchingNationality " + isSwitchingNationality);
                }
                response.put("paymentsBreakdowns", newPaymentsBreakdowns);
            }
        }

        response.put("switchingNationality", isSwitchingNationality);
        response.put("canHireMaid", true);
        response.put("message", message);
        return response;
    }

    public ContractPaymentTerm switchNationalityChangeToNewCpt(
            ContractPaymentTerm currentCPT, Housemaid newHousemaid, boolean keepCurrentDDs,
            DateTime replacementDate, SwitchingNationalityService.SwitchingNationalityType switchingNationalityType,
            boolean saveAndRollback) {

        // add new term config to contract
        ContractPaymentTerm newCPT = createNewTermForSwitchingNationalities(currentCPT.getContract(), newHousemaid,
                currentCPT, keepCurrentDDs, replacementDate, switchingNationalityType,
                saveAndRollback);

        contractPaymentTermRepository.save(newCPT);
        newCPT = contractPaymentTermRepository.findOne(newCPT.getId());

        currentCPT.setIsActive(false);
        currentCPT.setSwitchedOrReplacedNationality(true);
        return newCPT;
    }

    // Client paying via credit card send success message
    public void sendReplacementSuccessMessage(ContractPaymentTerm cpt, Housemaid newHousemaid) {
        logger.info("cpt id: " + cpt.getId());
        String templateName = cpt.getContract().isMaidCc() ?
                CcNotificationTemplateCode.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_NOTIFICATION.toString() :
                MvNotificationTemplateCode.MV_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_NOTIFICATION.toString();

        DateTime lastPayment = paymentService.getLastReceivedMonthlyPaymentDate(cpt.getContract());

        Map<String, String> p = new HashMap<>();
        Map<String, AppAction> c = new HashMap<>();

        if (cpt.getContract().isPayingViaCreditCard() ||
                flowProcessorService.existsRunningFlow(cpt.getContract(), FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED)) {
            clientPayingViaCreditCardService.setReplacementSuccessMessageParameters(
                    cpt, newHousemaid, lastPayment, p, c);
        } else if (cpt.getContract().isOneMonthAgreement()) {
            oneMonthAgreementFlowService.setReplacementSuccessMessageParameters(
                    cpt, newHousemaid, lastPayment, p, c);
        }

        logger.info("parameters: " + p.entrySet());

        if (p.containsKey("todoUuid")) {
            templateName = cpt.getContract().isMaidCc() ?
                    CcNotificationTemplateCode.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_WITH_PAY_LINK_NOTIFICATION.toString() :
                    MvNotificationTemplateCode.MV_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_WITH_PAY_LINK_NOTIFICATION.toString();
        }

        Template t = TemplateUtil.getTemplate(templateName);
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendMessageToClient(cpt.getContract(),
                        p, c,
                        p.containsKey("todoUuid") ? Long.valueOf(p.get("todoId")) : cpt.getContract().getId(),
                        p.containsKey("todoUuid") ? "ContractPaymentConfirmationToDo" : cpt.getContract().getEntityType(),
                        t);
    }

    public void sendRefundEmailUponDowngradingNationality(ContractPaymentTerm cpt, Housemaid newHousemaid, Double amount) {

        Map<String, String> parameters = new HashMap<>();
        parameters.put("previous_nationality", StringUtils.getHousemaidNationality(cpt.getHousemaid().getNationality().getName()));
        parameters.put("new_nationality", StringUtils.getHousemaidNationality(newHousemaid.getNationality().getName()));
        parameters.put("client_name", cpt.getContract().getClient().getName());
        parameters.put("client_id", String.valueOf(cpt.getContract().getClient().getId()));
        parameters.put("contract_id", String.valueOf(cpt.getContract().getId()));
        parameters.put("refund_amount", String.valueOf(amount.intValue()));


        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "client_paying_via_credit_card_downgrade_email",
                        parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_DOWNGRADE_RECIPIENTS_EMAIL),
                        "Client switched nationality - Credit Card refund is needed");
    }

    public String getPayingViaCreditCardPaymentsInfo(Map<String, Object> firstPayment, Map<String, Map<String, Object>> groupByAmount) {

        StringBuilder paymentsInfo = new StringBuilder();

        if (!firstPayment.isEmpty()) {
             paymentsInfo.append("For ").append(DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                     .parseLocalDate((String) firstPayment.get("paymentDate")).toString("MMM"))
                     .append(" AED ")
                     .append(((Double) firstPayment.get("amount")).intValue());
        }


        groupByAmount.forEach((d, map) -> {
            paymentsInfo.append(paymentsInfo.toString().isEmpty() ? "" : " + ");

            switch ((Integer) map.get("count")) {
                case 1:
                    paymentsInfo.append("For ")
                            .append(map.get("firstMonth"))
                            .append(" AED ")
                            .append(((Double) map.get("amount")).intValue());
                    break;
                case 2:
                    paymentsInfo.append("For ")
                            .append(map.get("firstMonth"))
                            .append(" and ")
                            .append((map.get("lastMonth")))
                            .append(" AED 2* ")
                            .append(((Double) map.get("amount")).intValue());
                    break;
                default:
                    paymentsInfo.append("For ")
                            .append(map.get("firstMonth"))
                            .append(" till ")
                            .append(map.get("lastMonth"))
                            .append(" AED ")
                            .append(map.get("count"))
                            .append("* ")
                            .append(((Double) map.get("amount")).intValue());
                    break;
            }
        });

        return paymentsInfo.toString();
    }

    public String getPayingViaCreditCardPaymentsInfo(List<Map<String, Object>> l) {

        StringBuilder paymentsInfo = new StringBuilder();
        for (Map<String, Object> p : l) {
            paymentsInfo.append(paymentsInfo.toString().isEmpty() ? "" : " + ");
            paymentsInfo.append("For ").append(DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                            .parseLocalDate((String) p.get("paymentDate")).toString("MMM"))
                    .append(" AED ")
                    .append(((Double) p.get("amount")).intValue());
        }
        return paymentsInfo.toString();
    }

    public ContractPaymentTerm createNewCptAfterSwitchingAndMoveOldFlow(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            DateTime replacementDate, SwitchingNationalityService.SwitchingNationalityType type, Replacement replacement) {

        logger.info("old cpt id: " + cpt.getId());
        ContractPaymentTerm newCpt = createNewTermForSwitchingNationalities(
                cpt.getContract(), newHousemaid, cpt, false, replacementDate,
                type,false);

        cpt.setIsActive(false);
        cpt.setSwitchedOrReplacedNationality(true);
        newCpt.setReplacement(replacement);
        contractPaymentTermRepository.save(cpt);
        contractPaymentTermRepository.save(newCpt);

        // ACC-7253
        sendSwitchingNationalitiesMoreThanOnceForClientPayingViaCcEmail(cpt);

        flowProcessorEntityRepository.findByContractPaymentTermAndStoppedFalseAndCompletedFalse(cpt)
                .forEach(f -> {
                    f.setContractPaymentTerm(newCpt);
                    if (f.getContractPaymentConfirmationToDo() != null) {
                        ContractPaymentConfirmationToDo todo = f.getContractPaymentConfirmationToDo();
                        todo.setContractPaymentTerm(newCpt);
                        contractPaymentConfirmationToDoRepository.save(todo);
                    }
                    flowProcessorEntityRepository.save(f);
                });

        return newCpt;
    }

    public void sendSwitchingNationalitiesMoreThanOnceForClientPayingViaCcEmail(ContractPaymentTerm cpt) {
        if (!flowProcessorService.isPayingViaCreditCard(cpt.getContract())) return;

        Date currentMonth = cpt.getContract().isOneMonthAgreement() ?
                Setup.getApplicationContext().getBean(OneMonthAgreementFlowService.class)
                        .getCurrentPaymentDate(cpt.getContract()).toDate() :
                new LocalDate().dayOfMonth().withMinimumValue().toDate();

        if (!contractPaymentTermRepository
                .existsSwitchingNationalitiesPayingViaCcPerCurrentMonth(cpt.getContract().getId(), currentMonth)) return;

        logger.info("old cpt id: " + cpt.getId());

        Map<String, String> parameters = new HashMap<>();
        parameters.put("client_name", cpt.getContract().getClient().getName());
        parameters.put("client_id", String.valueOf(cpt.getContract().getClient().getId()));
        parameters.put("contract_id", String.valueOf(cpt.getContract().getId()));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "client_paying_via_credit_card_switched_nationalities_several_times",
                        parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_PAYING_VIA_CC_SWITCH_NATIONALITY_RECIPIENTS_EMAIL),
                        "Client paying via credit card switched nationalities several times");
    }
}