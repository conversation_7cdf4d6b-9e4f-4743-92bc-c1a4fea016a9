package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.ResourceRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.core.type.FunctionType;
import com.magnamedia.core.type.NavigationType;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.PaymentSection.*;
import com.magnamedia.entity.workflow.*;
import com.magnamedia.extra.*;
import com.magnamedia.extra.Gender;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.newversion.services.graphicdesigner.GraphicToDoType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

//CMA-2728
@Service
public class CCAppContentService {

    protected static final Logger logger = Logger.getLogger(CCAppContentService.class.getName());

    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private LocalPushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private LocalSmsRepository localSmsRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private CCAppService ccAppService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private Utils utils;
    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;

    public String approvedMonthlyPaymentGetRefundAmount(Contract contract) {
        ClientRefundToDo clientRefundToDo = getCurrentRefundForPaymentSection(contract);

        return clientRefundToDo == null ? "" : PaymentHelper.df.format((clientRefundToDo.getAmount().intValue()));
    }

    private ClientRefundToDo getCurrentRefundForPaymentSection(Contract c) {
        ClientRefundToDo clientRefundToDo = clientRefundTodoRepository
                .findFirstByContractAndStatus(c, ClientRefundStatus.PENDING);
        if (clientRefundToDo != null) return clientRefundToDo;

        return clientRefundTodoRepository.findFirstByContractAndStatusAndCreationDateGreaterThanEqualOrderByCreationDateAsc(
                    c, ClientRefundStatus.PAID, new DateTime().minusDays(3).toDate());
    }

    @Deprecated
    public String getViewProofTransferLink(Contract contract) {
        ClientRefundToDo clientRefundToDo = clientRefundTodoRepository
                .findFirstByContractAndStatus(contract, ClientRefundStatus.PENDING);
        if (clientRefundToDo == null)
            return null;

        logger.log(Level.INFO, "clientRefundToDo id: {0}", clientRefundToDo.getId());
        PayrollAccountantTodo payrollAccountantTodo = clientRefundToDo.getPayrollAccountantTodos().stream()
                .filter(o -> o.getAttachment("client_refund_transfer_slip") != null)
                .findFirst().orElse(null);

        if (payrollAccountantTodo != null) {
            logger.log(Level.INFO, "payrollAccountantTodo id: {0}", payrollAccountantTodo.getId());

            return "<br/><a href=\"" + Setup.getApplicationContext().getBean(Shortener.class)
                    .shorten(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BACKEND_BASE_URL)
                            + "/public/download/" + payrollAccountantTodo.getAttachment("client_refund_transfer_slip").getUuid())
                    + "\"><span>Click here to view the proof of transfer</span></a>.";
        }
        return "";
    }

    public String payWithCreditCardEarlyCcPaymentAmount(Map<String, Object> body) {
        if (body.containsKey("amount") && !body.get("amount").equals("@amount@")) {
            return (String) body.get("amount");
        }
        Contract contract = (Contract) body.get("contract");
        String todoUuid = (String) body.get("todoUuid");
        logger.log(Level.INFO, "Contract id: {0}, todoUuid: {1}", new Object[]{contract.getId(), todoUuid});

        ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository
                .findByUuid(todoUuid);
        if (toDo == null) return "";

        return PaymentHelper.df.format(toDo.getTotalAmount().intValue());
    }

    @Deprecated
    public String payWithCreditCardEarlyCcNote(Contract contract) {

        logger.log(Level.INFO, "Contract id: {0}", contract.getId());
        List<DirectDebitStatus> allowedStatuses = Arrays.asList(
                DirectDebitStatus.CONFIRMED,
                DirectDebitStatus.PENDING,
                DirectDebitStatus.PENDING_DATA_ENTRY);
        // ACC-5992 Updates on Payment With Credit Card Screen
        if (Setup.getRepository(DirectDebitRepository.class)
                .existsByContractAndCategoryBAndStatusIn(contract, allowedStatuses)) {

            return "<b>Note:</b> Even if you pay now via card, our system will still try to deduct the amount via " +
                    "your monthly bank payment form on the first of the month. If we manage to deduct the amount, " +
                    "we’ll refund it automatically";
        }

        return "";
    }

    @Deprecated
    public String approvedDdsMonthlyPaymentIntro(Contract contract) {
        logger.log(Level.INFO, "Contract id" + contract.getId());

        Template template = TemplateUtil.getTemplate(CcAppCmsTemplate.CC_PAYMENT_APPROVED_DDS_MONTHLY_PAYMENT_INTRO.toString());
        if (template != null) {
            List<Map<String, Object>> payments = (List<Map<String, Object>>) paymentRepository
                    .findUpcomingMonthlyPaymentByContract(contract, PageRequest.of(0,1));
            if (!payments.isEmpty()) {
                Map<String, String> paramValues = getNextPaymentInfo(contract);
                if (paramValues.isEmpty()) return "";

                return TemplateUtil.compileTemplate(template, null, paramValues);
            }
        }
        return "";
    }

    @Deprecated
    public String resolveContent(Map<String, Object> body) {

        logger.log(Level.INFO, "resolveContent body is {0}", body);
        if (!body.containsKey("base_template")) {
            throw new RuntimeException("The body should contain field 'base_template'");
        }

        if (!body.containsKey("contract")) {
            throw new RuntimeException("The body should contain object 'contract'");
        }

        String templateStr = body.get("base_template").toString();
        CcAppCmsTemplate template = CcAppCmsTemplate.valueOf(templateStr);
        if (template == null) {
            throw new RuntimeException("Template '" + templateStr + "' is not supported");
        }

        Contract contract = (Contract) body.get("contract");

        Map<String, Object> result = resolveContent(contract, template);

        return result.containsKey("text") ? result.get("text").toString() : "";
    }

    private Map<String, Object> resolveContent(Contract contract, CcAppCmsTemplate template) {

        try {
            Map<String, Object> result = null;

            if (contract != null) {
                logger.log(Level.INFO, "resolveContent contract Id is {0}", contract.getId());
                result = resolveTemplateContent(template, contract);
            }

            if (result != null) {
                if(result.containsKey("text")) {
                    return result;
                } else {
                    throw new RuntimeException("Content not resolved");
                }
            }
            return new HashMap<>();
        } catch (Exception ex) {
            logger.log(Level.SEVERE, ex.toString());
            throw new RuntimeException(ex);
        }
    }

    @Deprecated
    public Map<String, Object> resolveTemplateContent(
            CcAppCmsTemplate template,
            Contract contract) {

        Map<String, Object> output = new HashMap<>();
        output.put("template", template);

        switch (template) {
            case CC_PAYMENT_PAID_BY_CARD_AND_NOT_SUBMIT_DD:
            case CC_PAYMENT_INCOMPLETE_DDS:
            case CC_PAYMENT_BOUNCED_DDS:
            case CC_PAYMENT_REJECTED_DDS:
            case CC_PAYMENT_BOUNCED_DDS_PAID:
                output.putAll(resolveDdsGetLastNotification(contract, template, true));
                break;
        }

        return output;
    }

    public CcPaymentSectionButtonVisibility resolveButtonVisibility(
            CcAppCmsTemplate template,
            CcPaymentSectionButton button,
            Contract contract,
            Map<String, Object> map) {

        String code = null;
        boolean contractCanceled = Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                .contains(contract.getStatus());

        if (contractCanceled) return CcPaymentSectionButtonVisibility.DISABLED;

        switch(button) {
            case PAY_USING_ALTERNATIVE_METHODS:
                Payment p = (Payment) map.get("bouncedPayment");
                Date dateOfBouncing = p.getBouncedPaymentLogs() == null || p.getBouncedPaymentLogs().isEmpty() ?
                        p.getDateOfBouncing() :
                        p.getBouncedPaymentLogs().get(p.getBouncedPaymentLogs().size() - 1).getCreationDate();

                boolean existsPaymentReceived = contractPaymentConfirmationToDoRepository
                        .existsByReplacedBouncedPaymentIdAndShowOnErpTrueAndCreationDate(p.getId(), dateOfBouncing);
                logger.info("payment id: " + p.getId() +
                        "; existsPaymentReceived: " + existsPaymentReceived +
                        "; dateOfBouncing: " + new LocalDate(dateOfBouncing).toString("yyyy-MM-dd"));

                if (existsPaymentReceived) return CcPaymentSectionButtonVisibility.DISABLED;


                if (Setup.getRepository(ContractPaymentRepository.class)
                        .existsConfirmedDdaCoveredPaymentAddFromErp(p, DirectDebitStatus.CONFIRMED)) {
                    logger.info("exits confirmed DDA cover the payment -> exiting");
                    return CcPaymentSectionButtonVisibility.DISABLED;
                }

                return clientProvidedNewDetailsAfterDate(contract, dateOfBouncing) ?
                        CcPaymentSectionButtonVisibility.DISABLED :
                        CcPaymentSectionButtonVisibility.ENABLED;

            case PAY_BY_CARD:

                switch (template) {
                    case PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE:
                        Date d = clientPayingViaCreditCardService.getChangeToPayingViaCcDate(contract);

                        if (Setup.getRepository(GraphicDesignerTodoRepository.class)
                                .existsByContractIdAndToDoTypeAndCreationDateGreaterThan(
                                        contract.getId(), d == null ? new Date() : d)) {
                            logger.info("exits graphic designer created after flow start date -> exiting");
                            return CcPaymentSectionButtonVisibility.DISABLED;
                        }

                       return Setup.getRepository(DirectDebitRepository.class)
                                .existsByContractPaymentTermAndActive(contract.getActiveContractPaymentTerm(),
                                        d == null ? new Date() : d,
                                        Arrays.asList(
                                                DirectDebitStatus.CANCELED,
                                                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                                                DirectDebitStatus.EXPIRED,
                                                DirectDebitStatus.REJECTED)) ?
                               CcPaymentSectionButtonVisibility.DISABLED :
                               CcPaymentSectionButtonVisibility.ENABLED;

                }
                if(!flowProcessorService.existsRunningFlow(
                        contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED))
                    return CcPaymentSectionButtonVisibility.HIDDEN;

                switch (template) {
                    case CC_PAYMENT_PAID_BY_CARD_AND_NOT_SUBMIT_DD:
                    case CC_PAYMENT_INCOMPLETE_DDS:
                        code = resolveDdsGetLastNotification(contract, template, false).get("code");
                        break;
                    default:
                        break;
                }

                logger.info("IPAM flow running");
                if (code == null) return CcPaymentSectionButtonVisibility.HIDDEN;
                Template t = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(code);
                if(t == null) return CcPaymentSectionButtonVisibility.HIDDEN;
                logger.info("t id: " + t.getId());

                DDMessaging m = Setup.getRepository(DDMessagingRepository.class).findByClientTemplate(t);
                if(m == null) return CcPaymentSectionButtonVisibility.HIDDEN;
                logger.info("m id: " + m.getId());

                return m.getSendPayTabMessage() ?
                        CcPaymentSectionButtonVisibility.ENABLED :
                        CcPaymentSectionButtonVisibility.HIDDEN;

            case CHANGE_BANK_DETAILS:

                switch (template) {
                    case CC_PAYMENT_REJECTED_DDS:
                        DirectDebit d = (DirectDebit) map.getOrDefault("rejectedDd", null);
                        if (d == null) return CcPaymentSectionButtonVisibility.DISABLED;
                        Date lastTrialDate = getRejectionFlowLastTrialDate(d.getDirectDebitRejectionToDo() != null ?
                                d.getDirectDebitRejectionToDo() : d.getDirectDebitBouncingRejectionToDo());

                        return flowProcessorService.clientProvidesSignatureAndBankInfo(
                                contract, lastTrialDate) ?
                                CcPaymentSectionButtonVisibility.DISABLED :
                                CcPaymentSectionButtonVisibility.ENABLED;
                    case CC_PAYMENT_BOUNCED_DDS:
                        p = (Payment) map.get("bouncedPayment");
                        dateOfBouncing = p.getBouncedPaymentLogs() == null || p.getBouncedPaymentLogs().isEmpty() ?
                                p.getDateOfBouncing() :
                                p.getBouncedPaymentLogs().get(p.getBouncedPaymentLogs().size() - 1).getCreationDate();

                        existsPaymentReceived = contractPaymentConfirmationToDoRepository
                                .existsByReplacedBouncedPaymentIdAndShowOnErpTrueAndCreationDate(p.getId(), dateOfBouncing);
                        logger.info("payment id: " + p.getId() +
                                "; existsPaymentReceived: " + existsPaymentReceived +
                                "; dateOfBouncing: " + new LocalDate(dateOfBouncing).toString("yyyy-MM-dd"));

                        if (existsPaymentReceived) return CcPaymentSectionButtonVisibility.DISABLED;


                        if (Setup.getRepository(ContractPaymentRepository.class)
                                .existsConfirmedDdaCoveredPaymentAddFromErp(p, DirectDebitStatus.CONFIRMED)) {
                            logger.info("exits confirmed DDA cover the payment -> exiting");
                            return CcPaymentSectionButtonVisibility.DISABLED;
                        }

                        return clientProvidedNewDetailsAfterDate(contract, dateOfBouncing) ?
                                CcPaymentSectionButtonVisibility.DISABLED :
                                CcPaymentSectionButtonVisibility.ENABLED;

                    case PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE:
                    case PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_HAS_CONFIRMED_DDS_DEFAULT_MESSAGE:

                       if (Setup.getRepository(GraphicDesignerTodoRepository.class)
                                .existsByContractIdAndToDoTypeAndCompletedFalse(
                                        contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE)) {
                            logger.info("exits graphic designer -> exiting");
                            return CcPaymentSectionButtonVisibility.DISABLED;
                        }

                        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                        if (cpt.getReason() == null ||
                                !cpt.getReason().equals(ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT))
                            return CcPaymentSectionButtonVisibility.ENABLED;

                        boolean existsPendingDd = Setup.getRepository(DirectDebitRepository.class)
                                .existsByContractPaymentTermAndStatusesAndCreationDate(
                                        cpt,
                                        cpt.getCreationDate(),
                                        new LocalDate(cpt.getCreationDate()).plusDays(1).toDate(),
                                        Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY));

                        return existsPendingDd ?
                                CcPaymentSectionButtonVisibility.DISABLED :
                                CcPaymentSectionButtonVisibility.ENABLED;
                }

                return preventSwitchingBankAccount(contract) ?
                        CcPaymentSectionButtonVisibility.HIDDEN :
                        CcPaymentSectionButtonVisibility.ENABLED;
            case SIGN_NOW:

                switch (template) {
                    case CC_PAYMENT_REJECTED_DDS:
                        DirectDebit d = (DirectDebit) map.getOrDefault("rejectedDd", null);
                        if (d == null) return CcPaymentSectionButtonVisibility.DISABLED;
                        if (!Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                                .existsIncompleteDDRelatedToRejectionFlow(contract.getId())) {
                            logger.info("There are no incomplete todos -> exiting");
                            return CcPaymentSectionButtonVisibility.DISABLED;
                        }

                        Date lastTrialDate = getRejectionFlowLastTrialDate(d.getDirectDebitRejectionToDo() != null ?
                                d.getDirectDebitRejectionToDo() : d.getDirectDebitBouncingRejectionToDo());

                        if (Setup.getRepository(GraphicDesignerTodoRepository.class)
                                .existsByContractIdAndToDoTypeAndCreationDateGreaterThan(
                                        contract.getId(), lastTrialDate)) {
                            logger.info("exits graphic designer created after latest trial Date -> exiting");
                            return CcPaymentSectionButtonVisibility.DISABLED;
                        }

                        return CcPaymentSectionButtonVisibility.ENABLED;
                    case PAYMENT_SECTION_INCOMPLETE_DDS_MISSING_BANK_INFO_DEFAULT_MESSAGE:
                        if (Setup.getRepository(GraphicDesignerTodoRepository.class)
                                .existsByContractIdAndToDoTypeAndCompletedFalse(
                                        contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE)) {
                            logger.info("Exists graphic designer running contract id: " + contract.getId());
                            return CcPaymentSectionButtonVisibility.DISABLED;
                        }

                        return Setup.getRepository(DirectDebitRepository.class)
                                .existsIncompleteDDsByCpt(contract.getActiveContractPaymentTerm()) ?
                                CcPaymentSectionButtonVisibility.ENABLED :
                                CcPaymentSectionButtonVisibility.DISABLED;
                }

                boolean existsPendingDd = Setup.getRepository(DirectDebitRepository.class)
                        .existsByContractPaymentTermAndCategoryBAndStatusIn(
                                contract.getActiveContractPaymentTerm(),
                                Arrays.asList(DirectDebitCategory.A, DirectDebitCategory.B),
                                Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY));

                return existsPendingDd ?
                        CcPaymentSectionButtonVisibility.DISABLED :
                        CcPaymentSectionButtonVisibility.ENABLED;
            case ADD_AUTOMATIC_MONTHLY_BANK:
                Date d = null;
                switch (template) {
                    case PAYMENT_SECTION_INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD_DEFAULT_MESSAGE:
                    case PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE:
                    case PAYMENT_SECTION_ONE_MONTH_AGREEMENT_DEFAULT_MESSAGE:
                        d = (Date) map.get("flowStartDate");
                        break;
                }
                return clientProvidedNewDetailsAfterDate(contract, d) ?
                        CcPaymentSectionButtonVisibility.DISABLED :
                        CcPaymentSectionButtonVisibility.ENABLED;
            default:
                return CcPaymentSectionButtonVisibility.ENABLED;
        }
    }

    public boolean allowSwitchBankAccountFromBouncedPaymentOptions(Contract c) {
        Payment p = paymentRepository.findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(
                c, PaymentStatus.BOUNCED);
        if (p == null) return false;

        return isButtonEnabled(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS,
                CcPaymentSectionButton.CHANGE_BANK_DETAILS, c,
                new HashMap<String, Object>(){{
                    put("bouncedPayment", p);
                }});
    }

    private Date getRejectionFlowLastTrialDate(DirectDebitRejectionToDo todo) {

        HistorySelectQuery<DirectDebitRejectionToDo> query = new HistorySelectQuery<>(DirectDebitRejectionToDo.class);
        query.filterBy("id", "=", todo.getId());
        query.filterByChanged(todo.getLastRejectCategory().equals(DirectDebitRejectCategory.Signature) ? "reSignTrials" : "trials");
        query.sortBy("lastModificationDate", false);
        query.setLimit(1);

        List<DirectDebitRejectionToDo> l = query.execute();
        return l.isEmpty() ? todo.getLastModificationDate() : l.get(0).getLastModificationDate();
    }

    @Deprecated
    public Map<String, String> resolveDdsGetLastNotification(Contract contract, CcAppCmsTemplate template, boolean compileText) {
        logger.log(Level.INFO, "Contract id" + contract.getId());
        logger.log(Level.INFO, "ddTemplatesCode " + template);

        Map<String, String> output = new HashMap<>();

        List<DDMessagingType> types = new ArrayList<>();
        List<String> notificationNames = Arrays.asList("-1");
        List<String> smsNames = Arrays.asList("-1");
        switch (template) {
            case CC_PAYMENT_PAID_BY_CARD_AND_NOT_SUBMIT_DD:
                types.add(DDMessagingType.ClientPaidCashAndNoSignatureProvided);
                notificationNames = Arrays.asList("PayTab_Thanks_Message_Notification_CC",
                        "PayTab_Thanks_Message_Notification_MV",
                        MvNotificationTemplateCode.MV_PAYTABS_THANKS_MESSAGE_NOTIFICATION.toString(),
                        CcNotificationTemplateCode.CC_PAYTAB_THANKS_MESSAGE_NOTIFICATION.toString());
                smsNames = Arrays.asList("PayTab_Thanks_Message_SMS_CC",
                        "PayTab_Thanks_Message_SMS_MV",
                        MvSmsTemplateCode.MV_PAYTABS_THANKS_MESSAGE_SMS.toString(),
                        CcSmsTemplateCode.CC_PAYTAB_THANKS_MESSAGE_SMS.toString());
                break;
            case CC_PAYMENT_INCOMPLETE_DDS:
                types.add(DDMessagingType.IncompleteDDRejectedByDataEntry);
                break;
            case CC_PAYMENT_BOUNCED_DDS:
            case CC_PAYMENT_BOUNCED_DDS_PAID:
                //ACC-4914
                Map<String, Object> map = getBouncedPaymentCode(contract);
                if (map.get("code") != null && ((String) map.get("code")).equals("has_e-signature_and_no_manual_dd")) {
                    types.addAll(Arrays.asList(DDMessagingType.BouncedPayment, DDMessagingType.DirectDebitRejected));
                    break;
                }
                types.add(DDMessagingType.BouncedPayment);
                break;
            case CC_PAYMENT_REJECTED_DDS:
                types.add(DDMessagingType.DirectDebitRejected);
                break;
            default:
                return new HashMap<>();
        }

        types.add(DDMessagingType.Termination);

        PushNotification lastNotification = null;
        List<PushNotification> notifications = disablePushNotificationRepository
                .findLastNotificationByEvent(
                        contract.getClient().getId().toString(), types,
                        notificationNames, contract.getId(), PageRequest.of(0,1));

        if (!notifications.isEmpty()) lastNotification = notifications.get(0);

        Sms lastSms = null;
        List<Sms> smsList = localSmsRepository.findLastSmsByType(
                contract.getClient().getId(), types, smsNames, PageRequest.of(0,1));
        if (!smsList.isEmpty()) {
            lastSms = smsList.get(0);
        }

        if (lastNotification != null) {     // normal case the flow started
            Template notifTemplate = Setup.getRepository(TemplateRepository.class)
                    .findByNameIgnoreCase(lastNotification.getType().getCode());

            if (lastSms == null || lastSms.getType() == null ||
                    notifTemplate.getName().equals(lastSms.getType().getCode()) ||
                    lastNotification.getCreationDate().after(lastSms.getCreationDate())) {

                output.put("code", lastNotification.getType() == null ? "" :
                        lastNotification.getType().getCode());

                if(compileText) output.put("text", buildExternalLinks(lastNotification, template, contract));
                logger.info("normal case the flow started: " + output.entrySet());
                return output;
            }
        }

        if (lastNotification == null && lastSms == null) {  // flow didn't start yet
            Template ddMessagingTemplate = getFirstMessageByFlowType(types.get(0), contract);
            if (ddMessagingTemplate != null) {
                output.put("code", ddMessagingTemplate.getName());
                if(compileText) output.put("text", compileTemplate(ddMessagingTemplate, contract, types.get(0), template));
                logger.info("flow didn't start yet: " + output.entrySet());

                return output;
            }
        }


        if (lastSms != null && (lastNotification == null ||
                lastSms.getCreationDate().after(lastNotification.getCreationDate()))) {

            // the client not logged in or the notification not sent
            output.put("code", lastSms.getTemplate() != null ?
                    lastSms.getTemplate().getName() :
                    lastNotification == null || lastNotification.getType() == null ?
                            "" : lastNotification.getType().getCode());
            if(compileText) output.put("text", compileTemplate(lastSms.getTemplate(), contract, types.get(0), template));
            logger.info("the client not logged in or the notification not sent: " + output.entrySet());

            return output;
        }

        return new HashMap<>();
    }

    @Deprecated
    public Template getFirstMessageByFlowType(DDMessagingType type, Contract contract) {
        logger.log(Level.SEVERE, "type id : {0}", type);
        logger.log(Level.SEVERE, "contract id {0}: ", contract.getId());
        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();
        logger.log(Level.SEVERE, "contractPaymentTerm id : {0}", contractPaymentTerm.getId());

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("isActive", "=", true);
        query.filterBy("contractProspectTypes", "like", "%" + contract.getContractProspectType().getCode() + "%");

        switch (type) {
            case BouncedPayment:
                Map<String, Object> map = getBouncedPaymentCode(contract);
                String code = (String) map.get("code");

                if (code == null) break;

                if (code.equals("has_e-signature_and_no_manual_dd")) { //ACC-4914
                    Payment payment = (Payment) map.get("payment");
                    if (payment.getTrials() == 0) {
                        return TemplateUtil.getTemplate(
                                CcAppCmsTemplate.CC_PAYMENT_PENDING_DD_NO_APPROVED_DD.toString());
                    } else {
                        type = DDMessagingType.DirectDebitRejected;
                        filterDirectDebitRejectedMessage(query, contractPaymentTerm);
                    }
                } else {
                    query.filterBy("bouncedPaymentStatus.code", "=", code);
                }
                break;

            case DirectDebitRejected:
                filterDirectDebitRejectedMessage(query, contractPaymentTerm);
                break;

            case IncompleteDDRejectedByDataEntry:
                int index = 0;
                DirectDebitDataEntryRejectCategory dataEntryRejectCategory = null;

                if (contractPaymentTerm.getIsEidRejected()) index += 1;
                if (contractPaymentTerm.getIsIBANRejected()) index += 2;
                if (contractPaymentTerm.getIsAccountHolderRejected()) index +=4;
                logger.log(Level.SEVERE, "index : {0}", index);

                switch (index) {
                    case 1:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.WrongEID;
                        break;
                    case 2:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.WrongIBAN;
                        break;
                    case 3:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.WrongEIDAndIBAN;
                        break;
                    case 4:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.WrongAccountName;
                        break;
                    case 5:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.WrongEIDAndAccountName;
                        break;
                    case 6:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.WrongIBANAndAccountName;
                        break;
                    case 7:
                        dataEntryRejectCategory = DirectDebitDataEntryRejectCategory.AllDataIncorrect;
                        break;
                }
                if (dataEntryRejectCategory == null) break;
                query.filterBy("dataEntryRejectCategory", "=", dataEntryRejectCategory);
                break;
        }

        query.filterBy("event", "=", type);
        query.sortBy("trials", true, true);
        query.sortBy("reminders", true, true);
        query.sortBy("id", false, true);
        query.setLimit(1);

        List<DDMessaging> ddMessagingList = query.execute();

        if (!ddMessagingList.isEmpty()) {
            logger.log(Level.SEVERE, "ddMessaging id : {0}", ddMessagingList.get(0).getId());
            return Setup.getApplicationContext()
                    .getBean(DDMessagingService.class)
                    .getDdMessagingContract(ddMessagingList.get(0), contractPaymentTerm.getBank())
                    .getClientTemplate();
        }

        return null;
    }

    //ACC-4914
    @Deprecated
    private Map<String, Object> getBouncedPaymentCode(Contract contract) {
        Map<String, Object> map = new HashMap<>();
        Payment payment = paymentRepository.findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(
                contract, PaymentStatus.BOUNCED);

        if (payment == null) return map;

        map.put("payment", payment);
        logger.log(Level.SEVERE, "payment id : {0}", payment.getId());

        if (payment.getDirectDebit() != null && payment.getDirectDebit().getManualDdfFile() != null) {
            logger.log(Level.SEVERE, "BouncedPaymentMessagesBR execute payment with e-sign and manual dd");
            map.put("code", "has_e-signature_and_manual_dd");
            return map;
        }

        Map<String, Object> signatureType = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class)
                .getLastSignatureType(contract.getClient(), contract.getClient().getEid(), true, false);

        List<Attachment> approvedSignatureByClient = (List<Attachment>) signatureType.get("currentSignatures");
        boolean paymentHasNoESignature = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByKeyAndOriginAndDeletedFalse(Payment.HAS_NO_E_SIGNATURE, payment) != null;

        if (payment.getDirectDebit() == null ||
                payment.getDirectDebit().getCategory() == DirectDebitCategory.B) {

            if (approvedSignatureByClient != null &&
                    !approvedSignatureByClient.isEmpty() &&
                    !paymentHasNoESignature) {

                logger.log(Level.SEVERE, "BouncedPaymentMessagesBR execute payment with e-sign and no manual dd");
                map.put("code", "has_e-signature_and_no_manual_dd");
            } else {
                logger.log(Level.SEVERE, "BouncedPaymentMessagesBR execute payment with no e-sign");
                map.put("code", "has_no_e-signature");
            }
        }

        return map;
    }

    @Deprecated
    private void filterDirectDebitRejectedMessage(
            SelectQuery<DDMessaging> query,
            ContractPaymentTerm contractPaymentTerm) {

        List<DirectDebitRejectionToDo> debitRejectionToDos = Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                .findActiveByContractPaymentTerm(contractPaymentTerm);

        if (debitRejectionToDos.isEmpty()) return;
        logger.log(Level.SEVERE, "debitRejectionToDo id : {0}", debitRejectionToDos.get(0).getId());

        query.filterBy("rejectCategory", "=", debitRejectionToDos.get(0).getLastRejectCategory());
        query.filterBy("ddCategory", "like",
                "%" + debitRejectionToDos.get(0).getLastDirectDebit().getCategory().toString() + "%");
    }

    @Deprecated
    public String compileTemplate(
            Template template,
            Contract contract,
            DDMessagingType type,
            CcAppCmsTemplate cmstemplate) {

        ChannelSpecificSetting channelSpecificSetting = template.getChannelSetting(
                ChannelSpecificSettingType.Notification.toString());
        logger.log(Level.INFO, "id: {0}", (channelSpecificSetting == null ? null : channelSpecificSetting.getId()));

        String text = channelSpecificSetting == null ? template.getText() : channelSpecificSetting.getText();

        Map<String, String> parameters = new HashMap<>();
        parameters.put("greetings", contract.getContractProspectType().getCode()
                .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)) ?
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA) :
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC));
        parameters.put("dear_receiver_name", contract.getClient() != null ?
                "Dear " + (contract.getClient().getTitle() != null ?
                        contract.getClient().getTitle().getName() + ". " : "") + contract.getClient().getFirstName(true) : "");
        parameters.put("client_nickname_or_first_name", StringUtils.getClientNicknameOrFirstName(contract.getClient()));
        parameters.put("maid_name", contract.getHousemaid() != null ? contract.getHousemaid().getName() : "");
        parameters.put("maid_first_name", contract.getHousemaid() == null || contract.getHousemaid().getFirstName()== null ?
                parameters.get("maid_name") : contract.getHousemaid().getFirstName());

        if (type.equals(DDMessagingType.BouncedPayment)) {
            Date soonestDate = Setup.getApplicationContext().getBean(Utils.class).getSoonestPayrollDate();
            Payment payment = paymentRepository
                    .findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(
                            contract, PaymentStatus.BOUNCED);
            String bouncedRoute = payment != null ? "<a " + "<a style=\"font-weight: bold;" +
                    (cmstemplate.equals(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS) ? "color:#0000ff;" : "") + "\" " +
                    (cmstemplate.equals(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS) ? " href=\"route://payment_bounced?bouncedPaymentId=" +
                            payment.getId() + "&amount=" + payment.getAmountOfPayment() +
                            "&contractId=" + contract.getId() + "\" " : "") +
                    "><span>@label@</span></a>" : "";

            parameters.put("soonest_payroll", soonestDate != null ? DateUtil.formatDateDashedV2(soonestDate) : "");
            parameters.put("monthly_payment", payment != null ? String.valueOf(payment.getAmountOfPayment().intValue()) : "");
            parameters.put("bounced_payment_amount", payment != null ?
                    String.valueOf(payment.getAmountOfPayment().intValue())  : "");
            parameters.put("latest_bounced_amount", parameters.get("bounced_payment_amount"));

            parameters.put("link1", bouncedRoute.replace("@label@", "click here"));
            parameters.put("bounced_payment_click_here", bouncedRoute.replace("@label@", "click here"));

            // ACC-4706
            parameters.put("bounced_payment_clicking_here", bouncedRoute.replace("@label@", "clicking here"));
            parameters.put("payment_bounced_sign_now_clicking_here", payment != null ? "<a href=\"route://payment_different_bank" +
                    "?bouncedPaymentId=" + String.valueOf(payment.getId()) +
                    "&previousAppBarTitle=back&isSwitch=false&contractId=" + contract.getId() +
                     "\" ><span>clicking here</span></a>" : "");

            parameters.put("cheque_number", payment != null ? payment.getChequeNumber() : "");
            parameters.put("bank_name", payment != null && payment.getBankName() != null ? payment.getBankName().getName() : "");
            parameters.put("cheque_name", payment != null ? payment.getChequeName() : "");
        }

        String link_send_dd_details = "<a href=\"route://payment_different_bank?isSwitch=false&contractId=" + contract.getId() +"\"><span>link_text</span></a>";
        parameters.put("link_send_dd_details", link_send_dd_details.replace("link_text", "click here"));
        parameters.put("link_send_dd_details_clicking_here", link_send_dd_details.replace("link_text", "clicking here"));
        parameters.put("link_send_dd_details_click_here", link_send_dd_details.replace("link_text", "click here"));
        parameters.put("link_send_dd_details_here", link_send_dd_details.replace("link_text", "here"));
        parameters.put("link_send_dd_details_payment_link", link_send_dd_details.replace("link_text", "payment link"));

        parameters.put("scheduled_termination_date",
                contract.getScheduledDateOfTermination() == null ? "" :
                        DateUtil.formatClientFullDate(contract.getScheduledDateOfTermination()));
        parameters.put("scheduled_termination_date - 1 day",
                contract.getScheduledDateOfTermination() == null ? "" :
                        DateUtil.formatClientFullDate(new LocalDate(
                                contract.getScheduledDateOfTermination()).minusDays(1).toDate()));

        // switch bank account
        if (text.contains("@pay_using_different_bank_account_click_here@")) {
            parameters.put("pay_using_different_bank_account_click_here",
                    "<a href=\"route://my_monthly_payments_different_bank?isSwitch=true&contractId=" + contract.getId() +
                            "\"><span>click here</span></a>");
        }

        try {
            ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
            parameters.put("Account_holder_name", cpt.getAccountName() == null ? "" : cpt.getAccountName());
        } catch(Exception ex) {
            logger.severe("Error while fetching active CPT of contract: " + contract.getId());
            parameters.put("Account_holder_name", "");
        }

        parameters.put("client_first_name",  contract.getClient().getName() == null ?
                "" : contract.getClient().getName());
        parameters.put("paid_end_date", new LocalDate(contract.getPaidEndDate())
                .toString("yyyy-MM-dd"));
        parameters.put("paid_end_date - 1", new LocalDate(contract.getPaidEndDate()).minusDays(1)
                .toString("yyyy-MM-dd"));
        parameters.put("adjusted_end_date", new LocalDate(contract.getAdjustedEndDate())
                .toString("yyyy-MM-dd"));
        parameters.put("adjusted_end_date - 1", new LocalDate(contract.getAdjustedEndDate()).minusDays(1)
                .toString("yyyy-MM-dd"));

        parameters.put("accommodation_location", Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ACCOMMODATION_LOCATION_MESSAGES));

        if(channelSpecificSetting == null) {
            return TemplateUtil.compileTemplate(template, contract, parameters);
        } else {
            return TemplateUtil.compileTemplateText(template, channelSpecificSetting,
                    channelSpecificSetting.getText(), contract, parameters);
        }
    }

    @Deprecated
    public String buildExternalLinks(PushNotification pushNotification, CcAppCmsTemplate template, Contract contract) {
        logger.log(Level.INFO, "pushNotification id " + pushNotification.getId());
        String text = pushNotification.getContent();

        try {
            ObjectMapper mapper = new ObjectMapper();
            TypeReference<Map<String, Map<String, Object>>> typeRef = new TypeReference<Map<String, Map<String, Object>>>() {};
            if (pushNotification.getContext() != null) {
                // ACC-4706 bounced link
                if (text.contains("@bounced_payment_clicking_here@")) {
                    Map<String, Object> m = mapper.readValue(pushNotification.getContext(), typeRef)
                            .get("bounced_payment_clicking_here");
                    Map<String, String> appRouteArguments = (Map<String, String> ) m.get("appRouteArguments");
                    text = text.replace("@bounced_payment_clicking_here@", "<a style=\"font-weight: bold;" +
                            (template.equals(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS) ? "color:#0000ff;" : "") + "\" " +
                    (template.equals(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS) ? " href=\"route:/" + m.get("appRouteName") +
                            "?bouncedPaymentId=" + appRouteArguments.get("bouncedPaymentId") +
                             "&amount=" + appRouteArguments.get("amount") + "&contractId=" + contract.getId() +
                            (appRouteArguments.containsKey("todoUuid") ? "&todoUuid=" + appRouteArguments.get("todoUuid") : "") +"\" " : "") +
                            "><span>clicking here</span></a>");
                }

                // switch bank account
                if (text.contains("@pay_using_different_bank_account_click_here@")) {
                    Map<String, Object> m = mapper.readValue(pushNotification.getContext(), typeRef)
                            .get("pay_using_different_bank_account_click_here");
                    text = text.replace("@pay_using_different_bank_account_click_here@",
                            "<a href=\"route:/" + m.get("appRouteName") +
                                    "?isSwitch=true&contractId=" + contract.getId() + "\"><span>click here</span></a>");
                }

                // bounced link
                if (text.contains("@payment_bounced_sign_now_clicking_here@")) {
                    Map<String, Object> m = mapper.readValue(pushNotification.getContext(), typeRef)
                            .get("payment_bounced_sign_now_clicking_here");
                    text = text.replace("@payment_bounced_sign_now_clicking_here@",
                            "<a href=\"route:/" + m.get("appRouteName") +
                                    "?previousAppBarTitle=back&isSwitch=false&contractId=" + contract.getId() +
                                    "\"><span>clicking here</span></a>");
                }

                // uber link
                Map<String, Object> action1 = mapper.readValue(pushNotification.getContext(), typeRef)
                        .get("@Action1@");

                if (action1 != null) {
                    Map<String, String> appRouteArguments = (Map<String, String> ) action1.get("appRouteArguments");

                    if (appRouteArguments.containsKey("notificationArguments") && appRouteArguments.get("notificationArguments") != null) {
                        Map<String, String> notificationArguments = mapper.readValue(appRouteArguments.get("notificationArguments"), Map.class);

                        if (action1.get("appRouteName").equals("/replace/uber_becka/send_uber")) {
                            return text + "<br/>" + "<a href=\"route:/" + action1.get("appRouteName") +
                                    "?oldHousemaidId=" + String.valueOf(notificationArguments.get("oldHousemaidId"))
                                    + "&oldHousemaidName=" + String.valueOf(notificationArguments.get("oldHousemaidName"))
                                    + "&contractId=" + String.valueOf(notificationArguments.get("contractId"))
                                    + "&reschedule=" + String.valueOf(notificationArguments.get("reschedule"))
                                    + "&currentTaxiDateTime=" + String.valueOf(notificationArguments.get("currentTaxiDateTime"))
                                    + "&luggageInClintHome=" + String.valueOf(notificationArguments.get("luggageInClintHome"))
                                    + "&newWorkOrder=" + String.valueOf(notificationArguments.get("newWorkOrder"))
                                    + "&oldWorkOrder=" + String.valueOf(notificationArguments.get("oldWorkOrder"))
                                    + "&complaintCode=" + String.valueOf(notificationArguments.get("complaintCode"))
                                    + "&taxiWorkOrderId=" + String.valueOf(notificationArguments.get("taxiWorkOrderId"))
                                    + "&alreadyLeftHome=" + String.valueOf(notificationArguments.get("alreadyLeftHome"))
                                    + "&luggageTaxiWorkOrderUuid=" + String.valueOf(notificationArguments.get("luggageTaxiWorkOrderUuid"))
                                    + "\" ><span>" + action1.get("text") + "</span></a>";

                        } else if (action1.get("appRouteName").equals("/request_uber")) {
                            return text + "<br/>" + "<a href=\"route:/" + action1.get("appRouteName") +
                                    "?housemaidId=" + String.valueOf(notificationArguments.get("housemaidId"))
                                    + "&housemaidName=" + String.valueOf(notificationArguments.get("housemaidName"))
                                    + "&contractId=" + String.valueOf(notificationArguments.get("contractId"))
                                    + "&notificationType=" + String.valueOf(notificationArguments.get("notificationType"))
                                    + "\" ><span>" + action1.get("text") + "</span></a>";
                        }
                    }
                }

                // old bounced link
                String parameterName = "";
                if (text.contains("@link1@")) {
                    parameterName = "link1";
                    Map<String, Object> m = mapper.readValue(pushNotification.getContext(), typeRef).get(parameterName);
                    Map<String, String> appRouteArguments = (Map<String, String> ) m.get("appRouteArguments");
                    return text.replace("@" + parameterName + "@", "<a style=\"font-weight: bold; " +
                            (template.equals(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS) ? "color:#0000ff;" : "") + "\" " +
                            (template.equals(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS) ? "\" href=\"route:/" + m.get("appRouteName") +
                                    "?bouncedPaymentId=" + appRouteArguments.get("bouncedPaymentId") +
                                    "&contractId=" + contract.getId()
                                    + "&amount=" + appRouteArguments.get("amount") + "\" " : "") +
                            "><span>" + m.get("text") + "</span></a>");

                } else if (text.contains("@link2@")) {
                    parameterName = "link2";
                } else if (text.contains("@link3@")) {
                    parameterName = "link3";
                } else if (text.contains("@link4@")) {
                    parameterName = "link4";
                } else if (text.contains("@link5@")) {
                    parameterName = "link5";
                } else if (text.contains("@link6@")) {
                    parameterName = "link6";
                } else if (text.contains("@link7@")) {
                    parameterName = "link7";
                } else if (text.contains("@link8@")) {
                    parameterName = "link8";
                } else if (text.contains("@link9@")) {
                    parameterName = "link9";
                }

                Map<String, Object> m1 = mapper.readValue(pushNotification.getContext(), typeRef).get(parameterName);

                switch (parameterName) {
                    case "link2" :
                    case "link6" :
                    case "link7" :
                    case "link8" :
                    case "link9" :
                        return text.replace("@" + parameterName + "@",
                                "<a href=\"route://payment_different_bank?isSwitch=false" +
                                        "&contractId=" + contract.getId() + "\"><span>" + m1.get("text") + "</span></a>");
                    case "link3" :
                    case "link4" :
                    case "link5" :
                        return text.replace("@" + parameterName + "@",
                                "<a href=\"" + m1.get("hyperlink") + "\" ><span>" + m1.get("text") + "</span></a>");
                }
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return text;
    }

    public String bouncedPayByCardFaqBody(Long contractId) {
        Template t = Setup.getRepository(TemplateRepository.class)
                .findByNameIgnoreCase(CcAppCmsTemplate.CC_FAQ_BOUNCED_PAY_BY_CARD.toString());

        Payment nextPayment = paymentRepository.findFirstByContract_IdAndStatusInOrderByDateOfPaymentAsc(
                contractId, Arrays.asList(PaymentStatus.PRE_PDP, PaymentStatus.PDC));
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);

        if(nextPayment == null) {
            return TemplateUtil.compileTemplate(t, null, new HashMap<String, String>() {{
                put("pay_link", "");
            }});
        } else {
            String link = "context://";

            HashMap<String, Object> action1 = new HashMap<>();
            action1.put("type", AppActionType.LINK);
            action1.put("text", "pay_by_credit_card");
            action1.put("functionType", FunctionType.NAVIGATE);
            action1.put("navigationType", NavigationType.INAPP);
            action1.put("appRouteName", "/my_monthly_payments_pay_with_cc");
            action1.put("appRouteArguments", new HashMap<String, Object>() {{
                put("amount", nextPayment.getAmountOfPayment());
                put("futurePaymentId", nextPayment.getId().toString());
                put("contractId", contractId);
                put("contractUuid", contract.getUuid());
            }});

            try {
                link += new ObjectMapper().writeValueAsString(
                        new HashMap<String, Object>() {{
                            put("action_1", action1);
                        }});
            } catch (JsonProcessingException ex) {
                Logger.getLogger(CCAppContentService.class.getName()).log(Level.SEVERE, null, ex);
                link = "";
            }

            String finalLink = link;
            return TemplateUtil.compileTemplate(t, null, new HashMap<String, String>() {{
                put("pay_link", finalLink);
            }});
        }
    }

    public enum CcPaymentSectionButton {
        SIGN_NOW, PAY_BY_CARD, CHANGE_BANK_DETAILS, SUBMIT_PAYMENT_FORM, PAY_USING_ALTERNATIVE_METHODS, ADD_AUTOMATIC_MONTHLY_BANK
    }

    public enum CcPaymentSectionButtonVisibility {
        ENABLED, DISABLED, HIDDEN
    }

    private ContractPaymentConfirmationToDo getToDoForPayingViaCreditCard(Contract contract) {
        // ACC-5509 ACC-5687
        return Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                .findFirstByContractPaymentTerm_ContractAndSourceInAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(contract,
                        contract.isOneMonthAgreement() ?
                                Collections.singletonList(ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT) :
                                Collections.singletonList(ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card));

    }

    public String clientPayingViaCreditCard(Contract contract, boolean enableCtas, ContractPaymentConfirmationToDo todo) {

        Template t = Setup.getRepository(TemplateRepository.class)
                .findByNameIgnoreCase(contract.isOneMonthAgreement() ?
                        CcAppCmsTemplate.PAYMENT_SECTION_ONE_MONTH_AGREEMENT_DEFAULT_MESSAGE.toString() :
                        CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE.toString());

        boolean firstMonth = new LocalDate(contract.getStartOfContract()).toString("yyyy-MM-dd")
                .equals(new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd"));
        //LocalDate dueDate = getUpcomingPaymentDate(contract);
        if (todo == null || todo.isShowOnERP() || !enableCtas) {
            return TemplateUtil.compileTemplate(t, contract, new HashMap<String, String>() {{
                put("pay_link", "");
                put("amount", "");
                put("paid_end_date", new LocalDate(contract.getPaidEndDate()).toString("dd MMM, yyyy"));
                //put("due_date",dueDate.toString("dd MMM, yyyy"));
                put("due_date", new LocalDate(contract.getPaidEndDate()).plusDays(firstMonth ? 0 : 1).toString("dd MMM, yyyy"));
            }});
        } else {
            boolean isRecurring = ContractPaymentConfirmationToDoService.isEligibleForTokenizationViaConfirmationToDO(todo);

            String link = "context://";

            HashMap<String, Object> action1 = new HashMap<>();
            action1.put("type", AppActionType.LINK);
            action1.put("text", "pay_by_credit_card");
            action1.put("functionType", FunctionType.NAVIGATE);
            action1.put("navigationType", NavigationType.INAPP);
            action1.put("appRouteName", "/my_monthly_payments_pay_with_cc");
            ContractPaymentConfirmationToDo finalTodo = todo;
            action1.put("appRouteArguments", new HashMap<String, Object>() {{
                put("amount", finalTodo.getTotalAmount().intValue());
                put("todoUuid", finalTodo.getUuid());
                put("contractId", contract.getId().toString());
                put("contractUuid", contract.getUuid());
                put("isRecurring", isRecurring);
            }});

            try {
                link += new ObjectMapper().writeValueAsString(
                        new HashMap<String, Object>() {{
                            put("action_1", action1);
                        }});
            } catch (JsonProcessingException ex) {
                Logger.getLogger(CCAppContentService.class.getName()).log(Level.SEVERE, null, ex);
                link = "";
            }

            logger.info("link: " + link);
            Map<String, Object> para = new HashMap<>();
            para.put("pay_link", link);
            para.put("amount", PaymentHelper.df.format(todo.getTotalAmount().intValue()));
            para.put("paid_end_date", new LocalDate(contract.getPaidEndDate()).toString("dd MMM, yyyy"));
            //put("due_date",dueDate.toString("dd MMM, yyyy"));
            para.put("due_date", new LocalDate(contract.getPaidEndDate()).plusDays(firstMonth ? 0 : 1).toString("dd MMM, yyyy"));
            return TemplateUtil.compileTemplate(t, contract, para);
        }
    }

    public String clientPayingViaCreditCardHasToken(ContractPaymentTerm cpt) {

        Template t = Setup.getRepository(TemplateRepository.class)
                .findByNameIgnoreCase(CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_WITH_TOKEN_DEFAULT_MESSAGE.toString());

        return TemplateUtil.compileTemplate(t, cpt.getContract(), new HashMap<String, String>() {{
            put("amount", PaymentHelper.df.format(((Double)(cpt.getMonthlyPayment() - cpt.getDiscount())).intValue()));
            put("paid_end_date", new LocalDate(cpt.getContract().getPaidEndDate()).toString("dd MMM, yyyy"));
            put("due_date", new LocalDate(cpt.getContract().getPaidEndDate()).plusDays(1).toString("dd MMM, yyyy"));
        }});
    }

    public PaymentSectionInfo getPaymentSectionData(Contract contract) {

        Map<String, Object> m = ccAppService.ccPaymentsSectionDataAcc7142(contract);
        PaymentSectionInfo paymentSectionInfo = new PaymentSectionInfo();

        if (contract.getHousemaid() != null && contract.getHousemaid().getName() != null) {
            String maidsName = contract.getHousemaid().getName().toLowerCase().contains("unknown") ?
                    "My_maid" : contract.getHousemaid().getName();
            paymentSectionInfo.setTitle("My payments for " + maidsName + "'s services");
        } else {
            paymentSectionInfo.setTitle("My monthly payments");
        }

        List<PaymentSectionHeaderBox> headerBoxes = new ArrayList<>();

        receivePaymentNotificationsInfo(paymentSectionInfo, contract);

        if (contract.getScheduledDateOfTermination() != null) {
            PaymentSectionHeaderBox headerBox = new PaymentSectionHeaderBox(PaymentSectionBoxColor.ORANGE.getLabel(),
                    CcAppCmsTemplate.PAYMENT_SECTION_CONTRACT_SCHEDULED_FOR_TERMINATION.toString(),
                    TemplateUtil.compileTemplate(
                            TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_CONTRACT_SCHEDULED_FOR_TERMINATION.toString()),
                            contract, new HashMap<String, Object>(){{
                                put("termination_date",
                                        new LocalDate(contract.getScheduledDateOfTermination()).toString("dd MMM, yyyy"));
                            }})); // orange
            headerBoxes.add(headerBox);
        }

        // Refund flow
        ClientRefundToDo refundToDo = clientRefundTodoRepository
                .findFirstByContractAndStatus(contract, ClientRefundStatus.PENDING);

        String templateName = null;
        if (refundToDo != null) {
            logger.info("refund pending id " + refundToDo.getId());

            if (refundToDo.isConditionalRefund()) { // screen #3
                templateName = CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_CONDITIONAL.toString();
            } else { // screen #2
                templateName = CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_NON_CONDITIONAL.toString();
            }
        } else {
            refundToDo = clientRefundTodoRepository.findFirstByContractAndStatusAndCreationDateGreaterThanEqualOrderByCreationDateAsc(
                    contract, ClientRefundStatus.PAID, new DateTime().minusDays(3).toDate());
            if (refundToDo != null) {
                logger.info("refund paid id " + refundToDo.getId());
                // screen #4
                templateName = CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_PROOF_UPLOADED.toString();
            }
        }

        if (templateName != null) {
            logger.info("refund templateName " + templateName);

            PaymentSectionHeaderBox headerBox = new PaymentSectionHeaderBox(PaymentSectionBoxColor.ORANGE.getLabel(), templateName,
                    TemplateUtil.compileTemplate(TemplateUtil.getTemplate(templateName), contract, new HashMap<>())); // orange

            headerBoxes.add(headerBox);
        }

        if (!headerBoxes.isEmpty()) {
            paymentSectionInfo.setHeaderBoxes(headerBoxes);
        }

        if (!m.containsKey("sectionKey")) {
            noRulesApply(paymentSectionInfo, contract);
            return paymentSectionInfo;
        }

        Map<String, Object> contractMap = new HashMap<String, Object>() {{
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }};

        ActionButton viewYourPaymentHistory = new ActionButton(
                "View your payment history", "/my_monthly_payments_history", contractMap, true, ActionButton.ButtonType.SECONDARY);

        ActionButton changeYourBankDetails = new ActionButton(
                "Change your bank details", "/my_monthly_payments_different_bank",
                new HashMap<String, Object>() {{
                    put("isSwitch", Boolean.TRUE.toString());
                    putAll(contractMap);
                }},true, ActionButton.ButtonType.PRIMARY);

        ActionButton addAutomaticMonthlyBank = new ActionButton(
                "Add Automatic Monthly Bank Payment Form", "/payment_different_bank",
                new HashMap<String, Object>() {{
                    put("isSwitch", Boolean.FALSE.toString());
                    putAll(contractMap);
                }}, true, ActionButton.ButtonType.PRIMARY);

        /*Map<String, Object> payNowInfo = new HashMap<>(contractMap);
        if (m.containsKey("amount"))
            payNowInfo.put("amount", m.get("amount"));
        if (m.containsKey("todoUuid"))
            payNowInfo.put("todoUuid", m.get("todoUuid"));
        ActionButton payNow = new ActionButton("Pay By Credit/Debit Card", "/my_monthly_payments_pay_with_cc", payNowInfo, true, ActionButton.ButtonType.PRIMARY);
*/
        paymentSectionInfo.setSectionKey((String) m.get("sectionKey"));
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        switch (CCAppService.CCAppPaymentStatusSection.valueOf((String) m.get("sectionKey"))) {
            case BOUNCED_PAYMENTS:
                bouncedPaymentFlowInfo(paymentSectionInfo, contract,
                        (Payment) m.get("bouncedPayment"), (Map<String, Object>) m.get("bankDetails"), changeYourBankDetails);
                break;
            case REJECTED_DD:
                rejectionFlowInfo(paymentSectionInfo, cpt, m, changeYourBankDetails);
                break;
            case INCOMPLETE_DD:
                incompleteDdFlowInfo(paymentSectionInfo, cpt, changeYourBankDetails, viewYourPaymentHistory);
                break;
            case CLIENT_PAYING_VIA_CREDIT_CARD:
                payingViaCreditCardFlowInfo(paymentSectionInfo, cpt, addAutomaticMonthlyBank, viewYourPaymentHistory);
                break;
            case INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD:
                iPAMFlowInfo(paymentSectionInfo, contract, addAutomaticMonthlyBank, viewYourPaymentHistory, m);
                break;
            case ONE_MONTH_AGREEMENT:
                oneMonthAgreementFlowInfo(paymentSectionInfo, cpt, addAutomaticMonthlyBank, viewYourPaymentHistory, m);
                break;
            case ONLINE_CREDIT_CARD_PAYMENT_REMINDERS:
                onlineReminderFlowInfo(paymentSectionInfo, cpt, (Map<String, Object>) m.get("bankDetails"),
                        (FlowProcessorEntity) m.get("reminderFlow"), changeYourBankDetails, viewYourPaymentHistory);
                break;
            case APPROVED_PAYMENTS:
                confirmedDdInfo(paymentSectionInfo, contract,
                        (Map<String, Object>) m.get("bankDetails"), changeYourBankDetails, viewYourPaymentHistory);
                break;
            case PENDING_DD:
                pendingDdInfo(paymentSectionInfo, contract, viewYourPaymentHistory);
                break;
        }

        return paymentSectionInfo;
    }

    public String getSignDdWebLinkForPaymentSection(
            ContractPaymentTerm cpt,
            DirectDebitRejectCategory category,
            boolean incompleteDd,
            boolean missingBankInfoRunning) {
        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", cpt);
        signDDMap.put("directDebitRejectCategory", category);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "CCAPP_PaymentSection");
            put("category", category);
            put("incompleteDd", incompleteDd);
            put("missingBankInfoRunning", missingBankInfoRunning);
        }});

        if (category != null && category.equals(DirectDebitRejectCategory.Signature)) {
            return utils.getSingDDLinkWithWrongSignatureTrue(signDDMap);
        }

        if (missingBankInfoRunning) {
            return utils.getSingDDLinkWithWrongSignatureTrueAndHideRejectionMessageTrue(signDDMap);
        }

        if (incompleteDd &&
                (cpt.getIsIBANRejected() ||
                        cpt.getIsEidRejected() ||
                        cpt.getIsAccountHolderRejected())) {
            signDDMap.put("ddMessagingType", DDMessagingType.IncompleteDDRejectedByDataEntry);
        } else if (!incompleteDd) {
            signDDMap.put("ignoreDataEntryRejection", true);
        }

        return utils.getSingDDLink(signDDMap);
    }

    private boolean isButtonEnabled(CcAppCmsTemplate template, CcPaymentSectionButton button, Contract contract) {
        return isButtonEnabled(template, button, contract, new HashMap<>());
    }

    private boolean isButtonEnabled(CcAppCmsTemplate template, CcPaymentSectionButton button, Contract contract, Map<String, Object> m) {
        return resolveButtonVisibility(template, button, contract, m).equals(CcPaymentSectionButtonVisibility.ENABLED);
    }

    private Template getTemplateViaCode(Map<String, Object> resolveContent) {

        if (!resolveContent.containsKey("code") || ((String) resolveContent.get("code")).isEmpty()) return null;

        logger.info("template name: " + resolveContent.get("code"));
        return TemplateUtil.getTemplate((String) resolveContent.get("code"));
    }

    private DDMessaging getDdMessagingViaTemplate(Template t) {
        if (t == null) return null;

        DDMessaging d = Setup.getRepository(DDMessagingRepository.class).findByClientTemplate(t);
        logger.info("DD Messaging id: " + (d == null ? "NULL" : d.getId()));

        return d;
    }

    public String getConditionalRefundPayment(Contract contract) {

        ClientRefundToDo todo = clientRefundTodoRepository
                .findFirstByContractAndStatus(contract, ClientRefundStatus.PENDING);
        if (todo == null) return "";

        return Setup.getApplicationContext()
                .getBean(ClientRefundService.class)
                .getConditionalRefundPayment(todo);
    }

    public String getViewProofTransferLink(Map<String, Object> body) {

        ClientRefundToDo clientRefundToDo = getCurrentRefundForPaymentSection((Contract) body.get("contract"));
        if (clientRefundToDo == null) return "";

        logger.log(Level.INFO, "clientRefundToDo id: {0}", clientRefundToDo.getId());
        PayrollAccountantTodo payrollAccountantTodo = clientRefundToDo.getPayrollAccountantTodos().stream()
                .filter(o -> o.getAttachment("client_refund_transfer_slip") != null)
                .findFirst().orElse(null);

        if (payrollAccountantTodo != null) {
            logger.log(Level.INFO, "payrollAccountantTodo id: {0}", payrollAccountantTodo.getId());

            return "<br/><a href=\"" + Setup.getApplicationContext()
                    .getBean(Shortener.class)
                    .shorten(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BACKEND_BASE_URL)
                            + "/public/download/" + payrollAccountantTodo.getAttachment("client_refund_transfer_slip").getUuid())
                    + "\"><span>" + body.get("text") + "</span></a> .";
        }
        return "";
    }

    public boolean preventSwitchingBankAccount(Contract c) {

        ContractPaymentTerm cpt = c.getActiveContractPaymentTerm();
        boolean bouncedFlowRunning = paymentService.existsRunningBouncedPaymentFlow(c);
        boolean rejectionFlowRunning = directDebitRejectionFlowService.existsRunningDirectDebitRejectionFlow(c);
        boolean incompleteDdFlowRunning = directDebitService.existsByDdStatuses(cpt, Collections.singletonList(DirectDebitStatus.IN_COMPLETE));

        return !bouncedFlowRunning &&
                !rejectionFlowRunning &&
                !incompleteDdFlowRunning &&
                directDebitService.existsByDdStatuses(cpt, Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY));

        // ACC-6703 Removed cause Ahmed changed the description
        // If we have running Switch bank account flow
        /*boolean isSwitchBankAccountFlowRunning = Setup.getApplicationContext()
                .getBean(SwitchingBankAccountService.class)
                .isSwitchBankAccountFlowRunning(cpt);
        logger.info("isSwitchBankAccountFlowRunning: " + isSwitchBankAccountFlowRunning);

        return isSwitchBankAccountFlowRunning;*/
    }

    // Payment sections Info

    // Bounced payment section
    private void bouncedPaymentFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            Contract contract,
            Payment p,
            Map<String, Object> bankDetails,
            ActionButton changeYourBankDetails) {

        String text = TemplateUtil.compileTemplate(TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_BOUNCED_PAYMENT_DEFAULT_MESSAGE.toString()),
                contract, new HashMap<String, String>() {{
                    put("amount", PaymentHelper.df.format((p.getAmountOfPayment()).intValue()));
                }});

        PaymentSectionBox box = new PaymentSectionBox(
                PaymentSectionBoxColor.RED.getLabel(), CcAppCmsTemplate.PAYMENT_SECTION_BOUNCED_PAYMENT_DEFAULT_MESSAGE.toString(),
                text, new ArrayList<>(), getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.RED));
        paymentSectionInfo.setBox(box);

        paymentSectionInfo.setBankDetails(bankDetails);

        ActionButton payUsingAlternativeMethods = new ActionButton(
                "Pay Using alternative Methods", "/payment_bounced",
                new HashMap<String, Object>() {{
                    put("bouncedPaymentId", p.getId());
                    put("amount", p.getAmountOfPayment());
                    put("contractId", contract.getId().toString());
                    put("contractUuid", contract.getUuid());
                }},
                isButtonEnabled(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS,
                        CcPaymentSectionButton.PAY_USING_ALTERNATIVE_METHODS, contract,
                        new HashMap<String, Object>(){{
                            put("bouncedPayment", p);
                        }}),
                ActionButton.ButtonType.PRIMARY);

        List<ActionButton> buttons = new ArrayList<>();
        buttons.add(payUsingAlternativeMethods);

        changeYourBankDetails.setEnabled(isButtonEnabled(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS,
                CcPaymentSectionButton.CHANGE_BANK_DETAILS, contract,
                new HashMap<String, Object>(){{
                    put("bouncedPayment", p);
                }}));
        changeYourBankDetails.setButtonType(ActionButton.ButtonType.SECONDARY);

        buttons.add(changeYourBankDetails);


        paymentSectionInfo.setActionButtons(buttons);
    }

    // Rejection DD flow section
    private void rejectionFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            ContractPaymentTerm cpt,
            Map<String, Object> m,
            ActionButton changeYourBankDetails) {

        DirectDebit d = (DirectDebit) m.get("rejectedDd");
        String text = TemplateUtil.compileTemplate(
                TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_DD_REJECTION_DEFAULT_MESSAGE.toString()),
                cpt.getContract(), new HashMap<>());
        PaymentSectionBox box = new PaymentSectionBox(PaymentSectionBoxColor.RED.getLabel(),
                CcAppCmsTemplate.PAYMENT_SECTION_DD_REJECTION_DEFAULT_MESSAGE.toString(), text, new ArrayList<>(),
                getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.RED));
        paymentSectionInfo.setBox(box);

        DirectDebitRejectCategory category = d.getDirectDebitRejectionToDo() != null ?
                d.getDirectDebitRejectionToDo().getLastRejectCategory() :
                d.getDirectDebitBouncingRejectionToDo().getLastRejectCategory();

        ActionButton signNowWebLink =  new ActionButton("Sign Now",
                getSignDdWebLinkForPaymentSection(cpt, category, false, false),
                d.getContractPaymentTerm().isActive() &&
                        isButtonEnabled(CcAppCmsTemplate.CC_PAYMENT_REJECTED_DDS, CcPaymentSectionButton.SIGN_NOW, cpt.getContract(),
                        new HashMap<String, Object>() {{
                            put("rejectedDd", m.get("rejectedDd"));
                        }}));

        List<ActionButton> buttons = new ArrayList<>();
        buttons.add(signNowWebLink);

        changeYourBankDetails.setEnabled(d.getContractPaymentTerm().isActive() &&
                isButtonEnabled(CcAppCmsTemplate.CC_PAYMENT_REJECTED_DDS,
                CcPaymentSectionButton.CHANGE_BANK_DETAILS, cpt.getContract(), new HashMap<String, Object>() {{
                    put("rejectedDd", m.get("rejectedDd"));
                }}));
        changeYourBankDetails.setButtonType(ActionButton.ButtonType.SECONDARY);
        buttons.add(changeYourBankDetails);

        paymentSectionInfo.setActionButtons(buttons);
    }

    // Incomplete DD flow section
    private void incompleteDdFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            ContractPaymentTerm cpt,
            ActionButton changeYourBankDetails,
            ActionButton viewYourPaymentHistory) {

        List<String> l = Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .getMissingBankInfo(cpt);
        l.remove("Signatures");

        String buttonText;
        CcAppCmsTemplate templateName;
        Map<String, String> parameters = new HashMap<>();

        boolean missingBankInfoRunning = flowProcessorService.existsRunningFlow(
                cpt.getContract(), FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);
        boolean rejectDataEntryRunning = cpt.getIsIBANRejected() ||
                cpt.getIsEidRejected() ||
                cpt.getIsAccountHolderRejected();

        if (missingBankInfoRunning && !rejectDataEntryRunning) {
            templateName = CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_MISSING_BANK_INFO_DEFAULT_MESSAGE;
            buttonText = "Add Your Bank Details";
        } else if (l.size() == 1) {
            templateName = CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_ONE_DOC_REJECTED_DEFAULT_MESSAGE;
            String missingInfo = getMissingBankInfoName(l.get(0));
            buttonText = "Add New " + (missingInfo.equals("name") ? "Account Name" : missingInfo) + " Photo";
            parameters.put("missing_ban_info", missingInfo);
        } else if (l.size() == 2) {
            templateName = CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_TWO_DOC_REJECTED_DEFAULT_MESSAGE;
            buttonText = "Add New Photos";
            parameters.put("missing_ban_info", getMissingBankInfoName(l.get(0)) + " and " +
                    getMissingBankInfoName(l.get(1)));
        } else {
            templateName = CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_THREE_DOC_REJECTED_DEFAULT_MESSAGE;
            buttonText = "Add Your Bank Details";
        }

        ActionButton signNowWebLink =  new ActionButton(buttonText,
                getSignDdWebLinkForPaymentSection(cpt, null,
                        !templateName.equals(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_THREE_DOC_REJECTED_DEFAULT_MESSAGE),
                        missingBankInfoRunning),
                isButtonEnabled(templateName, CcPaymentSectionButton.SIGN_NOW, cpt.getContract()));

        List<ActionButton> buttons = new ArrayList<>();
        buttons.add(signNowWebLink);

        String text = TemplateUtil.compileTemplate(TemplateUtil.getTemplate(templateName.toString()), cpt.getContract(), parameters);
        PaymentSectionBox box = new PaymentSectionBox(PaymentSectionBoxColor.RED.getLabel(), templateName.toString(), text, new ArrayList<>(),
                getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.RED));
        paymentSectionInfo.setBox(box);

        if (!templateName.equals(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_MISSING_BANK_INFO_DEFAULT_MESSAGE) &&
                !templateName.equals(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_THREE_DOC_REJECTED_DEFAULT_MESSAGE) &&
                isButtonEnabled(CcAppCmsTemplate.CC_PAYMENT_INCOMPLETE_DDS, CcPaymentSectionButton.CHANGE_BANK_DETAILS, cpt.getContract())) {
            changeYourBankDetails.setButtonType(ActionButton.ButtonType.SECONDARY);
            buttons.add(changeYourBankDetails);
        }

        if (templateName.equals(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_THREE_DOC_REJECTED_DEFAULT_MESSAGE) ||
                templateName.equals(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_MISSING_BANK_INFO_DEFAULT_MESSAGE)) {
            buttons.add(viewYourPaymentHistory);
        }

        paymentSectionInfo.setActionButtons(buttons);
    }

    private String getMissingBankInfoName(String s) {
        return s.equalsIgnoreCase("account name") ?
                "name" :
                s.equalsIgnoreCase("eid") ?
                        "Emirates ID" : "IBAN";
    }

    // Paying via credit card flow section
    private void payingViaCreditCardFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            ContractPaymentTerm cpt,
            ActionButton addAutomaticMonthlyBank,
            ActionButton viewYourPaymentHistory) {

        boolean enableCtas = isButtonEnabled(CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE,
                CcPaymentSectionButton.PAY_BY_CARD, cpt.getContract());


        String text = getTextAndFillCardSectionInfo(paymentSectionInfo, enableCtas, cpt);

        paymentSectionInfo.setBox(getBoxForPayingViaCCOrOMA(paymentSectionInfo, text, cpt.getContract().isOneMonthAgreement()));

        addAutomaticMonthlyBank.setEnabled(enableCtas);
        paymentSectionInfo.setActionButtons(Arrays.asList(addAutomaticMonthlyBank, viewYourPaymentHistory));
    }

    // IPAM flow section
    private void iPAMFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            Contract contract,
            ActionButton addAutomaticMonthlyBank,
            ActionButton viewYourPaymentHistory,
            Map<String, Object> m) {

        FlowProcessorEntity f = (FlowProcessorEntity) m.get("ipamFlow");

        boolean enableCTAs = isButtonEnabled(CcAppCmsTemplate.PAYMENT_SECTION_INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD_DEFAULT_MESSAGE,
                CcPaymentSectionButton.ADD_AUTOMATIC_MONTHLY_BANK, contract,
                new HashMap<String, Object>(){{
                    put("flowStartDate", ((FlowProcessorEntity) m.get("ipamFlow")).getCreationDate());
                }});
        addAutomaticMonthlyBank.setEnabled(enableCTAs);

        String text = getTextByFlowProcessorEntity(f, CcAppCmsTemplate.PAYMENT_SECTION_INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD_DEFAULT_MESSAGE.toString(), enableCTAs);

        String[] array = text.split("->");
        PaymentSectionBox box = new PaymentSectionBox(PaymentSectionBoxColor.ORANGE.getLabel(),
                CcAppCmsTemplate.PAYMENT_SECTION_INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD_DEFAULT_MESSAGE.toString(), array[0], new ArrayList<>(),
                getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.ORANGE), array.length > 1 ? array[1] : "");
        paymentSectionInfo.setBox(box);

        paymentSectionInfo.setActionButtons(Arrays.asList(addAutomaticMonthlyBank, viewYourPaymentHistory));
    }

    // OMA flow section
    private void oneMonthAgreementFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            ContractPaymentTerm cpt,
            ActionButton addAutomaticMonthlyBank,
            ActionButton viewYourPaymentHistory,
            Map<String, Object> m) {

        boolean enableCTAs = isButtonEnabled(CcAppCmsTemplate.PAYMENT_SECTION_ONE_MONTH_AGREEMENT_DEFAULT_MESSAGE,
                CcPaymentSectionButton.ADD_AUTOMATIC_MONTHLY_BANK, cpt.getContract(),
                new HashMap<String, Object>(){{
                    put("flowStartDate", m.get("flowStartDate"));
                }});
        addAutomaticMonthlyBank.setEnabled(enableCTAs);

        String text = getTextAndFillCardSectionInfo(paymentSectionInfo, enableCTAs, cpt);

        paymentSectionInfo.setBox(getBoxForPayingViaCCOrOMA(paymentSectionInfo, text, cpt.getContract().isOneMonthAgreement()));

        paymentSectionInfo.setActionButtons(Arrays.asList(addAutomaticMonthlyBank, viewYourPaymentHistory));
    }

    // Online reminder flow
    private void onlineReminderFlowInfo(
            PaymentSectionInfo paymentSectionInfo,
            ContractPaymentTerm cpt,
            Map<String, Object> bankDetails,
            FlowProcessorEntity f,
            ActionButton changeYourBankDetails,
            ActionButton viewYourPaymentHistory) {
        String templateName = directDebitService.cptHasConfirmedDd(cpt) ?
                CcAppCmsTemplate.PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_HAS_CONFIRMED_DDS_DEFAULT_MESSAGE.toString() :
                CcAppCmsTemplate.PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_WITH_NO_CONFIRMED_DDS_DEFAULT_MESSAGE.toString();

        String text = getTextByFlowProcessorEntity(f, templateName, true);

        String[] array = text.split("->");
        PaymentSectionBox box = new PaymentSectionBox(PaymentSectionBoxColor.ORANGE.getLabel(),
                templateName,
                array[0], new ArrayList<>(),
                getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.ORANGE),
                array.length > 1 ? array[1] : "");
        paymentSectionInfo.setBox(box);


        List<ActionButton> buttons = new ArrayList<>();
        if (templateName.equals(CcAppCmsTemplate.PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_HAS_CONFIRMED_DDS_DEFAULT_MESSAGE.toString())) {
            buttons.add(changeYourBankDetails);
            changeYourBankDetails.setEnabled(isButtonEnabled(
                    CcAppCmsTemplate.PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_HAS_CONFIRMED_DDS_DEFAULT_MESSAGE,
                    CcPaymentSectionButton.CHANGE_BANK_DETAILS, cpt.getContract()));
            paymentSectionInfo.setBankDetails(bankDetails);
        }

        buttons.add(viewYourPaymentHistory);

        paymentSectionInfo.setActionButtons(buttons);
    }

    private String getTextByFlowProcessorEntity(FlowProcessorEntity f, String templateName, boolean enableCTAs) {
        ContractPaymentConfirmationToDo todo = f.getContractPaymentConfirmationToDo();
        logger.info("todo id: " + (todo == null ? "NULL" : todo.getId()));

        Template t = Setup.getRepository(TemplateRepository.class)
                .findByNameIgnoreCase(templateName);

        LocalDate paidEndDate = new LocalDate(f.getContract().getPaidEndDate());
        Map<String, String> parameters = new HashMap<String, String>() {{
            put("pay_link", "");
            put("amount", "");
            put("paid_end_date", paidEndDate.toString("dd MMM, yyyy"));
            put("paid_end_date - 1", paidEndDate.minusDays(1).toString("dd MMM, yyyy"));
        }};
        if (todo == null || todo.isShowOnERP() || todo.isDisabled() || !enableCTAs) {
            return TemplateUtil.compileTemplate(t, f.getContract(), parameters);
        } else {
            String link = "context://";

            HashMap<String, Object> action1 = new HashMap<>();
            action1.put("type", AppActionType.LINK);
            action1.put("text", "pay_by_credit_card");
            action1.put("functionType", FunctionType.NAVIGATE);
            action1.put("navigationType", NavigationType.INAPP);
            action1.put("appRouteName", "/my_monthly_payments_pay_with_cc");
            action1.put("appRouteArguments", new HashMap<String, Object>() {{
                put("amount", todo.getTotalAmount().intValue());
                put("todoUuid", todo.getUuid());
                put("contractId", f.getContract().getId().toString());
                put("contractUuid", f.getContract().getUuid());
            }});

            try {
                link += new ObjectMapper().writeValueAsString(
                        new HashMap<String, Object>() {{
                            put("action_1", action1);
                        }});
            } catch (JsonProcessingException ex) {
                Logger.getLogger(CCAppContentService.class.getName()).log(Level.SEVERE, null, ex);
                link = "";
            }
            String finalLink = link;
            parameters.put("pay_link", finalLink);
            parameters.put("amount", PaymentHelper.df.format(todo.getTotalAmount().intValue()));
            return TemplateUtil.compileTemplate(t, f.getContract(), parameters);
        }
    }

    // Confirmed DD
    private void confirmedDdInfo(
            PaymentSectionInfo paymentSectionInfo,
            Contract contract,
            Map<String, Object> bankDetails,
            ActionButton changeYourBankDetails,
            ActionButton viewYourPaymentHistory) {

        String text = TemplateUtil.compileTemplate(
                TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE.toString()),
                contract, new HashMap<>());
        PaymentSectionBox box = new PaymentSectionBox(PaymentSectionBoxColor.GREEN.getLabel(), CcAppCmsTemplate.PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE.toString(),
                text, new ArrayList<>(), getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.GREEN));
        paymentSectionInfo.setBox(box);

        paymentSectionInfo.setBankDetails(bankDetails);

        List<ActionButton> buttons = new ArrayList<>();
        if (isButtonEnabled(CcAppCmsTemplate.PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE,
                CcPaymentSectionButton.CHANGE_BANK_DETAILS, contract)) {
            buttons.add(changeYourBankDetails);
        }

        buttons.add(viewYourPaymentHistory);
        paymentSectionInfo.setActionButtons(buttons);
    }

    public String approvedDdsPaymentIntro(Contract contract) {
        logger.log(Level.INFO, "Contract id" + contract.getId());

        Map<String, String> paramValues = getNextPaymentInfo(contract);
        if (paramValues.isEmpty()) return "";

        Template template = TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE_INTRO.toString());
        return "<br/><br/>" + TemplateUtil.compileTemplate(template, null, paramValues);
    }

    private Map<String, String> getNextPaymentInfo(Contract contract) {
        Map<String, String> paramValues = new HashMap<>();

        List<Object[]> payments = paymentRepository.findUpcomingPaymentsByContract(contract.getId());

        if (payments.isEmpty()) {
            boolean isFirstMonth = new LocalDate(contract.getStartOfContract()).toString("yyyy-MM-dd")
                            .equals(new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd"));

            //Date nextPaymentDate = getUpcomingPaymentDate(contract).toDate();
            Date nextPaymentDate = new LocalDate(contract.getPaidEndDate())
                    .plusDays(isFirstMonth ? 0 : 1).toDate();
            ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
            CalculateDiscountsWithVatService calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);
            Double amount = isFirstMonth && contract.getIsProRated() ?
                    cpt.getFirstMonthPayment() :
                    calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, nextPaymentDate);

            if (isFirstMonth && contract.getProRatedPlusMonth()) {
                amount += calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, new LocalDate(nextPaymentDate).plusMonths(1).dayOfMonth().withMinimumValue().toDate());
            }

            List<ContractPaymentType> nonMonthlyTypes = cpt.getContractPaymentTypes() != null ?
                    cpt.getContractPaymentTypes().stream()
                            .filter(contractPaymentType ->
                                    !Arrays.asList(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                                                    AbstractPaymentTypeConfig.MONTHLY_PAYMENT_ADD_ON_TYPE_CODE)
                                            .contains(contractPaymentType.getType().getCode()))
                            .collect(Collectors.toList()) : new ArrayList<>();

            for (ContractPaymentType t : nonMonthlyTypes) {
                List<ContractPayment> cps = t.generateContractPayments(
                        new DateTime(contract.getStartOfContract()), new DateTime(nextPaymentDate).dayOfMonth().withMaximumValue());
                for (ContractPayment cp : cps) {
                    if (new LocalDate(cp.getDate()).toString("yyyy-MM-dd")
                            .equals(new LocalDate(nextPaymentDate).toString("yyyy-MM-dd"))) {
                        logger.info("t id: " + t.getId());
                        amount += cp.getAmount();
                    }
                }
            }

            paramValues.put("amount", PaymentHelper.df.format(amount.intValue()));
            paramValues.put("next_deduction_date", new LocalDate(nextPaymentDate).toString("dd MMM, yyyy"));
            return paramValues;
        }

        paramValues.put("amount", PaymentHelper.df.format(payments.get(0)[1]));
        paramValues.put("next_deduction_date", new LocalDate(payments.get(0)[0]).toString("dd MMM, yyyy"));

        return paramValues;
    }

    // Pending DD
    private void pendingDdInfo(PaymentSectionInfo paymentSectionInfo, Contract contract, ActionButton viewYourPaymentHistory) {

        String text = TemplateUtil.compileTemplate(
                TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_DD_PENDING_DEFAULT_MESSAGE.toString()),
                contract, new HashMap<>());
        PaymentSectionBox box = new PaymentSectionBox(PaymentSectionBoxColor.ORANGE.getLabel(), CcAppCmsTemplate.PAYMENT_SECTION_DD_PENDING_DEFAULT_MESSAGE.toString(),
                text, new ArrayList<>(), getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.ORANGE));

        paymentSectionInfo.setActionButtons(Collections.singletonList(viewYourPaymentHistory));
        paymentSectionInfo.setBox(box);
    }

    // No rules apply
    private void noRulesApply(PaymentSectionInfo paymentSectionInfo, Contract contract) {

        Map<String, String> parameters = getNextPaymentInfo(contract);
        String text = TemplateUtil.compileTemplate(
                TemplateUtil.getTemplate(parameters.isEmpty() ?
                        CcAppCmsTemplate.CC_PAYMENT_NO_RULES_APPLY.toString() :
                        CcAppCmsTemplate.PAYMENT_SECTION_NO_RULES_APPLY_DEFAULT_MESSAGE.toString()),
                contract, parameters);
        PaymentSectionBox b = new PaymentSectionBox(PaymentSectionBoxColor.ORANGE.getLabel(), parameters.isEmpty() ?
                CcAppCmsTemplate.CC_PAYMENT_NO_RULES_APPLY.toString() :
                CcAppCmsTemplate.PAYMENT_SECTION_NO_RULES_APPLY_DEFAULT_MESSAGE.toString(),
                text, new ArrayList<>(), getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.ORANGE));
        paymentSectionInfo.setBox(b);
    }

    // Receive Payment Notifications section
    private void receivePaymentNotificationsInfo(PaymentSectionInfo paymentSectionInfo, Contract contract) {

        String text = TemplateUtil.compileTemplate(
                TemplateUtil.getTemplate(CcAppCmsTemplate.PAYMENT_SECTION_RECEIVE_PAYMENT_NOTIFICATIONS_SUB_TITLE.toString()),
                contract, new HashMap<>());

        paymentSectionInfo.setReceivePaymentNotificationBox(new ReceivePaymentNotificationBox(
                contract.getReceivePaymentNotifications() != null &&
                        !contract.getReceivePaymentNotifications().equals(Contract.ReceivePaymentNotificationsStatus.DISABLED),
                CcAppCmsTemplate.PAYMENT_SECTION_RECEIVE_PAYMENT_NOTIFICATIONS_SUB_TITLE.toString(),
                text, "Receive Payment Notifications"));
    }

    private String getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon icon) {
        ResourceRepository resourceRepository = Setup.getRepository(ResourceRepository.class);
        Resource r = resourceRepository.findByCode(icon.getLabel());

        return r == null || r.getAttachments().isEmpty() ? "" : r.getAttachments().get(0).getUuid();
    }
    public boolean clientProvidedNewDetailsAfterDate(Contract c, Date d) {
        if (Setup.getRepository(GraphicDesignerTodoRepository.class)
                .existsByContractIdAndToDoTypeAndCreationDateGreaterThan(
                        c.getId(), d)) {
            logger.info("exits graphic designer created after flow start date -> exiting");
            return true;
        }

        return flowProcessorService.clientProvidesSignatureAndBankInfo(c, d);
    }

//    private LocalDate getUpcomingPaymentDate(Contract c) {
//        boolean firstMonth = new LocalDate(c.getStartOfContract()).toString("yyyy-MM-dd")
//                .equals(new LocalDate(c.getPaidEndDate()).toString("yyyy-MM-dd"));
//
//        if (firstMonth) {
//            return new LocalDate(c.getPaidEndDate());
//        }
//
//        if (c.isMaidCc()) {
//            return new LocalDate(c.getPaidEndDate()).plusDays(1);
//        }
//
//        DateTime lastPaymentReceived = paymentService.getLastReceivedMonthlyPaymentDate(c);
//
//
//        return lastPaymentReceived == null ?
//                new LocalDate(c.getStartOfContract()) :
//                new LocalDate(lastPaymentReceived.plusDays(1).toDate());
//    }


    // ACC-8019 For payingViaCreditCardFlowInfo Or oneMonthAgreementFlowInfo
    private String getTextAndFillCardSectionInfo(PaymentSectionInfo paymentSectionInfo, boolean enableCtas, ContractPaymentTerm cpt) {

        boolean isEligibleForTokenizationViaContract = ContractService.isEligibleForTokenizationViaContract(cpt.getContract());

        if (isEligibleForTokenizationViaContract) {
            paymentSectionInfo.setCardSectionState(PaymentSectionInfo.CardSectionState.ADD_NEW_CARD);
            Map<String, Object> cardInfo = cpt.getSourceInfo();
            if (contractPaymentConfirmationToDoService.isWaitingRecurringToken(cpt)) {
                paymentSectionInfo.setCardSectionState(PaymentSectionInfo.CardSectionState.WAITING_CARD_DETAILS);
            } else if (cpt.getSourceId() != null && !cardInfo.isEmpty()) {
                paymentSectionInfo.setCardSectionState(PaymentSectionInfo.CardSectionState.EDIT_YOUR_CARD_DETAILS);
                paymentSectionInfo.setCardSectionInfo(new HashMap<String, Object>(){{
                    put("cardHolderName", cardInfo.get("name"));
                    put("cardNumber", "****" + cardInfo.get("last4"));
                    put("cardType", cardInfo.get("scheme"));
                    put("expiryDate", ((LocalDate) cardInfo.get("expiryDate")).toString("MM/YY"));
                }});

            }
        }

        return getTextForPayingViaCCOrOMA(paymentSectionInfo, enableCtas, cpt,
                getToDoForPayingViaCCOrOMA(cpt, isEligibleForTokenizationViaContract));
    }

    private String getTextForPayingViaCCOrOMA(PaymentSectionInfo paymentSectionInfo, boolean enableCtas, ContractPaymentTerm cpt, ContractPaymentConfirmationToDo todo) {

        return Arrays.asList(PaymentSectionInfo.CardSectionState.EDIT_YOUR_CARD_DETAILS,
                        PaymentSectionInfo.CardSectionState.WAITING_CARD_DETAILS)
                .contains(paymentSectionInfo.getCardSectionState()) && todo == null ?
                clientPayingViaCreditCardHasToken(cpt) :
                clientPayingViaCreditCard(cpt.getContract(), enableCtas, todo);
    }

    public ContractPaymentConfirmationToDo getToDoForPayingViaCCOrOMA(ContractPaymentTerm cpt, boolean isEligibleForTokenizationViaContract) {

        DateTime d = paymentService.getLastReceivedMonthlyPaymentDate(cpt.getContract());

        d = d == null ?
                new DateTime(cpt.getContract().getStartOfContract()) :
                d.plusMonths(1);

        // ACC-5509
        ContractPaymentConfirmationToDo todo = null;
        if (!isEligibleForTokenizationViaContract || cpt.getSourceId() == null ||
                !paymentService.nextMonthRecurringAndMatchTokenAmount(cpt) ||
                flowProcessorService.existsRunningFlow(
                        cpt.getContract(),
                        FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        FlowProcessorService.recurringFailureFlowsWithExpiredCard)) {
            todo = clientPayingViaCreditCardService.createTodoIfNotExists(cpt, new LocalDate(d));
        } else if (flowProcessorService.existsRunningFlow(
                cpt.getContract(),
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                Arrays.asList(
                        FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA,
                        FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB))) {
            // if initial flow is running and the client add a card
            todo = getToDoForPayingViaCreditCard(cpt.getContract());
        }
        logger.info("ContractPaymentConfirmationToDo id: " + (todo == null ? "NULL" : todo.getId()));
        return todo;
    }

    private PaymentSectionBox getBoxForPayingViaCCOrOMA(PaymentSectionInfo paymentSectionInfo, String text, boolean isOneMonthAgreement) {

        if (paymentSectionInfo.getCardSectionState().equals(PaymentSectionInfo.CardSectionState.EDIT_YOUR_CARD_DETAILS)) {
            String[] array = text.split("->");
            return new PaymentSectionBox(
                    PaymentSectionBoxColor.GREEN.getLabel(),
                    CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_WITH_TOKEN_DEFAULT_MESSAGE.toString(),
                    array[0], new ArrayList<>(),
                    getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.GREEN),
                    array.length > 1 ? array[1] : "");
        } else {
            String[] array = text.split("->");
            return new PaymentSectionBox(
                    PaymentSectionBoxColor.ORANGE.getLabel(),
                    isOneMonthAgreement ?
                            CcAppCmsTemplate.PAYMENT_SECTION_ONE_MONTH_AGREEMENT_DEFAULT_MESSAGE.toString() :
                            CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE.toString(),
                    array[0], new ArrayList<>(),
                    getPaymentSectionBoxIconUuid(PaymentSectionBox.PaymentSectionBoxIcon.ORANGE),
                    array.length > 1 ? array[1] : "");
        }
    }

    public String getTemplateTypeForUpdateMaidMonthlyPaymentAndContractBody(Contract contract) {
        Map<String, String> parameters = new HashMap<>();
        logger.info("contract id: " + contract.getId());

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        CcAppCmsTemplate template = flowProcessorService.isPayingViaCreditCard(contract) ?
                CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_VIA_CC_BODY :
                CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_DD_BODY;

        Map<String, Object> m = Setup.getApplicationContext()
                .getBean(ContractService.class)
                .getWorkerSalaryAndDesignationByOecNationalitySetup(contract.getHousemaid().getNationality().getId());
        double workerSalary = (double) m.get("workerSalary");
        List<String> designation = (List<String>) m.get("designation");

        LocalDate startDiscountDate = new LocalDate(calculateDiscountsWithVatService.getDiscountStartDateInMillis(
                        contract.getStartOfContract(), contract.getIsProRated(), cpt));

        boolean matchedDesignation = designation.stream()
                .anyMatch(d -> d.equals(contract.getWorkerType().getCode()));

        parameters.put("nepali_total_salary",PaymentHelper.df.format(workerSalary));
        parameters.put("duration", String.valueOf(matchedDesignation ? 2 : 3));
        parameters.put("payment", PaymentHelper.df.format(DiscountsWithVatHelper.getAmountWithoutVat(
                cpt.getMonthlyPayment()) + workerSalary));
        parameters.put("her_his", contract.getHousemaid() != null && contract.getHousemaid().getGender() != null &&
                contract.getHousemaid().getGender().equals(Gender.Male) ? "his" : "her" );
        parameters.put("maid_first_name", getMaidFirstNameForTemplateMaidGoOnVacation(contract));
        parameters.put("next_month", new LocalDate().plusMonths(1).dayOfMonth()
                .withMinimumValue().isBefore(startDiscountDate) ?
                startDiscountDate.toString("MMMM") :
                new LocalDate().plusMonths(1).toString("MMMM"));
        parameters.put("changes", matchedDesignation ?
                "minimum salary" : "designation and minimum salary");

        return TemplateUtil.compileTemplate(template.toString(), contract, parameters);
    }

    public String getTemplateTypeForUpdateMaidMonthlyPaymentAndContractTitle(Contract contract) {
        Map<String, String> parameters = new HashMap<>();
        logger.info("contract id: " + contract.getId());

        CcAppCmsTemplate template = flowProcessorService.isPayingViaCreditCard(contract) ?
                CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_VIA_CC_TITLE :
                CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_DD_TITLE;

        parameters.put("maid_first_name", getMaidFirstNameForTemplateMaidGoOnVacation(contract));

        return TemplateUtil.compileTemplate(template.toString(), contract, parameters);
    }

    public String getMaidFirstNameForTemplateMaidGoOnVacation(Contract contract) {

        if(contract.getHousemaid() != null &&
                contract.getHousemaid().getFirstName() != null &&
                contract.getHousemaid().getName() != null &&
                !contract.getHousemaid().getName().toLowerCase().contains("unknown")) {

            return contract.getHousemaid().getFirstName();
        }

        switch (contract.getWorkerType().getCode().toLowerCase()) {
            case "private_driver":
                return "Your Driver";
            case "domestic_worker":
                return (contract.getHousemaid() != null && Gender.Male.equals(contract.getHousemaid().getGender())) ?
                        "Your Worker" : "Your Maid";
            case "cleaner":
                return "Your Maid";
            default:
                return "Your " + contract.getWorkerType().getName();
        }
    }
}