package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Service
public class UnpaidOnlineCreditCardPaymentService {
    private static final Logger logger = Logger.getLogger(UnpaidOnlineCreditCardPaymentService.class.getName());

    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private PaymentRepository paymentRepository;

    public void createPaymentReminderFlow(ContractPaymentConfirmationToDo toDo, ContractPaymentTerm cpt) {
        createPaymentReminderFlow(toDo, cpt, new Date());
    }

    public FlowProcessorEntity createPaymentReminderFlow(ContractPaymentConfirmationToDo toDo, ContractPaymentTerm cpt, Date executionDate) {
        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);

        FlowSubEventConfig flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.PENDING_PAYMENT, flowEventConfig);

        Map<String, Object> map = new HashMap<>();
        map.put("trials", 1);
        map.put("reminders", 0);
        map.put("lastExecutionDate", new DateTime(executionDate).withHourOfDay(10)
                .withMinuteOfHour(0)
                .withSecondOfMinute(0)
                .toDate());
        map.put("todo", toDo);
        FlowProcessorEntity f =
                flowProcessorService.createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);

        logger.log(Level.SEVERE, "flowProcessorEntity id: {0}", f == null ? "null" : f.getId());

        f = flowProcessorEntityRepository.findOne(f.getId());
        f.setContractPaymentConfirmationToDo(toDo);
        return flowProcessorEntityRepository.save(f);
    }

    public void reactivateReminderFlowAfterReActiveContract(ContractPaymentConfirmationToDo t) {
        logger.info("toDo id: " + t.getId());

        // filter payment received
        List<ContractPaymentWrapper> contractPaymentWrappers = t.getContractPaymentList()
                .stream()
                .filter(w -> !PaymentHelper.isMonthlyPayment(w.getPaymentType()) &&
                        !paymentRepository.existsByStatusAndContractAndDateOfPaymentAndTypeOfPayment(
                        PaymentStatus.RECEIVED, t.getContractPaymentTerm().getContract(), w.getPaymentDate(), w.getPaymentType()))
                .collect(Collectors.toList());
        logger.info("contractPaymentWrappers size: " + contractPaymentWrappers.size());

        // Disable ContractPaymentConfirmationToDo if it’s not
        if(!t.isDisabled()){
            t.setDisabled(true);
            Setup.getRepository(ContractPaymentConfirmationToDoRepository.class).saveAndFlush(t);
        }

        if(contractPaymentWrappers.isEmpty()) return;

        // remove conflict wrapper between confirmationTodo and matched confirmationTodo
        Setup.getApplicationContext()
        .getBean(ContractPaymentConfirmationToDoService.class)
        .getMatchedToDoViaWrappers(t)
        .forEach(matchedToDo -> matchedToDo.getContractPaymentList()
                .forEach(w -> {
                    logger.info("wrapper id: " + w.getId());
                    contractPaymentWrappers.removeIf(wrapper -> wrapper.getAmount().equals(w.getAmount()) &&
                            wrapper.getPaymentType().getCode().equals(w.getPaymentType().getCode()) &&
                            wrapper.getPaymentDate().getTime() >= new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate().getTime() &&
                            wrapper.getPaymentDate().getTime() <= new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate().getTime());
                }));

        if (contractPaymentWrappers.isEmpty()) return;

        logger.info("contractPaymentWrappers size: " + contractPaymentWrappers.size());

        // create confirmationTodo for payment
        createConfirmationTodoAndStartReminderFlow(contractPaymentWrappers, t.getContractPaymentTerm().isActive() ?
                t.getContractPaymentTerm() : t.getContractPaymentTerm().getContract().getActiveContractPaymentTerm());
    }

    private void createConfirmationTodoAndStartReminderFlow(
            List<ContractPaymentWrapper> contractPaymentWrappers, ContractPaymentTerm cpt) {

        // get ContractPayment form contractPaymentWrappers
        List<ContractPayment> contractPayments = contractPaymentWrappers.stream()
                .map(ContractPaymentWrapper::initContractPaymentProps)
                .collect(Collectors.toList());
        logger.info("ContractPayments size: " + contractPayments.size());

        // non-monthly Payments
        createConfirmationTodoFromContractPaymentsAndStartReminderFlow(contractPayments, cpt);
    }

    @Transactional
    public FlowProcessorEntity createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
            List<ContractPayment> l, ContractPaymentTerm cpt) {
        return createConfirmationTodoFromContractPaymentsAndStartReminderFlow(l, cpt, new Date());
    }

    @Transactional
    public FlowProcessorEntity createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
            List<ContractPayment> l, ContractPaymentTerm cpt, Date executionDate) {
        return createConfirmationTodoFromContractPaymentsAndStartReminderFlow(l, cpt, executionDate, new HashMap<>());
    }

    @Transactional
    public FlowProcessorEntity createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
            List<ContractPayment> l, ContractPaymentTerm cpt, Date executionDate, Map<String, Object> map) {
        if (l == null || l.isEmpty()) return null ;
        logger.info("cpt id: " + cpt.getId() + "; " + l.size());
        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .createConfirmationTodoFromContractPayments(
                        cpt, l, ContractPaymentConfirmationToDo.Source.PAYMENT_REMINDER, map);

        return createPaymentReminderFlow(todo, cpt, executionDate);
    }
}