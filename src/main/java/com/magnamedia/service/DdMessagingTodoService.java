package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.UaePhoneNormlizer;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.repository.DDMessagingToDoRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class DdMessagingTodoService {

    protected static final Logger logger = Logger.getLogger(DdMessagingTodoService.class.getName());
    @Autowired
    private DDMessagingToDoRepository ddMessagingToDoRepository;
    @Autowired
    private DDMessagingRepository ddMessagingRepository;

    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;

    public void createMessagingTodo(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            DDMessagingService.DdMessagingMethod type,
            Map<String, Object> parameters) {

        if (cpt.getContract() == null) return;

        Client client = cpt.getContract().getClient();
        Housemaid maid = cpt.getContract().getHousemaid();

        DDMessagingToDo ddMessagingToDo = new DDMessagingToDo();
        ddMessagingToDo.setType(type.toString());
        ddMessagingToDo.setSendToClient(ddMessagingContract.getSendToClient());
        ddMessagingToDo.setRejectCategory(ddMessagingContract.getDdMessaging().getRejectCategory());
        ddMessagingToDo.setSendToSpouse(ddMessagingContract.getSendToSpouse());
        ddMessagingToDo.setSendToMaid(ddMessagingContract.getSendToMaid());
        ddMessagingToDo.setSendToMaidWhenRetractCancellation(ddMessagingContract.getSendToMaidWhenRetractCancellation());
        ddMessagingToDo.setClientId(client.getId());
        ddMessagingToDo.setMaidId(maid != null ? maid.getId() : null);
        ddMessagingToDo.setAdjustedEndDate(cpt.getContract().getAdjustedEndDate());
        ddMessagingToDo.setContractUuid(cpt.getContract().getUuid());
        ddMessagingToDo.setAccountName(cpt.getAccountName());
        ddMessagingToDo.setEvent(ddMessagingContract.getDdMessaging().getEvent());
        ddMessagingToDo.setDdMessagingConfigId(ddMessagingContract.getDdMessaging().getId());
        ddMessagingToDo.setFlowProcessorEntityId(entity != null ? entity.getId() : null);
        ddMessagingToDo.setAmount(parameters.get("bounced_payment_amount") == null ? null :
                Double.valueOf(parameters.get("bounced_payment_amount").toString()));
        ddMessagingToDo.setPaymentId(parameters.get("bouncedPaymentId") == null ? null :
                Long.valueOf(parameters.get("bouncedPaymentId").toString()));
        ddMessagingToDo.setDirectDebitId(parameters.get("directDebitId") == null ? null :
                Long.valueOf(parameters.get("directDebitId").toString()));

        if (ddMessagingContract.getDdMessaging().getSendDate() != null)
            ddMessagingToDo.setSendDate(ddMessagingContract.getDdMessaging().getSendDate());

        if (parameters.get("scheduled_termination_date") != null) {
            ddMessagingToDo.setContractScheduleDateOfTermination(new LocalDateTime(parameters.get("scheduled_termination_date")).toDate());
        } else if (cpt.getContract().getScheduledDateOfTermination() != null) {
            ddMessagingToDo.setContractScheduleDateOfTermination(cpt.getContract().getScheduledDateOfTermination());
        } else if (parameters.get("scheduled_termination_date") != null) {
            ddMessagingToDo.setContractScheduleDateOfTermination(new LocalDateTime(parameters.get("scheduled_termination_date")).toDate());
        }

        switch(type) {
            case MESSAGE:
                ddMessagingToDo.setSendTime(ddMessagingContract.getDdMessaging().getSendTime());
                break;
            /*case HUMAN_SMS:
                ddMessagingToDo.setSendTime(ddMessagingContract.getDdMessaging().getHumanSmsTime());
                ddMessagingToDo.setHumanSmsDesc(ddMessagingContract.getDdMessaging().getHumanSmsDescription());
                ddMessagingToDo.setHumanSmsTitle(ddMessagingContract.getDdMessaging().getHumanSmsTitle());
                break;
            case EXPERT_TODO:
                ddMessagingToDo.setSendTime(ddMessagingContract.getDdMessaging().getHumanSmsTime());
                break;*/
        }

        if (ddMessagingContract.getSendToClient() || ddMessagingContract.getSendToSpouse()) {
            ddMessagingToDo.setClientName(client.getName());
            ddMessagingToDo.setSpouseName(client.getSpouseName());
            ddMessagingToDo.setClientPhoneNumber(client.getNormalizedMobileNumber());
            ddMessagingToDo.setSpousePhoneNumber(client.getNormalizedSpouseMobileNumber());
            ddMessagingToDo.setClientTemplateName(ddMessagingContract.getClientTemplate().getName());
        }

        if (ddMessagingContract.getSendToMaid()) {
            ddMessagingToDo.setMaidName(maid != null ? maid.getName() : "");
            ddMessagingToDo.setMaidFirstName(maid != null ? maid.getFirstName(): "");
            ddMessagingToDo.setMaidPhoneNumber(maid != null ? UaePhoneNormlizer.NormalizePhoneNumber(maid.getPhoneNumber()) : "");
            ddMessagingToDo.setMaidTemplateName(ddMessagingContract.getMaidTemplate().getName());
        }

        // ACC-2445
        if (ddMessagingContract.getSendToMaidWhenRetractCancellation()) {
            ddMessagingToDo.setMaidName(maid != null ? maid.getName() : "");
            ddMessagingToDo.setMaidFirstName(maid != null ? maid.getFirstName(): "");
            ddMessagingToDo.setMaidPhoneNumber(maid != null ? UaePhoneNormlizer.NormalizePhoneNumber(maid.getPhoneNumber()) : "");
            ddMessagingToDo.setMaidWhenRetractCancellationTemplateName(ddMessagingContract.getMaidWhenRetractCancellationTemplate().getName());
        }

        ddMessagingToDoRepository.save(ddMessagingToDo);
    }

    @Transactional
    public void processTodo(Long ddMessagingToDoId) {
        DDMessagingToDo ddMessagingToDo = ddMessagingToDoRepository.findOne(ddMessagingToDoId);
        logger.log(Level.SEVERE, "DirectDebitMessagesSendingJob ddMessagingList on: {0}", ddMessagingToDo.getId());

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        Contract contract = contractRepository.findByUuid(ddMessagingToDo.getContractUuid());
        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

        // ACC-7294
        if (!contract.getStatus().equals(ContractStatus.ACTIVE) &&
                ddMessagingToDo.getEvent().equals(DDMessagingType.ExpiryPayment)) {
            ddMessagingToDo.setActive(false);
            ddMessagingToDoRepository.save(ddMessagingToDo);
            return;
        }

        Map<String, String> parameters = new HashMap<>();
        if (ddMessagingToDo.getPaymentId() != null)
            parameters.put("bouncedPaymentId", ddMessagingToDo.getPaymentId().toString());
        if (ddMessagingToDo.getAmount() != null) {
            parameters.put("bounced_payment_amount", String.valueOf(ddMessagingToDo.getAmount().intValue()));
            parameters.put("latest_bounced_amount", parameters.get("bounced_payment_amount"));
        }

        if (ddMessagingToDo.getContractScheduleDateOfTermination() != null) {
            parameters.put("scheduled_termination_date",
                    DateUtil.formatClientFullDate(ddMessagingToDo.getContractScheduleDateOfTermination()));
            parameters.put("scheduled_termination_date - 1 day",
                    DateUtil.formatClientFullDate(new LocalDate(ddMessagingToDo.getContractScheduleDateOfTermination()).minusDays(1).toDate()));
        }

        DDMessagingContract ddMessagingContract = null;
        FlowProcessorEntity entity = null;

        if (ddMessagingToDo.getDdMessagingConfigId() != null) {
            DDMessaging ddMessaging = ddMessagingRepository.findOne(ddMessagingToDo.getDdMessagingConfigId());
            ddMessagingContract = Setup.getApplicationContext()
                            .getBean(DDMessagingService.class)
                                    .getDdMessagingContract(ddMessaging, contractPaymentTerm.getBank());

            if(ddMessagingToDo.getFlowProcessorEntityId() != null)
                entity = Setup.getRepository(FlowProcessorEntityRepository.class)
                        .findOne(ddMessagingToDo.getFlowProcessorEntityId());

            flowProcessorMessagingService.fillParameters(ddMessagingContract, entity, contractPaymentTerm, parameters);
        }

        // values not of DdMessagingMethod enum is for backward compatibility
        switch(ddMessagingToDo.getType()) {
            case "force_sms":
            case "FORCE_SMS":
                flowProcessorMessagingService.sendForceSms(ddMessagingToDo,
                        ddMessagingContract != null ? ddMessagingContract.getClientTemplate() :
                                TemplateUtil.getTemplate(ddMessagingToDo.getClientTemplateName()),
                        parameters,
                        contractPaymentTerm);
                break;
            case "sms":
            case "MESSAGE":
                logger.info("sending notification of: " + ddMessagingToDo.getId());

                if (ddMessagingToDo.getClientId() == null) logger.info("Client is NULL -> return");

                switch (ddMessagingToDo.getEvent()) {
                    case DirectDebitRejected:
                        if (ddMessagingToDo.getDirectDebitId() != null) {
                            parameters.put("ownerType", "DirectDebit");
                            parameters.put("ownerId", ddMessagingToDo.getDirectDebitId().toString());
                            break;
                        }
                }

                parameters.putIfAbsent("ownerType", "Contract");
                parameters.putIfAbsent("ownerId", contract.getId().toString());

                flowProcessorMessagingService.sendDdMessage(
                        ddMessagingContract, contractPaymentTerm, parameters, new HashMap<>(), ddMessagingToDo);
                break;
            /*case "human_sms":
            case "HUMAN_SMS":
                flowProcessorMessagingService.createDdMessagingHumanSms(
                        ddMessagingContract.getDdMessaging(), contractPaymentTerm, parameters);
                break;
            case "expert_todo":
            case "EXPERT_TODO":
                flowProcessorMessagingService.createDdMessagingExpertTodo(
                        ddMessagingContract.getDdMessaging(), contractPaymentTerm);
                break;*/
        }

        ddMessagingToDo.setSent(true);
        ddMessagingToDoRepository.save(ddMessagingToDo);
    }
}