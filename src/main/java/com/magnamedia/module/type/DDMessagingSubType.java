package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

public enum DDMessagingSubType implements LabelValueEnum {

    NO_SIGNATURE("No Signature"),

    PENDING_PAYMENT("Pending Payment"),

    INITIAL_FLOW_FOR_DDA("Initial Flow For DDA"),
    INITIAL_FLOW_FOR_DDB("Initial Flow For DDB"),
    MONTHLY_REMINDER("Monthly Reminder"),
    DD_Rejection("DD Rejection"),
    DD_SIGNING_OFFER("DD Signing Offer"),
    // ACC-6840
    INSUFFICIENT_FUNDS("Insufficient funds"),
    EXCEEDING_DAILY_LIMITS("Exceeding Daily Limits"),
    EXPIRED_CARD("Expired Card"),
    ACCOUNT_ISSUE("Account Issue"),
    OTHER_ISSUES("Other Issues"),
    TOKEN_DELETED("Token Deleted"),

    MISSING_BANK_INFO("Missing Bank Info"),
    GENERATE_DDS_AFTER_PAID_END_DATE("Generate DDs After Paid End Date"),
    PAYMENT_REMINDER_EXTENSION("Payment Reminder"),

    // DEPRECATED
    NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY("Missing Signature With One Payment Only", true),
    PAYMENT_REMINDER("Payment Reminder", true),
    SIGNING_OFFER_AND_CC_PAYMENT_ALLOWED("Signing offer And CC Payment Allowed", true),
    SIGNING_OFFER_AND_CC_PAYMENT_IS_NOT_ALLOWED("Signing Offer And CC Payment Is Not Allowed", true);


    private final String label;
    private final boolean deprecated;

    DDMessagingSubType(String label) {
        this.deprecated = false;
        this.label = label;
    }

    DDMessagingSubType(String label, boolean deprecated) {
        this.deprecated = deprecated;
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }

    public boolean isDeprecated() { return deprecated; }
}


