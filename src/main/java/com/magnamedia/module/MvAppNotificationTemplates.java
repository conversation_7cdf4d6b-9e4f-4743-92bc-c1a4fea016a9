package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.MvSmsTemplateCode;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.service.AccountingTemplateService;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class MvAppNotificationTemplates {

   private static AccountingTemplateService getAccountingTemplateService() {
      return Setup.getApplicationContext()
              .getBean(AccountingTemplateService.class);
   }

    public static void createCCAppNotifications(){
       mvPaymentExpiry411Notification();
       mvPaymentExpiry412Notification();
       mvAccountingNotOwedMoneyFromClient812Notification();
       mvAccountingWronglyChargedMoneyOnClient813Notification();

       createDirectDebitGenerationPlanInsurance();
       createDirectDebitGenerationPlanSdr();
       createDirectDebitGenerationPlanOtherDdType();
       createDdPendingInfo();
       createPaytabsThankyouMessage();

//       createCardPaymentsTemplates();

       clientPayingViaCreditCardReplacementSuccessWithPayLink();
       clientPayingViaCreditCardReplacementSuccess();

       // ACC-5663
       refundRejected();
       conditionalRefundRequested();
       nonConditionalRefundRequested();

       // ACC-7611
       refundRejectedCreditCard();
       conditionalRefundRequestedCreditCard();
       nonConditionalRefundRequestedCreditCard();

       CreateRefundSalaryAmountEVisaNotIssuedOrIssuedInThePreviousMonth();

       createForACC6587();

       createForAcc8796();

       createMAIDVISA_J2_8_20();
    }

   private static void mvPaymentExpiry411Notification() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString());
      template.setDescription("In case of an unused signature, the message will be sent to the client");
      template.setText("ACTION REQUIRED: The Monthly Bank Payment Form that you’ve previously signed expires soon. " +
          "To avoid interruption in your service, we'll amend and resubmit your Bank Payment Forms to the bank. Please " +
          "remember, you can stop the service and cancel your contract anytime you want. If you have any questions, " +
          "please @chat_with_us@ .");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setNotificationAlwaysSendSms(false);
      template.setNotificationHoursBeforeSendSms(null);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   private static void mvPaymentExpiry412Notification() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString());
      template.setDescription("In case of an unused signature, the message will be sent to the client");
      template.setText("@greetings@ The Monthly Bank Payment Form that you’ve previously signed expires on " +
          "@ExpiryDate@. To avoid interruption in your service, please @link_send_dd_details_click_here@ to sign the new payment form.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setSendSMSIfNotReceived(false);
      template.setNotificationHoursBeforeSendSms(null);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   private static void mvAccountingNotOwedMoneyFromClient812Notification() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString());
      template.setDescription("In case of an unused signature, the message will be sent to the client");
      template.setText("Unfortunately, the bank didn't process the cancellation of your future payments on time. " +
              "You were charged AED @amount@. Don't worry; we’ll send you that amount within the next 7 days.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setNotificationAlwaysSendSms(true);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   private static void mvAccountingWronglyChargedMoneyOnClient813Notification() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString());
      template.setDescription("send to the Client If we receive an amount from the client when he doesn't owe us any amount");
      template.setText("You were charged AED @amount@ today. But don’t worry, we’ll transfer you the same amount by @scheduled_termination_date@.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setNotificationAlwaysSendSms(true);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   // ACC-5214
   private static void createDirectDebitGenerationPlanInsurance() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION.toString());
      template.setDescription("In case of a postpone direct debit generate with type insurance, the message will be sent to the client before five days of send date");
      template.setText("Thank you for using our services. It’s time to renew your " +
          "maid’s insurance. Your maid’s insurance will be automatically renewed and you’ll be charged AED " +
          "@insurance_DD_amount@ on @payment_date@. Please ensure sufficient funds are available in your account to " +
          "avoid penalties and service interruption. Thank you.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setSendSMSIfNotReceived(false);
      template.setNotificationHoursBeforeSendSms(null);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   // ACC-5214
   private static void createDirectDebitGenerationPlanSdr() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION.toString());
      template.setDescription("In case of a postpone direct debit generate with type Same day recruitment fee, the message will be sent to the client before five days of send date");
      template.setText("Thank you for using our services. It’s time to renew your maid’s " +
          "visa. Your maid’s visa will be automatically renewed and you’ll be charged AED " +
          "@SDR_DD_Amount@ on @SDR_Payment_date@. Please ensure sufficient funds are available in your " +
          "account to avoid service interruption. Thank you.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setSendSMSIfNotReceived(false);
      template.setNotificationHoursBeforeSendSms(null);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   // ACC-5214
   private static void createDirectDebitGenerationPlanOtherDdType() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION.toString());
      template.setDescription("In case of a postpone direct debit generate with other types, the message will be sent to the client before five days of send date");
      template.setText("Thank you for using our services. Please note that we’re going to " +
          "deduct AED @payment_amount@ from your account on @payment_date@ for @DD_TYPE@ fee. Please ensure " +
          "sufficient funds are available in your account to avoid penalties and service interruption. Thank you.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setSendSMSIfNotReceived(false);
      template.setNotificationHoursBeforeSendSms(null);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_SMS.toString());
      template.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }

   // ACC-5214
   private static void createDdPendingInfo() {

      Template template = new Template();
      template.setName(MvNotificationTemplateCode.MV_DD_PENDING_INFO_NOTIFICATION.toString());
      template.setDescription("DD status is Pending and sub-status is Pending to Receive DD Info/Signature from Client, At 10 AM");
      template.setText("ACTION REQUIRED: Please @link_send_dd_details_click_here@, and complete your Bank Payment Form.");
      template.setNotificationLocation(NotificationLocation.HOME);
      template.setNotificationHoursBeforeSendSms(2);
      template.setNotificationAlwaysSendSms(false);
      template.setSendSMSIfNotReceived(true);
      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_DD_PENDING_INFO_SMS.toString());
      template.setNotificationCanClosedByUser(false);
      TemplateUtil.createTemplate(template, new HashMap<>());
   }
   // ACC-5214
   private static void createPaytabsThankyouMessage() {

      Template templateMv = new Template();
      templateMv.setName(MvNotificationTemplateCode.MV_PAYTABS_THANKS_MESSAGE_NOTIFICATION.toString());
      templateMv.setDescription("Thank you message when a client pays online second cash payment");
      templateMv.setText("Thank you for settling your payment for your maid's service. We just want to " +
          "remind you that we can't accept Credit or Debit Card payments anymore. Please click on \"Sign Now\" " +
          "to complete your Monthly Bank Payment Form so we can deduct your future payments.");
      templateMv.setNotificationLocation(NotificationLocation.HOME);
      templateMv.setSendSMSIfNotReceived(false);
      templateMv.setNotificationHoursBeforeSendSms(0);
      templateMv.setNotificationAlwaysSendSms(false);
      templateMv.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_PAYTABS_THANKS_MESSAGE_SMS.toString());
      templateMv.setNotificationCanClosedByUser(true);
      TemplateUtil.createTemplate(templateMv, new HashMap<>());
   }

   // ACC-4591
//   private static void createCardPaymentsTemplates() {
//      Template template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_ACCOMMODATION_FEE_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay Accommodation Fee");
//      template.setText("Dear @client_name@, Thank you for using maids.cc. " +
//          "To pay your maid’s accommodation fee of AED @amount@ by Credit card, " +
//          "please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_ACCOMMODATION_FEE_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_CC_TO_MV_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay CC to MV");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To hire your maid under the new payment plan and to pay the transfer fee of AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_CC_TO_MV_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_MONTHLY_PAYMENT_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay Monthly Payment");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To pay your monthly payment of AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_MONTHLY_PAYMENT_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_OVERSTAY_FEES_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay Overstay fees");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To pay your maid's overstay fines of AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_OVERSTAY_FEES_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_PCR_TEST_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay Overstay fees");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To pay your maid's PCR fees of AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_PCR_TEST_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_URGENT_VISA_CHARGES_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay Urgent Visa Charges");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To pay your maid's urgent visa fees of AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_URGENT_VISA_CHARGES_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_INSURANCE_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay Urgent Insurance");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To pay your maid's insurance fees of AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_INSURANCE_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//
//      template = new Template();
//      template.setName(MvNotificationTemplateCode.MV_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_NOTIFICATION.toString());
//      template.setDescription("Send Notification to client for pay payments types");
//      template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//          " To pay AED @amount@ by Credit card," +
//          " please click on button below:");
//      template.setNotificationLocation(NotificationLocation.HOME);
//      template.setSendSMSIfNotReceived(true);
//      template.setNotificationHoursBeforeSendSms(2);
//      template.setNotificationSmsTemplateName(MvSmsTemplateCode.MV_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_SMS.toString());
//      template.setNotificationCanClosedByUser(true);
//      TemplateUtil.createTemplate(template, new HashMap<>());
//   }

   private static void clientPayingViaCreditCardReplacementSuccessWithPayLink() {

      CCAppNotificationTemplates.createNewModelTemplate(MvNotificationTemplateCode.MV_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_WITH_PAY_LINK_NOTIFICATION.toString(),
              "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                      "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 more month, " +
                      "please settle the payment today by clicking on the following link. @link@",
              "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                      "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 more month, " +
                      "please settle the payment today by clicking on the following link: @paying_via_credit_card_sms@",
              PicklistHelper.getItem("template_message_delay", "no_sms"));
   }

   private static void clientPayingViaCreditCardReplacementSuccess() {

      CCAppNotificationTemplates.createNewModelTemplate(MvNotificationTemplateCode.MV_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_NOTIFICATION.toString(),
              "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                      "Your payment covers your maid's service until @paid_end_date@",
              "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                      "Your payment covers your maid's service until @paid_end_date@" +
                      "Thank you for your understanding.",
              PicklistHelper.getItem("template_message_delay", "no_sms"));
   }
   private static void refundRejected() {

      ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
      nChannel.setType(ChannelSpecificSettingType.Notification);
      nChannel.setActive(true);
      nChannel.setText("We’re sorry but after carefully reviewing your case, " +
              "we’ve declined the refund request. If you think this is a mistake, please @whatsApp_us@.");
      nChannel.setNotificationCanClosedByUser(true);
      nChannel.setControls(new ArrayList<>());
      nChannel.setMessageDelayType(Setup.getItem("template_message_delay", "x_hours_after_notification"));
      nChannel.setMessageDelayHours(2);
      nChannel.setShowOnHomePage(true);
      nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

      new Template.TemplateBuilder()
                .Template(MvNotificationTemplateCode.MV_ACCOUNTING_REFUND_REJECTED_NOTIFICATION.toString(),"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .smsChannelSetting("We’re sorry but after carefully reviewing your case, we’ve declined the refund request. " +
                                "If you think this is a mistake, please WhatsApp us @whatsApp_us_sms@", new HashMap<>())
                .channelSetting(nChannel)
                .notificationLocation(NotificationLocation.HOME)
                .priority("priority_2")
                .target(PicklistHelper.getItem("template_target", "Clients"))
                .method("Push Notification")
                .newModel()
                .build();
   }

   private static void conditionalRefundRequested() {

      ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
      nChannel.setType(ChannelSpecificSettingType.Notification);
      nChannel.setActive(true);
      nChannel.setText("You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                              "we’ll refund the amount to your bank account within 2 business days.");
      nChannel.setNotificationCanClosedByUser(true);
      nChannel.setControls(new ArrayList<>());
      nChannel.setMessageDelayType(Setup.getItem("template_message_delay", "specific_time"));
      nChannel.setMessageDelaySpecificDate(Setup.getItem("template_message_delay_specific_date", "@nextMorning@"));
      nChannel.setMessageDelaySpecificTime(new DateTime().withTime(10, 0 ,0, 0).toDate());
      nChannel.setShowOnHomePage(true);
      nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

      new Template.TemplateBuilder()
             .Template(MvNotificationTemplateCode.MV_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString(),"", "")
             .showInReceiverLog(true)
             .unicode(true)
             .smsChannelSetting(
                     "You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                             "we’ll refund the amount to your bank account within 2 business days.", new HashMap<>())
             .channelSetting(nChannel)
             .notificationLocation(NotificationLocation.HOME)
             .priority("priority_3")
             .target(PicklistHelper.getItem("template_target", "Clients"))
             .method("Push Notification")
             .newModel()
             .build();
   }

   private static void nonConditionalRefundRequested() {

      ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
      nChannel.setType(ChannelSpecificSettingType.Notification);
      nChannel.setActive(true);
      nChannel.setText("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to " +
              "your bank account in the next 7 business days.");
      nChannel.setNotificationCanClosedByUser(true);
      nChannel.setControls(new ArrayList<>());
      nChannel.setMessageDelayType(Setup.getItem("template_message_delay", "specific_time"));
      nChannel.setMessageDelaySpecificDate(Setup.getItem("template_message_delay_specific_date", "@nextMorning@"));
      nChannel.setMessageDelaySpecificTime(new DateTime().withTime(10, 0 ,0, 0).toDate());
      nChannel.setShowOnHomePage(true);
      nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

      new Template.TemplateBuilder()
             .Template(MvNotificationTemplateCode.MV_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString(),"", "")
             .showInReceiverLog(true)
             .unicode(true)
             .smsChannelSetting(
                     "We just sent you AED @amount@ by bank transfer. Please expect to receive " +
                             "the amount to your bank account in the next 7 business days.", new HashMap<>())
             .channelSetting(nChannel)
             .notificationLocation(NotificationLocation.HOME)
             .priority("priority_3")
             .target(PicklistHelper.getItem("template_target", "Clients"))
             .method("Push Notification")
             .newModel()
             .build();
   }

   private static void refundRejectedCreditCard() {

      getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_ACCOUNTING_REFUND_REJECTED_CREDIT_CARD_NOTIFICATION.toString(),
              "We’re sorry but after carefully reviewing your case, " +
                      "we’ve declined the refund request. If you think this is a mistake, please @whatsApp_us@.",
              "We’re sorry but after carefully reviewing your case, we’ve declined the refund request. " +
                      "If you think this is a mistake, please WhatsApp us @whatsApp_us_sms@",
              2,
              new HashMap<>()
      );
   }

   private static void conditionalRefundRequestedCreditCard() {

      getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString(),
              "You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                      "we’ll refund the amount to your bank account within 2 business days.",
              "You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                      "we’ll refund the amount to your bank account within 2 business days.",
              3,
              new HashMap<>()
      );
   }

   private static void nonConditionalRefundRequestedCreditCard() {

      getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString(),
              "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount " +
                      "to your bank account in the next 7 business days.",
              "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount " +
                      "to your bank account in the next 7 business days.",
              3,
              new HashMap<>()
      );
   }

   private static void CreateRefundSalaryAmountEVisaNotIssuedOrIssuedInThePreviousMonth() {

      Map<String, String> expressionParameters = new HashMap<>();
      expressionParameters.put("worker_type", "#root == null ? " +
              "\"maid\" : " +
              "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                  "workerType != null ? " +
                     "workerType.getCode().equals(\"private_driver\") ? " +
                        "\"driver\" : " +
                        "\"maid\" : " +
                  "\"maid\" : " +
              "\"maid\"");
      expressionParameters.put("her_his", "#root == null ? " +
              "\"her\" : " +
              "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                  "housemaid == null ? " +
                     "\"her\" : " +
                     "housemaid.getGender() != null ? " +
                        "housemaid.getGender().equals(T(com.magnamedia.extra.Gender).Female) ? " +
                        "\"her\" : " +
                     "\"his\" : " +
                  "\"her\" : " +
              "\"her\"");

      getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION.toString(),
              "@greetings@\n" +
                      "Since your @worker_type@'s visa is not issued yet, we will refund the @worker_type@'s " +
                      "salary to your bank account. If you would like to pay @her_his@ anything " +
                      "for @previous_month@, please pay @her_his@ in cash. Your @worker_type@ should begin " +
                      "receiving @her_his@ full salary directly from us starting next month. " +
                      "You can expect to receive your refund within the next 7 business days. " +
                      "Sorry for any inconvenience caused.",
              "@greetings@\n" +
                      "Since your @worker_type@'s visa is not issued yet, we will refund the @worker_type@'s " +
                      "salary to your bank account. Please would you be so kind as to pay @her_his@ salary in cash " +
                      "for @previous_month@? Your @worker_type@ should begin receiving @her_his@ full salary directly " +
                      "from us starting next month. Sorry for any inconvenience caused.",
              1,
              new HashMap<String, Object>() {{
                 put("expressionParameters", expressionParameters);
              }}
      );
   }

   private static void createForACC6587() {
      AccountingTemplateService accountingTemplateService = Setup.getApplicationContext()
              .getBean(AccountingTemplateService.class);

      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION.toString(),
              "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                      "@monthly_payment_scheduled_date@\n" +
                      "To manage everything related to your payments, please visit the 'My Payments' section in your app. You can directly access it now by clicking the button below: ",
              "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                      "@monthly_payment_scheduled_date@\n" +
                      "To manage everything related to your payments, please download your personalized maids.cc app, by clicking on the following link: " +
                      "@cc_app_download_url@",
              1);

      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString(),
              "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                      "@monthly_payment_scheduled_date@\n" +
                      "Because we don't want to bother you, this is the last time you will receive this notification automatically. " +
                      "To continue receiving payment notifications, please click the \"Enable Payment Notifications\" button below. " +
                      "You can always manage your payment notifications from the payment section in your app.",
              "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                      "@monthly_payment_scheduled_date@\n" +
                      "Because we don't want to bother you, this is the last time you will receive this notification automatically.\n" +
                      "To manage everything related to your payments, please download your personalized maids.cc app, by clicking on the following link: " +
                      "@cc_app_download_url@",
              1);

      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION.toString(),
              "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                      "@monthly_payment_scheduled_date@",
              "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                      "@monthly_payment_scheduled_date@",
              4);
   }


   private static void createForAcc8796() {
      AccountingTemplateService accountingTemplateService = Setup.getApplicationContext()
              .getBean(AccountingTemplateService.class);
      Map<String, Object> m = new HashMap<>();
      m.put("contractType", "maidvisa");

      // First failed medical check template DD
      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT.toString(),
              "Since your domestic worker’s visa has not been issued yet, " +
                      "we can’t pay @her_his@ salary on @month_plus_one@ 5. If you’d like to pay @her_his@ for @month@, " +
                      "you can pay @her_his@ in cash. Rest assured, we won’t deduct any payment from you on @month_plus_one@ 1. " +
                      "Instead, the amount we already deducted on @month@ 1 " +
                      "will be used to pay @her_his@ salary on @month_plus_two@ 5.\n" +
                      " As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us.",
              "Since your domestic worker’s visa has not been issued yet, " +
                      "we can’t pay @her_his@ salary on @month_plus_one@ 5. If you’d like to pay @her_his@ for @month@, " +
                      "you can pay @her_his@ in cash. Rest assured, we won’t deduct any payment from you on @month_plus_one@ 1. " +
                      "Instead, the amount we already deducted on @month@ 1 " +
                      "will be used to pay @her_his@ salary on @month_plus_two@ 5.\n" +
                      " As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@ .",
              2,
              m);

      // First failed medical check template CC
      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_CREDIT_CARD_CONTRACT.toString(),
              "Since your domestic worker’s visa has not been issued yet, " +
                      "we can’t pay @her_his@ salary on @month_plus_one@ 5. If you’d like to pay @her_his@ for @month@, " +
                      "you can pay @her_his@ in cash. Rest assured, we will not be sending you the payment link " +
                      "for next month. Instead, the amount you already paid for @month@ will be used to pay @her_his@ " +
                      "salary on @month_plus_two@ 5.\n" +
                      " As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us.",
              "Since your domestic worker’s visa has not been issued yet, " +
                      "we can’t pay @her_his@ salary on @month_plus_one@ 5. If you’d like to pay @her_his@ for @month@, " +
                      "you can pay @her_his@ in cash. Rest assured, we will not be sending you the payment link " +
                      "for next month. Instead, the amount you already paid for @month@ will be used to pay @her_his@ " +
                      "salary on @month_plus_two@ 5.\n" +
                      " As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@ .",
              2,
              m);

      // Second failed medical check template DD
      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT.toString(),
              "Hello from maids.cc \n" +
                      "Since your domestic worker’s visa has not been issued yet, we can’t pay @her_his@ salary on @month@ 5. " +
                      "Therefore, once your payment of this month is received, we will make sure to refund you the amount deducted. " +
                      "If you’d like to pay @her_his@ for @month_minus_one@, you can pay @her_his@ in cash. " +
                      "Rest assured, the amount we already deducted from you on @first_pre_collected_month@ 1 will be used to pay @her_his@ salary on @month_plus_one@ 5.\n" +
                      "As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@.",
              "Hello from maids.cc \n" +
                      "Since your domestic worker’s visa has not been issued yet, we can’t pay @her_his@ salary on @month@ 5. " +
                      "Therefore, once your payment of this month is received, we will make sure to refund you the amount deducted. " +
                      "If you’d like to pay @her_his@ for @month_minus_one@, you can pay @her_his@ in cash. " +
                      "Rest assured, the amount we already deducted from you on @first_pre_collected_month@ 1 will be used to pay @her_his@ salary on @month_plus_one@ 5.\n" +
                      "As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@.",
              2,
              m);

      // Second failed medical check template recurring
      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_RECURRING_CONTRACT.toString(),
              "Since your domestic worker’s visa has not been issued yet, we can’t pay @her_his@ salary on @month_plus_one@ 5. " +
                           "If you’d like to pay @her_his@ for @month@, you can pay @her_his@ in cash. Rest assured, " +
                           "we won’t deduct any payment from you on @month_plus_one@ 1. Instead, the amount we already deducted on " +
                           "@first_pre_collected_month@ 1 will be used to pay @her_his@ salary on @month_plus_two@ 5. \n" +
                           "As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@ .",
              "Since your domestic worker’s visa has not been issued yet, we can’t pay @her_his@ salary on @month_plus_one@ 5. " +
                      "If you’d like to pay @her_his@ for @month@, you can pay @her_his@ in cash. Rest assured, " +
                      "we won’t deduct any payment from you on @month_plus_one@ 1. Instead, the amount we already deducted on " +
                      "@first_pre_collected_month@ 1 will be used to pay @her_his@ salary on @month_plus_two@ 5. \n" +
                      "As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@ .",
              2,
              m);

      // Second failed medical check template CC
      accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_CREDIT_CARD_CONTRACT.toString(),
              "Hello from maids.cc \n" +
                      "Since your domestic worker’s visa has not been issued yet, we can’t pay @her_his@salary on @month_plus_one@ 5. " +
                      "If you’d like to pay @her_his@ for @month@, you can pay @her_his@ in cash. " +
                      "Rest assured, we will not be sending you the payment link for next month. " +
                      "Instead, the amount you already paid on @first_pre_collected_month@ will be used to pay @her_his@ salary on @month_plus_two@ 5.\n" +
                      "As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us.",
              "Hello from maids.cc \n" +
                      "Since your domestic worker’s visa has not been issued yet, we can’t pay @her_his@salary on @month_plus_one@ 5. " +
                      "If you’d like to pay @her_his@ for @month@, you can pay @her_his@ in cash. " +
                      "Rest assured, we will not be sending you the payment link for next month. " +
                      "Instead, the amount you already paid on @first_pre_collected_month@ will be used to pay @her_his@ salary on @month_plus_two@ 5.\n" +
                      "As always, if you have any questions or concerns, please don’t hesitate to WhatsApp us @whatsapp_link@ .",
              2,
              m);
   }

   private static void createMAIDVISA_J2_8_20() {

      getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
              MvNotificationTemplateCode.MV_MAID_VISA_J2_8_20_NOTIFICATION.toString(),
              "Hello, we paid AED @hm_transactions_plus_ticket_and_administration_fees@ for @maid_first_name@'s flight ticket and " +
                      "visa processing until @she_he@ failed @her_his@ medical exam. " +
                      "Therefore, we'll refund you the remaining AED @remaining_amount@ from your initial payment.",
              "Hello, we paid AED @hm_transactions_plus_ticket_and_administration_fees@ for @maid_first_name@'s flight ticket and " +
                      "visa processing until @she_he@ failed @her_his@ medical exam. " +
                      "Therefore, we'll refund you the remaining AED @remaining_amount@ from your initial payment.",
              2);
   }
}
