package com.magnamedia.workflow.service.expensepayment;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Service
public class ExpensePaymentInPendingInvoiceStep extends ExpensePaymentAbstractStep<ExpensePayment> {

    public ExpensePaymentInPendingInvoiceStep() {
        this.setId(ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString());
    }

    @Override
    public void onDone(ExpensePayment entity) {
        entity.setDoneInPendingInvoiceStep(true);
        super.onDone(entity);
        if (isDoneFromCashierScreen(entity)) {
            updateStatusAndAddTaskInAccordingReconcilatorScreen(entity);
        } else { // done from reconcilator screen
            entity.setCompleted(Boolean.TRUE);

            Attachment invoice = entity.getAttachment(AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
            if (invoice == null) {
                entity.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
            } else {
                entity.setStatus(ExpensePaymentStatus.PAID);
            }
            entity.setConfirmed(Boolean.TRUE);
            entity.setMissingVatInvoice(ExpensePaymentInReconciliatorConfirmationStep.isMissingVatInvoice(entity));
            
            expensePaymentInReconciliatorConfirmationStep.confirmRequests(entity);
        }
    }

    private void updateStatusAndAddTaskInAccordingReconcilatorScreen(ExpensePayment entity) {
        if (ExpensePaymentInPendingPaymentCashierStep.isInvoiceMessingOrTaxInvoiceMessing(entity)) {
            entity.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
            addNewTask(entity, ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString());
        } else {
            entity.setStatus(ExpensePaymentStatus.PAID);
            addNewTask(entity, ExpensePaymentToDoType.TO_DO_IN_RECONCILIATOR_CONFIRMATION_SCREEN.toString());
        }
    }

    private boolean isDoneFromCashierScreen(ExpensePayment entity) {
        return entity.getConfirmed() == null || entity.getConfirmed().equals(Boolean.FALSE);
    }

    @Autowired
    ExpensePaymentInReconciliatorConfirmationStep expensePaymentInReconciliatorConfirmationStep;

    @Override
    public void onSave(ExpensePayment expensePayment) {
        expensePaymentInReconciliatorConfirmationStep.updateTransaction(expensePayment);
        super.onSave(expensePayment);
    }

}