package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitCancelationToDoRepository;
import com.magnamedia.service.DirectDebitService;
import com.magnamedia.service.JobExecutionService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 13 2019
 *         Jirra ACC-1135
 */
public class DDsCancellationScheduledJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(DDsCancellationScheduledJob.class.getName());
    private DirectDebitCancelationToDoController cancelationToDoController;
    private JobExecutionService jobExecutionService;

    public DDsCancellationScheduledJob() {
        cancelationToDoController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        jobExecutionService = Setup.getApplicationContext().getBean(JobExecutionService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.sendEmailsJob();
    }

    public void sendEmailsJob() {
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        logger.info("job started");
        if (!DirectDebitService.shouldRunDDsCancellationScheduledJob(false)) return;

        // Show all hidden "Cancel future DDs" todos
        Page<DirectDebitCancelationToDo> page;
        Integer pageIndex = 0;
        List<Long> processedIDs = new ArrayList();
        processedIDs.add(-1L);
        do {
            page = Setup.getRepository(DirectDebitCancelationToDoRepository.class)
                    .findByHiddenAndReasonNotInAndIdNotIn(true,
                            Arrays.asList(DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY,
                                    DirectDebitCancellationToDoReason.SWITCHING_TO_VAT),
                            processedIDs, PageRequest.of(pageIndex++, 100));

            List<DirectDebitCancelationToDo> hiddenToDos = page.getContent();
            hiddenToDos.forEach(toDo -> {
                try {
                    processedIDs.add(toDo.getId());
                    jobExecutionService.createBackGroundTask(DDsCancellationScheduledJob.class.getName(), "directDebitCancelationToDoController",
                            "showHiddenTodo", toDo);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Exception while Showing ToDo#" + toDo.getId());
                    logger.log(Level.SEVERE, "Exception: " + ExceptionUtils.getStackTrace(e));
                }
            });
        } while (page.hasNext());

        // get contract has been scheduled for termination after last run
        SelectQuery<Contract> query = new SelectQuery(Contract.class);
        query.filterBy("isScheduledForTermination", "=", true);
        query.filterBy("scheduledDateOfTermination", ">=", new LocalDate().toDate());
        query.filterBy("scheduledDateOfTermination", "<", new LocalDate().plusMonths(1).withDayOfMonth(1).toDate());

        Page<Contract> page2;
        pageIndex = 0;
        do {
            page2 = query.execute(PageRequest.of(pageIndex++, 100));
            List<Contract> contracts = page2.getContent();
            logger.info("contracts size:  " + contracts.size());

            if (contracts != null) {
                for (Contract old : contracts) {
                    logger.info("contract id: " + old.getId());
                    Contract contract = contractRepository.findOne(old.getId());
                    if (contract == null) continue;

                    try {
                        cancelationToDoController.createToDoIfValid(contract, null, DirectDebitCancellationToDoReason.COLLECTION_FLOW_EOM_JOB);
                    } catch (Exception e) {
                        EmailHelper.sendEmailByText(Setup.getParameter(Setup.getCurrentModule(),
                                        AccountingModule.PARAMETER_DDS_CANCELLATION_SCHEDULED_JOB_EXCEPTIONS_EMAILS),
                                "ddscancellationscheduledjob Contract id {" + contract.getId() + "} Exception", e.getMessage());
                    }
                }
            }
        } while (page2.hasNext());
    }
}