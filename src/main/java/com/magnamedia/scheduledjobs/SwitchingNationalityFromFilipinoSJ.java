package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.service.JobExecutionService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 06, 2021
 *         ACC-1689
 * 
 */

public class SwitchingNationalityFromFilipinoSJ implements MagnamediaJob {
    private static final Logger logger =
            Logger.getLogger(SwitchingNationalityFromFilipinoSJ.class.getName());

    private JobExecutionService jobExecutionService;
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;

    public SwitchingNationalityFromFilipinoSJ() {
        jobExecutionService = Setup.getApplicationContext().getBean(JobExecutionService.class);
        accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    private void runJob() {
        logger.info("Job Started");

        Page<AccountingEntityProperty> page;
        int index = 0;
        do {
            page = accountingEntityPropertyRepository.findByKeyAndOriginTypeAndIsDeletedFalse(Contract.DOWNGRADING_NATIONALITY_DATE, Contract.class.getSimpleName(), PageRequest.of(index++, 100));

            for (AccountingEntityProperty accountingEntityProperty : page.getContent()) {
                Contract contract = (Contract) accountingEntityProperty.getOrigin();
                if (accountingEntityProperty.getValue() == null) continue;
                try {
                    DateTime replacementDate = new DateTime(DateUtil.parseDateDashed(accountingEntityProperty.getValue()));
                    DateTime now = DateTime.now();

                    if (replacementDate.getMonthOfYear() == now.getMonthOfYear()) {
                        logger.info("Month of switching date = Current month, -> do nothing");
                        continue;
                    }

                    jobExecutionService.createBackGroundTask(SwitchingNationalityFromFilipinoSJ.class.getSimpleName(),
                            "contractController", "downgradeNationality", contract);
                } catch (Exception e) {
                    logger.severe("Exception while Downgrading Nationality, Contract#" + contract.getId());
                    logger.severe("Exception: " + ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());
    }
}
