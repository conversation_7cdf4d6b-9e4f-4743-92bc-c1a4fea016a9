package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         Created on 11, 4, 2020
 *         Jirra ACC-1597
 */

// this job checks for client didn't sign dd by defined setups and send messages
public class AfterCashFlowMessagesJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(AfterCashFlowMessagesJob.class.getName());
    private FlowEventConfig flowEventConfig;
    private FlowSubEventConfig noSignatureSubEvent;
    private FlowProcessorService flowProcessorService;
    private AfterCashFlowService afterCashFlowService;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    private ContractService contractService;
    private MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.SEVERE, "Started job");
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        afterCashFlowService = Setup.getApplicationContext().getBean(AfterCashFlowService.class);
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        contractPaymentConfirmationToDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
        contractService = Setup.getApplicationContext().getBean(ContractService.class);
        maidVisaFailedMedicalCheckService = Setup.getApplicationContext().getBean(MaidVisaFailedMedicalCheckService.class);

        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
        if (flowEventConfig == null) return;
        logger.log(Level.SEVERE, "flowEventConfig id: {0}", flowEventConfig.getId());
    
        noSignatureSubEvent = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flowEventConfig);
        if (noSignatureSubEvent == null) return;
        logger.log(Level.INFO, "flowSubEventConfig id: {0}", noSignatureSubEvent.getId());

        createNewFlows();
        sendAfterCashMessages();

        logger.log(Level.SEVERE, "Ended job");
    }

    public void sendAfterCashMessages() {
        logger.log(Level.SEVERE, "sendAfterCashMessages");

        List <FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);
        logger.log(Level.SEVERE, "flowProcessorEntityRepository size: {0}", flowProcessorEntities.size());

        Tag xPaidPaymentsTagMV = flowEventConfig.getTagValue("mv_ipam_x_paid_payments_to_convert_paying_cc");
        Tag xPaidPaymentsTagCC = flowEventConfig.getTagValue("cc_ipam_x_paid_payments_to_convert_paying_cc");
        int xPaidPaymentsMV = xPaidPaymentsTagMV != null ? Integer.parseInt(xPaidPaymentsTagMV.getValue()) : 5;
        int xPaidPaymentsCC = xPaidPaymentsTagCC != null ? Integer.parseInt(xPaidPaymentsTagCC.getValue()) : 2;

        for (FlowProcessorEntity flowProcessorEntity : flowProcessorEntities) {
            logger.log(Level.INFO, "processing entity id: {0}", flowProcessorEntity.getId());
            try {

                int xPaidPayments = flowProcessorEntity.getContract().isMaidCc() ? xPaidPaymentsCC : xPaidPaymentsMV;
                // ACC-6624
                if (afterCashFlowService.checkAndSwitchToPayingViaCC(flowProcessorEntity, xPaidPayments)) {
                    logger.info("Switch To Paying Via credit card -> exiting");
                    continue;
                }

                noSignatureSubEventProcess(flowProcessorEntity);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "sendAfterCashMessages is running contract prospect: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    
    private void noSignatureSubEventProcess(FlowProcessorEntity entity) {
        ContractPaymentTerm contractPaymentTerm = entity.getContractPaymentTerm();
        logger.log(Level.INFO, "contractPaymentTerms id: {0}", contractPaymentTerm.getId());
        logger.log(Level.INFO, "trial: {0}; currentFlowRun: {1}", new Object[] {entity.getTrials(), entity.getCurrentFlowRun()});

        if (entity.getTrials() < noSignatureSubEvent.getMaxTrials() - 1 &&
                entity.getCurrentFlowRun() > 1 &&
                new DateTime().getDayOfMonth() == 1) {
            afterCashFlowService.disableThankYouMessage(contractPaymentTerm.getContract());
        }

        if (entity.getContract().isPayingViaCreditCard()) {
            logger.info("entity id: " + entity.getId() + ",  Flow completed because contract is tagged by paying via cc");
            entity.setCompleted(true);
            flowProcessorEntityRepository.save(entity);
            return;
        }

        if (flowProcessorService.validateFlowStopping(entity)) {
            return;
        }

        if (new DateTime().isAfter(new DateTime(entity.getContract().getPaidEndDate()))) {
            entity.setTrials(entity.getCurrentSubEvent().getMaxTrials() - 1);
            entity.setReminders(entity.getCurrentSubEvent().getMaxReminders());
        } else if (entity.getTrials() == 1) {
            int xDays = Integer.parseInt(
                    flowEventConfig.getTagValue(entity.getContract().isMaidCc() ?
                            "ipam_x_days_before_paid_end_date" : "ipam_x_days_before_adjusted_end_date").getValue());
            LocalDate d = new LocalDate(entity.getContract().isMaidCc() ?
                    entity.getContract().getPaidEndDate() : entity.getContract().getAdjustedEndDate());

            if (new LocalDate().isBefore(d.minusDays(xDays))) {
                return;
            }

            // ACC-8796 retain the salary with us (IPAM flow)
            if (contractPaymentTerm.getContract().isMaidVisa() &&
                    maidVisaFailedMedicalCheckService.checkFailedMedicalsForPayingViaCc(contractPaymentTerm)) {
                logger.info("Maid failed medical step -> skip");
                afterCashFlowService.resetFlow(entity.getId());
                return;
            }

            entity.setLastExecutionDate(new DateTime()
                    .withHourOfDay(9).withMinuteOfHour(0).withSecondOfMinute(0)
                    .toDate());
        }

        if (entity.getTrials() == noSignatureSubEvent.getMaxTrials() - 1 &&
                entity.getCurrentFlowRun() < flowEventConfig.getMaxFlowRuns() &&
                flowProcessorService.nextMonthPaymentReceived(contractPaymentTerm.getContract())) { // 4th
            logger.log(Level.SEVERE, "Payment Received -> resetting IPAM flow");
            afterCashFlowService.resetFlow(entity.getId());
            return;
        }

        flowProcessorService.processFlowSubEventConfig(entity);
    }
    
    public void createNewFlows() {
        logger.info("start create new flows");
        List<Long> cptIds = new SelectQuery<>(
                getCandidateCPTs(flowEventConfig.getId()), "",
                Long.class, new HashMap()).execute();

        logger.info("Processing " + cptIds.size() + " contracts");

        for (Long cptId : cptIds) {
            logger.log(Level.INFO, "contractPaymentTerms id: {0}", cptId);
            try {
                ContractPaymentTerm cpt = Setup.getRepository(ContractPaymentTermRepository.class).findOne(cptId);

                if (!afterCashFlowService.validateFlow(cpt)) continue;

                Map<String, Object> map = new HashMap<>();
                map.put("trials", 0);
                map.put("reminders", 1);
                map.put("lastExecutionDate", new DateTime(cpt.getContract().getStartOfContract())
                        .dayOfMonth().withMinimumValue()
                        .withHourOfDay(9).withMinuteOfHour(0).withSecondOfMinute(0)
                        .toDate());

                FlowProcessorEntity f = flowProcessorService.createFlowProcessor(
                        flowEventConfig, noSignatureSubEvent,
                        cpt, map);

                // Send first message once the flow is triggered
                flowProcessorService.processFlowSubEventConfig(f);

                // add ipam additional info to the contract
                contractService.addAdditionalInfoForIpamFlow(cpt.getContract());

                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                        .cancelAllMonthlyDdsByCpt(cpt, DirectDebitCancellationToDoReason.CLIENT_PAID_CASH_EXTENDING_AFTER_CASH_FLOW);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "sendAfterCashMessages is running contract prospect: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public static String getCandidateCPTs(Long flowEventConfigId) {
        String query = Setup.getApplicationContext().getBean(QueryService.class)
                .getMainAfterCashQuery();

        // non DD payment should have been RECEIVED
        query += " and exists(select 1 from Payment p " +
                    "where p.contract = con and p.methodOfPayment <> 'DIRECT_DEBIT' " +
                        "and p.typeOfPayment.code = 'monthly_payment' and p.status = 'RECEIVED' " +
                        "and YEAR(p.dateOfPayment) = YEAR(con.startOfContract) " +
                        "and MONTH(p.dateOfPayment) = MONTH(con.startOfContract)) ";

        // START FLOWS FOR CONTRACTS IN CURRENT MONTH ONLY
        query += " and con.startOfContract < '" + new LocalDateTime().toString("yyyy-MM-dd HH:mm:ss")
                + "' and con.startOfContract >= '" + new LocalDateTime().minusMonths(1)
                .withDayOfMonth(1).toString("yyyy-MM-dd 00:00:00") + "'";

        // EXCLUDE ALREADY STARTED FLOWS
        // Should not exists Extension Flow is Running
        query += " and not exists (select 1 from FlowProcessorEntity f " +
                "where f.contractPaymentTerm.contract = con and " +
                "(f.flowEventConfig.id = " + flowEventConfigId + " or " +
                "(f.flowEventConfig.name = 'EXTENSION_FLOW' and f.stopped = false and f.completed = false))) ";

        return query;
    }
}