package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.service.JobExecutionService;
import java.util.List;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

public class OecAmendDDsJob implements MagnamediaJob {
    
    private static final Logger logger = Logger.getLogger(OecAmendDDsJob.class.getName());
    private static final String prefix = "MMM ";
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    private JobExecutionService jobExecutionService;

    public OecAmendDDsJob() {
        accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        jobExecutionService = Setup.getApplicationContext().getBean(JobExecutionService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        
        logger.log(Level.SEVERE, prefix + "OecAmendDDsJob started");
        
        LocalDate t = new LocalDate();
        LocalDate e = t.dayOfMonth().withMaximumValue();
        int oecDay = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
            AccountingModule.OEC_AMEND_DDS_JOB_START_DAY));
    
        logger.log(Level.SEVERE, "current: " + t.getDayOfMonth() +
            "; end: " + e.getDayOfMonth() + "; oecDay: " + oecDay);
    
        if(t.getDayOfMonth() != (e.getDayOfMonth() - oecDay)) {
            logger.log(Level.SEVERE, "OecAmendDDsJob exiting");
            return;
        }
        
        DateTime now = new DateTime();
        DateTime firstOfCurrentMonth = now.dayOfMonth().withMinimumValue();
        DateTime firstOfLastMonth = now.minusMonths(1).dayOfMonth().withMinimumValue().minusDays(1);
        List<AccountingEntityProperty> list =
                accountingEntityPropertyRepository.findByKeyAndOriginTypeAndValueDateBetweenAndDeletedFalse(
                        Contract.OEC_AMEND_DDS,
                        Contract.class.getSimpleName(),
                        firstOfLastMonth.toDate(),
                        firstOfCurrentMonth.toDate());
        logger.log(Level.SEVERE, "Date firstOfCurrentMonth "+firstOfCurrentMonth.toDate() + " firstOfLastMonth "+firstOfLastMonth.toDate()+" returned list is "+list);
        for (AccountingEntityProperty accountingEntityProperty : list) {
            Contract contract = (Contract) accountingEntityProperty.getOrigin();
            if (accountingEntityProperty.getValue() == null) continue;
            jobExecutionService.createBackGroundTask(OecAmendDDsJob.class.getSimpleName(),
                        "directDebitController", "oecAmendDDsFlow", contract);
            }
        
        logger.log(Level.SEVERE, prefix + "OecAmendDDsJob ended");
    }
}