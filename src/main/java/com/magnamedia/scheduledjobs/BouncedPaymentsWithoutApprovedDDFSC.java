package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.JobExecutionService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 17, 2020
 *         Jirra ACC-1721
 */

public class BouncedPaymentsWithoutApprovedDDFSC implements MagnamediaJob {

    private JobExecutionService jobExecutionService;
    private DirectDebitRepository directDebitRepository;
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;
    private DirectDebitRejectionToDoRepository debitRejectionToDoRepository;

    public BouncedPaymentsWithoutApprovedDDFSC() {
        jobExecutionService = Setup.getApplicationContext().getBean(JobExecutionService.class);
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);
        debitRejectionToDoRepository = Setup.getApplicationContext().getBean(DirectDebitRejectionToDoRepository.class);
    }

    private static final Logger logger =
            Logger.getLogger(BouncedPaymentsWithoutApprovedDDFSC.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    private void runJob() {
        Page<DirectDebit> page;
        Integer pageIndex = 0;
        List<Long> processedIDs = new ArrayList();
        processedIDs.add(-1L);
        do {
            page = directDebitRepository.findDDsForBouncedPayments(PageRequest.of(pageIndex, 100), DateUtil.getTodayDate(), processedIDs);
            for (DirectDebit dd : page.getContent()) {
                logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC page index:" + pageIndex);
                logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC page:" + page.getNumber());
                logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC page elements:" + page.getNumberOfElements());

                try {
                    processedIDs.add(dd.getId());

                    if (getRelatedBouncingRejectionFlow(dd) != null) {
                        logger.log(Level.INFO, "DD {0} Related to Rejection", dd.getId());
                        continue;
                    }

                    Contract contract = dd.getContractPaymentTerm().getContract();
                    if (directDebitRejectionFlowService.doesClientHavePendingDesignerToDo(contract)) {
                        logger.log(Level.INFO, "Contract{0} has a Pending Graphic Designer Todo", contract.getId());
                        continue;
                    }

                    DirectDebitRejectionToDo todo = getRelatedRejectionFlow(dd);
                    if (todo != null) {
                        todo.setCompleted(Boolean.TRUE);
                        todo.setStopped(true);
                        debitRejectionToDoRepository.save(todo);
                    }

                    jobExecutionService.createBackGroundTask(BouncedPaymentsWithoutApprovedDDFSC.class.getName(),
                            "bouncingFlowService", "processDDBouncedPayments", dd);
                } catch (Exception e) {
                    PrintWriter pw = new PrintWriter(System.out);
                    StringWriter sw = new StringWriter();
                    PrintWriter pw1 = new PrintWriter(sw);
                    e.printStackTrace(pw);
                    e.printStackTrace(pw1);

                    logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC" + e.getMessage());
                }
            }
        } while (page.hasNext());
    }

    private boolean isDDRelatedToRejectionFlow(DirectDebit directDebit) {
        boolean ddRejection = directDebit.getDirectDebitRejectionToDo() != null;
        ddRejection = ddRejection && !BooleanUtils.toBoolean(directDebit.getDirectDebitRejectionToDo().isStopped()) &&
                !BooleanUtils.toBoolean(directDebit.getDirectDebitRejectionToDo().isCompleted());

        boolean ddBouncingRejection = directDebit.getDirectDebitBouncingRejectionToDo() != null;
        ddBouncingRejection = ddBouncingRejection && !BooleanUtils.toBoolean(directDebit.getDirectDebitBouncingRejectionToDo().isStopped()) &&
                !BooleanUtils.toBoolean(directDebit.getDirectDebitBouncingRejectionToDo().isCompleted());

        return ddRejection || ddBouncingRejection;
    }

    private DirectDebitRejectionToDo getRelatedRejectionFlow(DirectDebit directDebit) {
        DirectDebitRejectionToDo ddRejection = directDebit.getDirectDebitRejectionToDo();
        if(ddRejection != null && !BooleanUtils.toBoolean(directDebit.getDirectDebitRejectionToDo().isStopped()) &&
                !BooleanUtils.toBoolean(directDebit.getDirectDebitRejectionToDo().isCompleted()))
            return ddRejection;
        
        return null;
    }

    private DirectDebitRejectionToDo getRelatedBouncingRejectionFlow(DirectDebit directDebit) {
        DirectDebitRejectionToDo ddRejection = directDebit.getDirectDebitBouncingRejectionToDo();
        if(ddRejection != null && !BooleanUtils.toBoolean(directDebit.getDirectDebitBouncingRejectionToDo().isStopped()) &&
                !BooleanUtils.toBoolean(directDebit.getDirectDebitBouncingRejectionToDo().isCompleted()))
            return ddRejection;
        
        return null;
    }
}
