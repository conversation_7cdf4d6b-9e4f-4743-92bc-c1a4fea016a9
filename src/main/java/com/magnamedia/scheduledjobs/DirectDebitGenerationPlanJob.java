package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.ContractPaymentTypeRepository;
import com.magnamedia.repository.DirectDebitGenerationPlanRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitGenerationPlanService;
import com.magnamedia.service.DirectDebitService;
import com.magnamedia.service.FlowProcessorService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

//ACC-3741
public class DirectDebitGenerationPlanJob implements MagnamediaJob {

    private final DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;
    private final ContractPaymentTypeRepository contractPaymentTypeRepository;

    private final DirectDebitGenerationPlanService directDebitGenerationPlanService;

    private final DirectDebitService directDebitService;

    private final FlowProcessorService flowProcessorService;
    private final ContractService contractService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitGenerationPlanJob.class.getName());

    public DirectDebitGenerationPlanJob() {
        directDebitGenerationPlanRepository = Setup.getRepository(DirectDebitGenerationPlanRepository.class);
        contractPaymentTypeRepository = Setup.getRepository(ContractPaymentTypeRepository.class);
        directDebitGenerationPlanService = Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class);
        directDebitService = Setup.getApplicationContext().getBean(DirectDebitService.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        contractService = Setup.getApplicationContext().getBean(ContractService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    public void runJob() {

        logger.info("Start");

        directDebitGenerationPlanService.updatePlansUponActiveCptChanged();
        generationPostponedDDsScheduleByContracts();
        //ACC-8875
        sendNotificationForGeneratedDdFromPlans();
        generateDdFromPlans();
        sendReport();

        logger.info("End");
    }

    private void generationPostponedDDsScheduleByContracts() {
        logger.info("started");

        Long lastId = -1L;
        Date today = new Date(new java.util.Date().getTime());

        Page<ContractPaymentTerm> p;
        do {
            p = directDebitGenerationPlanRepository.findActiveCptHasNotActivePlan(
                    lastId, AbstractPaymentTypeConfig.monthlyTypes, today,
                    DirectDebitGenerationPlanService.pendingStatus,
                    PageRequest.of(0, 200));
            if (p.isEmpty()) break;

            p.getContent()
                    .forEach(cpt -> {
                        logger.info("cpt id: " + cpt.getId());
                        try {
                            if (contractService.hasPreventCreateOtherDds(cpt.getContract())) {
                                logger.info("Has Prevent Create Other Dds -> exiting");
                                return;
                            }

                            if (flowProcessorService.isPayingViaCreditCard(cpt.getContract()) ||
                                    !directDebitService.contractHasClosedMainDdcToDo(
                                            cpt.getContract().getId(), AppsServiceDDApprovalTodo.DdcTodoType.PROSPECT_APP)) {
                                logger.info("Has not a closed main DDC todo or the IPAM flow or Paying Via CC flow is running -> exiting");
                                return;
                            }

                            cpt.getContractPaymentTypes()
                                    .stream().filter(t -> !t.isPostponedDdGenerated() && !AbstractPaymentTypeConfig.monthlyTypes.contains(t.getType().getCode()))
                                            .forEach(t -> {
                                                generationNewPlanOfContractPaymentType(cpt, t)
                                                    .forEach(directDebitGenerationPlanService::saveGenerationPlan);
                                                t.setPostponedDdGenerated(true);
                                                contractPaymentTypeRepository.silentSave(t);
                                            });
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });

            lastId = p.getContent().get(p.getContent().size() - 1).getId();

        } while (!p.isEmpty());

        logger.info("end");
    }

    private List<DirectDebitGenerationPlan> generationNewPlanOfContractPaymentType(ContractPaymentTerm cpt, ContractPaymentType t) {
        logger.info("non Monthly ContractPaymentType id: " + t.getId());

        DateTime startDate = new DateTime(cpt.getContract().getStartOfContract()).withTimeAtStartOfDay();
        DateTime endDate = startDate.plusMonths(cpt.getContract().getPaymentsDuration() - (cpt.getContract().getIsProRated() ? 0 : 1))
                .dayOfMonth().withMinimumValue();

        logger.info("startDate: " + startDate.toString("yyyy-MM-dd HH:mm:ss") +
                " ; endDate: " + endDate.toString("yyyy-MM-dd HH:mm:ss"));

        Map<String, Object> plansMap = directDebitGenerationPlanService.buildDirectDebitGenerationPlans(
                t, startDate, endDate);

        logger.info("plans size: " + ((List<DirectDebitGenerationPlan>) plansMap.get("plans")).size());
        return (List<DirectDebitGenerationPlan>) plansMap.get("plans");
    }

    private void sendNotificationForGeneratedDdFromPlans() {
        logger.info("Start");
        String daysNum = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DAYS_BEFORE_SEND_NOTIFICATION_FOR_GENERATION_PLAN_INSURANCE);
        if (daysNum == null || daysNum.isEmpty()) return;

        Long lastId = -1L;
        Date afterXDays = new Date(new LocalDate().plusDays(Integer.parseInt(daysNum)).toDate().getTime());
        Page<DirectDebitGenerationPlan> page;
        do {
            page = directDebitGenerationPlanRepository.findByStatusPendingAndTypeInsuranceAndDDGenerationDateEquals(
                    lastId, afterXDays, PageRequest.of(0, 100));

            for (DirectDebitGenerationPlan plan : page.getContent()) {
                try {
                    directDebitGenerationPlanService.sendNotificationAndStartReminderFlow(plan);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (!page.getContent().isEmpty()) {
                lastId = page.getContent().get(page.getContent().size() - 1).getId();
            }

        } while (!page.getContent().isEmpty());

        logger.info("End");
    }

    private void generateDdFromPlans() {
        logger.log(Level.INFO, "Start");
        List<Long> processedPlanIDs = new ArrayList<>();
        processedPlanIDs.add(-1L);
        Date today = new Date(new LocalDate().toDate().getTime());
        Page<DirectDebitGenerationPlan> page;

        int pageIndex = 0;
        do {
            page = directDebitGenerationPlanRepository.findByStatusPendingAndDDGenerationDateEquals(
                    today, processedPlanIDs, PageRequest.of(pageIndex, 100));

            for (DirectDebitGenerationPlan directDebitGenerationPlan : page.getContent()) {
                try {
                    processedPlanIDs.add(directDebitGenerationPlan.getId());
                    logger.log(Level.INFO, "Start BK task Generate direct debits "
                            + "plan Id " + directDebitGenerationPlan.getId());

                    Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                            .create(new BackgroundTask.builder(
                                    "generateDDFromGenerationPlan" + new java.util.Date().getTime(),
                                    "accounting",
                                    "directDebitGenerationPlanService",
                                    "generateDDFromGenerationPlan")
                                    .withRelatedEntity("DirectDebitGenerationPlan", directDebitGenerationPlan.getId())
                                    .withParameters(
                                            new Class[] {Long.class},
                                            new Object[] {directDebitGenerationPlan.getId()})
                                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                                    .withDelay(directDebitGenerationPlan.getOneTime() ? 0L : 60L * 1000L)
                                    .build());

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "exception while generate direct debits running on " +
                            "directDebitGenerationPlan#" + directDebitGenerationPlan.getId());
                    logger.log(Level.SEVERE, "Exception: " + ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());

        logger.log(Level.INFO, "End");
    }

    private void sendReport() {
        logger.log(Level.INFO, "Start");
        Date today = new Date(new LocalDate().toDate().getTime());
        //Send a report to George
        List<DirectDebitGenerationPlan> directDebitGenerationPlans =
                directDebitGenerationPlanRepository.findByStatusPendingAndDDGenerationDateLessThan(today);
        if (directDebitGenerationPlans != null && directDebitGenerationPlans.size() > 0) {
            logger.log(Level.INFO, "Plans size : " + directDebitGenerationPlans.size());
            directDebitGenerationPlanService.sendFailedDDsGenerationReport(directDebitGenerationPlans);
        }

        logger.log(Level.INFO, "End send a report");
    }
}