package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/11/2022
 * this is job which should be run daily to close rejection to-do if the contract is not active anymore or the CPT is not active or the direct debit is rejected or expired or cancelled
 * or if it's related to DDB and there is a newer DDB cover the same period (not rejected or cancelled or expired)
 **/

public class ClosingDirectDebitRejectionTodosJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> parameters) {
        DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
        DirectDebitRejectionFlowService service = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

        List<DirectDebitRejectionToDo> rejectionToDoList = directDebitRejectionToDoRepository.findActiveRejectionFlows();

        for (DirectDebitRejectionToDo t : rejectionToDoList) {
            if(!service.todoExpired(t)) continue;

            t.setCompleted(true);
            t.setStopped(true);
            directDebitRejectionToDoRepository.save(t);
        }
    }
}