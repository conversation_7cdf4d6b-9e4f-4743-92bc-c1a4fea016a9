package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.repository.FlowEventConfigRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.repository.FlowSubEventConfigRepository;
import com.magnamedia.service.FlowProcessorService;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UnpaidOnlineCreditCardPaymentJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(UnpaidOnlineCreditCardPaymentJob.class.getName());

    private FlowProcessorService flowProcessorService;
    private FlowEventConfig flowEventConfig;
    private FlowSubEventConfig flowSubEventConfig;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");

        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        if (flowEventConfig == null) return;
        logger.log(Level.INFO, "flowEventConfig id: {0}", flowEventConfig.getId());

        flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.PENDING_PAYMENT, flowEventConfig);
        if (flowSubEventConfig == null) return;
        logger.log(Level.INFO, "flowSubEventConfig id: {0}", flowSubEventConfig.getId());

        sendUnpaidOnlineCreditCardPaymentMessages();
        logger.log(Level.INFO, "Job finished");

    }

    public void sendUnpaidOnlineCreditCardPaymentMessages() {
        logger.log(Level.INFO, "sendUnpaidOnlineCreditCardPaymentMessages runs");

        List <FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);

        logger.log(Level.INFO, "flowProcessorEntities size: {0}", flowProcessorEntities.size());

        for (FlowProcessorEntity flowProcessorEntity : flowProcessorEntities) {
            logger.log(Level.INFO, "processing entity id: {0}", flowProcessorEntity.getId());
            try {
                ContractPaymentTerm contractPaymentTerm = flowProcessorEntity.getContractPaymentTerm();
                logger.log(Level.INFO, "contractPaymentTerms id: {0}", contractPaymentTerm.getId());

                flowProcessorService.processFlowSubEventConfig(flowProcessorEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}