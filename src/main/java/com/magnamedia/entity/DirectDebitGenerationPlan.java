package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentMethod;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.sql.Date;

//ACC-3741
@Entity
@Audited
public class DirectDebitGenerationPlan extends BaseEntity implements Cloneable {

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Column
    private Long ddID;

    @Column
    private Date ddGenerationDate;

    @Column
    private Date ddSendDate;

    @Column
    private Date ddExpiryDate;

    @Column
    @Enumerated(EnumType.STRING)
    private DdGenerationPlanStatus ddGenerationPlanStatus;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    private ContractPaymentType contractPaymentType;

    @Column(nullable = false)
    @Min(0)
    private Double amount;

    @Column(nullable = false)
    private Double discountAmount = 0.0;

    @Column(nullable = false)
    @Min(0)
    private Double additionalDiscountAmount = 0.0;

    @Column
    private Double moreAdditionalDiscount = 0.0;

    @Column(columnDefinition = "boolean default false")
    private Boolean messageSent = false;

    @Column(columnDefinition = "boolean default true")
    private Boolean sendNotificationToClient = true;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitStatus ddStatus;

    @Column(columnDefinition = "boolean default true")
    private Boolean oneTime = true;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebit ddCloned;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Date getDDGenerationDate() {
        return ddGenerationDate;
    }

    public void setDDGenerationDate(Date ddGenerationDate) {
        this.ddGenerationDate = ddGenerationDate;
    }

    public Long getDDID() {
        return ddID;
    }

    public void setDDID(Long ddID) {
        this.ddID = ddID;
    }

    public Date getDDSendDate() {
        return ddSendDate;
    }

    public void setDDSendDate(Date ddSendDate) {
        this.ddSendDate = ddSendDate;
    }

    public Date getDDExpiryDate() {
        return ddExpiryDate;
    }

    public void setDDExpiryDate(Date ddExpiryDate) {
        this.ddExpiryDate = ddExpiryDate;
    }

    public ContractPaymentType getContractPaymentType() {
        return contractPaymentType;
    }

    public void setContractPaymentType(ContractPaymentType contractPaymentType) {
        this.contractPaymentType = contractPaymentType;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Double getAdditionalDiscountAmount() {
        return additionalDiscountAmount;
    }

    public void setAdditionalDiscountAmount(Double additionalDiscountAmount) {
        this.additionalDiscountAmount = additionalDiscountAmount;
    }

    public DdGenerationPlanStatus getddGenerationPlanStatus() {
        return ddGenerationPlanStatus;
    }

    public void setddGenerationPlanStatus(DdGenerationPlanStatus ddGenerationPlanStatus) {
        this.ddGenerationPlanStatus = ddGenerationPlanStatus;
    }

    public Boolean getSendNotificationToClient() { return sendNotificationToClient; }

    public void setSendNotificationToClient(Boolean sendNotificationToClient) {
        this.sendNotificationToClient = sendNotificationToClient;
    }

    public DirectDebitStatus getDdStatus() { return ddStatus; }

    public void setDdStatus(DirectDebitStatus ddStatus) { this.ddStatus = ddStatus; }

    public Boolean getOneTime() { return oneTime != null && oneTime; }

    public void setOneTime(Boolean oneTime) { this.oneTime = oneTime; }

    @JsonIgnore
    @JsonSerialize(using = IdLabelSerializer.class)
    public Client getClient() {
        return  this.contract.getClient();
    }

    public Boolean getMessageSent() {
        return messageSent == null ? false : messageSent;
    }

    public void setMessageSent(Boolean messageSent) {
        this.messageSent = messageSent;
    }

    public DirectDebit getDdCloned() { return ddCloned; }

    public void setDdCloned(DirectDebit ddCloned) { this.ddCloned = ddCloned; }

    public Double getMoreAdditionalDiscount() { return moreAdditionalDiscount == null ? 0.0 : moreAdditionalDiscount; }

    public void setMoreAdditionalDiscount(Double moreAdditionalDiscount) { this.moreAdditionalDiscount = moreAdditionalDiscount; }

    public enum DdGenerationPlanStatus {
        PENDING,
        CREATED,
        CANCELED,
        PENDING_PAYING_VIA_CREDIT_CARD
    }

    public ContractPayment generateCardPayment(ContractPaymentTerm cpt) {
        ContractPayment contractPayment = new ContractPayment();
        contractPayment.setContractPaymentTerm(cpt);
        contractPayment.setDiscountAmount(this.getDiscountAmount());
        contractPayment.setPaymentType(this.getContractPaymentType().getType());
        contractPayment.setSubType(this.contractPaymentType.getSubType());
        contractPayment.setAmount(this.getAmount());
        contractPayment.setAdditionalDiscountAmount(this.getAdditionalDiscountAmount());
        contractPayment.setDate(this.getDDSendDate());
        contractPayment.setDescription(this.getContractPaymentType().getDescription());
        contractPayment.setAffectsPaidEndDate(this.getContractPaymentType().getAffectsPaidEndDate());
        contractPayment.setIsCalculated(true);
        contractPayment.setOneTime(true);
        contractPayment.setPaymentMethod(PaymentMethod.CARD);
        contractPayment.setMoreAdditionalDiscount(getMoreAdditionalDiscount());

        return contractPayment;
    }

    @Override
    public DirectDebitGenerationPlan clone() {
        try {
            return (DirectDebitGenerationPlan) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}