package com.magnamedia.entity.projection;

import com.magnamedia.module.type.VatType;
import java.sql.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Jan 4, 2018
 */
public interface TransactionCsvProjection {

    @Value("#{(target.getId())}")
    Long getName();

    //String getName();
//    @Value("#{(target.getFromBucket()!=null)?(target.getFromBucket().getName()):(\"\")}")
//    String getBucketFromName();

    //ACC-2841
    @Value("#{(target.getFromBucket()!=null)?(target.getFromBucket().getNameWithCardNumber()):(\"\")}")
    String getBucketFromName();

    //Jirra ACC-301
    @Value("#{(target.getFromBucket()!=null)?(target.getFromBucket().getCode()):(\"\")}")
    String getBucketFromCode();

    @Value("#{(target.getRevenue()!=null)?(target.getRevenue().getName()):(\"\")}")
    String getRevenueName();

    //Jirra ACC-301
    @Value("#{(target.getRevenue()!=null)?(target.getRevenue().getCode()):(\"\")}")
    String getRevenueCode();

    @Value("#{(target.getExpense()!=null)?(target.getExpense().getNameLabel()):(\"\")}")
    String getExpenseName();

    //Jirra ACC-301
    @Value("#{(target.getExpense()!=null)?(target.getExpense().getCodeLabel()):(\"\")}")
    String getExpenseCode();

//    @Value("#{(target.getToBucket()!=null)?(target.getToBucket().getName()):(\"\")}")
//    String getBucketToName();

    //ACC-2841
    @Value("#{(target.getToBucket()!=null)?(target.getToBucket().getNameWithCardNumber()):(\"\")}")
    String getBucketToName();

    //Jirra ACC-301
    @Value("#{(target.getToBucket()!=null)?(target.getToBucket().getCode()):(\"\")}")
    String getBucketToCode();

    String getDescription();
    
    java.util.Date getPnlValueDate();

    @Value("#{(target.getAmount())}")
    Double getTransactionAmount();
    Double getVatAmount();
    VatType getVatType();
    
    @Value("#{(target.getDate())}")
    Date getDateOfTransaction();

    @Value("#{(target.getCreationDate())}")
    java.util.Date getDateOfCreation();

    String getTransactionFor();
    
    String getVisaExpenseName();
    
    //Jirra ACC-960
    @Value("#{(target.getLicense()!=null) ? target.getLicense().getName() : ''}")
    String getLicense();

    @Value("#{target.getPaymentId() != null ? target.getPaymentId().toString() : ''}")
    String getPaymentId();
}
