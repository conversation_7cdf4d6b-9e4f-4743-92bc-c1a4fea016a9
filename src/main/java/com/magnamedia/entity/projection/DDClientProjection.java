package com.magnamedia.entity.projection;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.serializer.DDFListSerializer;
import com.magnamedia.module.type.DirectDebitStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 25, 2020
 *         Jirra ACC-2136
 */

public interface DDClientProjection {
    Long getId();

    List<Attachment> getAttachments();

    Double getAdditionalDiscount();

    Date getStartDate();

    Date getExpiryDate();

    @Value("#{{label:target.getStatusLabel() , value:target.getStatusValue()}}")
    Map<?,?> getStatus();

    DirectDebitStatus getMStatus();

    @Value("#{(target.getPayments() != null && !target.getPayments().isEmpty())" +
            " ? target.getPayments().get(0).getPaymentType() : null}")
    PicklistItem getPaymentType();

    String getAdditionalDiscountNotes();

    double getAmount();

    @Value("#{target.getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') != null ? "
            + "target.getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') : "
            + "(target.getAdditionalDiscount() != null && target.getAdditionalDiscount() != 0 "
            + "&& target.getContractPaymentTerm() != null ? target.getContractPaymentTerm().getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT'): null)}")
    Attachment getAdditionalDiscountAttachment();

    @Value("#{target.getContractPaymentTerm() != null ? "
            + "{id: target.getContractPaymentTerm().getId(), "
            + "label: target.getContractPaymentTerm().getLabel(), "
            + "isActive: target.getContractPaymentTerm().isActive(),"
            + "attachments: target.getContractPaymentTerm().getAttachments(), "
            + "additionalDiscountNotes: target.getContractPaymentTerm().getAdditionalDiscountNotes()}"
            + ": null}")
    Map<?, ?> getContractPaymentTerm();

    @JsonSerialize(using = DDFListSerializer.class)
    List<DirectDebitFile> getDirectDebitFiles();

    String getEid();

    String getIbanNumber();

    String getBankName();

    String getAccountName();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getBank();

    boolean isHidden();
}
