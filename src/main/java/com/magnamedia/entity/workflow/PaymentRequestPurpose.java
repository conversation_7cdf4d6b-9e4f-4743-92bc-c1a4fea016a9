package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.ClientRefundSetup;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.LoanType;

import javax.persistence.*;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 22, 2019
 * ACC-837
 */
@Entity
public class PaymentRequestPurpose extends BaseEntity {
    
    @Column
    @Label
    private String name;
    
    @Column
    private Integer threshold;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User user;
    
    @Column
    private boolean forClient = true;
    
    @Column
    private boolean forHousemaid = true;

    @Column(columnDefinition = "boolean default true")
    private Boolean active = true;

    @Column
    @Enumerated(EnumType.STRING)
    private LoanType loanType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem typeOfPayment;

    @JsonIgnore
    @OneToMany(mappedBy = "paymentRequestPurpose")
    private List<ClientRefundSetup> purposeSetup;

    //Jirra ACC-3692
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem refundCategory;

    @Column(columnDefinition = "boolean default false")
    private Boolean categorySamePurpose = false;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public boolean isForClient() {
        return forClient;
    }

    public void setForClient(boolean forClient) {
        this.forClient = forClient;
    }

    public boolean isForHousemaid() {
        return forHousemaid;
    }

    public void setForHousemaid(boolean forHousemaid) {
        this.forHousemaid = forHousemaid;
    }

    public String getCategory(){
        
        String result = "";
        if (this.isForClient())
            result = "Client";
        if (this.isForHousemaid()){
            if (result.isEmpty())
                result = "Housemaid";
            else
                result = result + " - Housemaid";
        }
        return result;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public void setLoanType(LoanType loanType) {
        this.loanType = loanType;
    }

    public LoanType getLoanType() {
        return loanType;
    }

    public ClientRefundSetup getUniquePurposeSetup() {
        return getUniquePurposeSetup(null);
    }

    public ClientRefundSetup getUniquePurposeSetup(Long partialId) {
        if(purposeSetup == null) return null;
        ClientRefundSetup setup = purposeSetup.stream()
                .filter(s -> (partialId != null &&
                        s.getPartialRefundForCancellationPaymentMethod() != null &&
                        s.getPartialRefundForCancellationPaymentMethod().getId().equals(partialId)) ||
                        (partialId == null &&
                                s.getPartialRefundForCancellationPaymentMethod() == null))
                .findFirst().orElse(null);

        if(setup == null) {
            setup = purposeSetup.stream()
                    .filter(s -> s.getPartialRefundForCancellationPaymentMethod() == null)
                    .findFirst().orElse(null);
        }

        return setup;
    }

    public void setPurposeSetup(List<ClientRefundSetup> purposeSetup) {
        this.purposeSetup = purposeSetup;
    }

    public List<ClientRefundSetup> getPurposeSetup() {
        return purposeSetup;
    }

    public PicklistItem getTypeOfPayment() {
        return typeOfPayment;
    }

    public void setTypeOfPayment(PicklistItem typeOfPayment) {
        this.typeOfPayment = typeOfPayment;
    }

    public PicklistItem getRefundCategory() {
        return refundCategory;
    }
    
    public String getCategoryStr() {
        return (this.getCategorySamePurpose()!=null && this.getCategorySamePurpose())?
                    this.getName() : (this.getRefundCategory()!=null? this.getRefundCategory().getLabel() : "");
    }

    public void setRefundCategory(PicklistItem refundCategory) {
        this.refundCategory = refundCategory;
    }

    public Boolean getCategorySamePurpose() {
        return categorySamePurpose != null && categorySamePurpose;
    }

    public void setCategorySamePurpose(Boolean categorySamePurpose) {
        this.categorySamePurpose = categorySamePurpose;
    }
    
}
