package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.BankDirectDebitCancelationRecordRepository;
import com.magnamedia.repository.DirectDebitFileRepository;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Where;
import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
@Entity
@Where(clause = "((DIRECT_DEBIT_FILE_ID IS NULL) OR exists(select ddf.id from DIRECTDEBITFILES ddf where ddf.id = DIRECT_DEBIT_FILE_ID))")
public class BankDirectDebitCancelationRecord extends BaseEntity {
    public static final String CB_STATUS_NAK = "NAK";
    public static final String CB_STATUS_ACK = "ACK";


    public enum status {
        CONFIRMED, // ACK
        REJECTED // NAK
    }

    @Column
    private Integer rowIndex;

    @Column
    private String ddaRefNo;

    @Column
    private String cancelReason;

    @Column
    private String errorMessage;

    @Column
    private boolean confirmed = false;

    @Column
    private Date checkerDate;

    @Column
    private String cbStatus;

    @Column
    @Enumerated(EnumType.STRING)
    private BankDirectDebitCancelationRecord.status status;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private BankDirectDebitCancelationFile bankDirectDebitCancelationFile;

    //Jirra ACC-1587
//    @NotAudited
//    @ManyToOne(fetch = FetchType.LAZY)
//    private DirectDebit directDebit;

    //Jirra ACC-1587
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    private DirectDebitFile directDebitFile;

    @Transient
    private boolean processing = false;

    @Column
    @ColumnDefault("0")
    private boolean confirmedByRPA = false;

    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }

    public String getDdaRefNo() {
        return ddaRefNo;
    }

    public String getDdaRefNoCsv() {
        return ddaRefNo != null ? "'" + ddaRefNo : "";
    }

    public void setDdaRefNo(String ddaRefNo) {
        this.ddaRefNo = ddaRefNo;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean isConfirmed() {
        return confirmed;
    }

    public void setConfirmed(boolean confirmed) {
        this.confirmed = confirmed;
    }

    public BankDirectDebitCancelationFile getBankDirectDebitCancelationFile() {
        return bankDirectDebitCancelationFile;
    }

    public void setBankDirectDebitCancelationFile(BankDirectDebitCancelationFile bankDirectDebitCancelationFile) {
        this.bankDirectDebitCancelationFile = bankDirectDebitCancelationFile;
    }

    public DirectDebitFile getDirectDebitFile() {
        return directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        this.directDebitFile = directDebitFile;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public BankDirectDebitCancelationRecord.status getStatus() { return status; }

    public void setStatus(BankDirectDebitCancelationRecord.status status) { this.status = status; }

    public void validate() {
        if (getBankDirectDebitCancelationFile() == null)
            throw new RuntimeException("File is mandatory.");
    }

    public Date getCheckerDate() {
        return checkerDate;
    }

    public void setCheckerDate(Date checkerDate) {
        this.checkerDate = checkerDate;
    }

    public String getCbStatus() {
        return cbStatus;
    }

    public void setCbStatus(String cbStatus) {
        this.cbStatus = cbStatus;
    }

    public boolean getProcessing() {

        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "BankDirectDebitCancelationRecord");
        query.filterBy("relatedEntityId", "=", this.getId());
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();
        if (result != null && !result.isEmpty())
            return true;
        else
            return false;
    }

    public void setProcessing(boolean processing) {
        this.processing = processing;
    }

    public boolean isConfirmedByRPA() {
        return confirmedByRPA;
    }

    public void setConfirmedByRPA(boolean confirmedByRPA) {
        this.confirmedByRPA = confirmedByRPA;
    }

    //Jirra ACC-2622
    @BeforeInsert
    public void validateInsert() {
        DirectDebitFileRepository directDebitFileRepository =
                Setup.getApplicationContext().getBean(DirectDebitFileRepository.class);
        List<DirectDebitFile> ddfs;
        if (getDdaRefNo() != null && !getDdaRefNo().isEmpty()) {

            ddfs = directDebitFileRepository.findByDdaRefNo(getDdaRefNo());

            if (ddfs.size() > 0) {

                if (ddfs.get(0).isOldByApplicationId()) {

                    for (DirectDebitFile ddf : ddfs) {
                        if (ddf.getDdStatus().equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)) {
                            setDirectDebitFile(ddf);
                            break;
                        }
                    }
                } else {
                    for (DirectDebitFile ddf : ddfs) {
                        if (ddf.getDdStatus().equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)) {
                            setDirectDebitFile(ddf);
                            break;
                        }
                    }
                    if (getDirectDebitFile() == null) {
                        for (DirectDebitFile ddf : ddfs) {
                            if (ddf.getDdStatus().equals(DirectDebitStatus.CANCELED)) {
                                setDirectDebitFile(ddf);
                                break;
                            }
                        }
                    }
                    if (getDirectDebitFile() == null) {
                        setDirectDebitFile(ddfs.get(0));
                    }
                }
            }
        }
        validate();
    }

    @BeforeUpdate
    public void validateUpdate() {
        validate();
        BankDirectDebitCancelationRecordRepository bankDirectDebitCancelationRecordRepository =
                Setup.getApplicationContext().getBean(BankDirectDebitCancelationRecordRepository.class);

        BankDirectDebitCancelationRecord old = bankDirectDebitCancelationRecordRepository.getOne(getId());
        if (getBankDirectDebitCancelationFile().getId() != old.getBankDirectDebitCancelationFile().getId())
            throw new RuntimeException("File is not updatable.");
    }
}
