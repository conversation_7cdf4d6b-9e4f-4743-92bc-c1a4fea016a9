package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.controller.BankStatementFileController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.BankDirectDebitCancelationFileRepository;
import com.magnamedia.repository.BankDirectDebitCancelationRecordRepository;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;

import org.hibernate.annotations.ColumnDefault;
import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 20, 2019
 *         Jirra ACC-1134
 */
@Entity
public class BankDirectDebitCancelationFile extends BaseEntity {

    @Label
    private String bankName;

    @Column
    private Date date;

    @Column
    private Date OldestPendingForCancellationDD;

    @Column
    private boolean hidden = false;

    @Column
    @ColumnDefault("0")
    private boolean confirmedByRPA = false;

    @Column
    @ColumnDefault("0")
    private boolean addedByRPA = false;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "bankDirectDebitCancelationFile",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    private List<BankDirectDebitCancelationRecord> records;

    public BankDirectDebitCancelationFile() {
    }

    public BankDirectDebitCancelationFile(Long attachmentId, Date date) {
        this(Setup.getRepository(AttachementRepository.class).getOne(attachmentId), date);
    }

    public BankDirectDebitCancelationFile(Attachment attachment, Date date) {
        this.addAttachment(attachment);
        this.setDate(date);
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public boolean isConfirmedByRPA() {
        return confirmedByRPA;
    }

    public void setConfirmedByRPA(boolean confirmedByRPA) {
        this.confirmedByRPA = confirmedByRPA;
    }

    public List<BankDirectDebitCancelationRecord> getRecords() {
        return records;
    }

    public void setRecords(List<BankDirectDebitCancelationRecord> records) {
        this.records = records;
    }

    public void setOldestPendingForCancellationDD(Date oldestPendingForCancellationDD) {
        OldestPendingForCancellationDD = oldestPendingForCancellationDD;
    }

    public Date getOldestPendingForCancellationDD() {
        return OldestPendingForCancellationDD;
    }

    public boolean isAddedByRPA() { return addedByRPA; }

    public void setAddedByRPA(boolean addedByRPA) { this.addedByRPA = addedByRPA; }

    @BeforeUpdate
    public void validateUpdate() {

        BankDirectDebitCancelationFileRepository bankDirectDebitCancelationFileRepository =
                Setup.getApplicationContext().getBean(BankDirectDebitCancelationFileRepository.class);

        BankDirectDebitCancelationFile old = bankDirectDebitCancelationFileRepository.getOne(getId());
        List<Long> oldRecordsIDs = old.getRecords().stream().map(x -> x.getId()).collect(Collectors.toList());
        if (getRecords().size() != oldRecordsIDs.size())
            throw new RuntimeException("Records is not updatable.");
        for (BankDirectDebitCancelationRecord record : getRecords())
            if (record.getId() == null || !oldRecordsIDs.contains(record.getId()))
                throw new RuntimeException("Records is not updatable.");
    }

    //Jirra ACC-330
    @AfterInsert
    public void parseRecords() throws IOException {
        if (getAttachments() == null || getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");

        Date smallestCheckerDate = null;

        Storage.updateAttachements(this);
        Attachment att = getAttachments().get(0);
        List<BankDirectDebitCancelationRecord> records = new ArrayList<>();
        BufferedReader br = null;
        String line = "";
        String cvsSplitBy = ",";
        br = new BufferedReader(new InputStreamReader(Storage.getStream(att)));
        for (int i = 0; i < 5; i++)
            br.readLine();

        while ((line = br.readLine()) != null) {
            String[] cellsValues = line.split(cvsSplitBy);
            BankDirectDebitCancelationRecord cancelationRecord = new BankDirectDebitCancelationRecord();
            try {
                //Jirra ACC-404
                cancelationRecord.setRowIndex(Integer.parseInt(getCellValue(cellsValues[0])));
                cancelationRecord.setDdaRefNo(
                        cellsValues[1] != null && !cellsValues[1].isEmpty() ?
                                getCellValue(cellsValues[1]) :
                                null);
                cancelationRecord.setCancelReason(
                        cellsValues[5] != null && !cellsValues[5].isEmpty() ?
                                getCellValue(cellsValues[5]) :
                                null);

                String cbStatus = cellsValues[6] != null && !cellsValues[6].isEmpty() ? getCellValue(cellsValues[6]) : null;

                cancelationRecord.setCbStatus(cbStatus);
                cancelationRecord.setStatus(BankDirectDebitCancelationRecord.CB_STATUS_NAK.equals(cbStatus) ?
                        BankDirectDebitCancelationRecord.status.REJECTED :
                        BankDirectDebitCancelationRecord.CB_STATUS_ACK.equals(cbStatus) ?
                                BankDirectDebitCancelationRecord.status.CONFIRMED :
                                null);

                String checkerDateStr = cellsValues[8] != null && !cellsValues[8].isEmpty() ? getCellValue(cellsValues[8]) : null;
                if (checkerDateStr != null) {
                    String[] checkerDateArray = checkerDateStr.split(" ");
                    if (checkerDateArray.length == 2) {
                        Date checkerDate = DateUtil.parseDateSlashed(checkerDateArray[1]);
                        cancelationRecord.setCheckerDate(
                                new java.sql.Date(checkerDate.getTime()));

                        if (cbStatus == null) {
                            if (smallestCheckerDate == null)
                                smallestCheckerDate = checkerDate;

                            if (checkerDate.before(smallestCheckerDate)) {
                                smallestCheckerDate = checkerDate;
                            }
                        }
                    }
                }

            } catch (NumberFormatException ex) {
                Logger.getLogger(BankDirectDebitCancelationFile.class.getName()).log(Level.SEVERE, null, ex);
                cancelationRecord.setErrorMessage("Number Parsing Exception: " + ex.getMessage());
            } catch (Exception ex) {
                Logger.getLogger(BankDirectDebitCancelationFile.class.getName()).log(Level.SEVERE, null, ex);
                cancelationRecord.setErrorMessage("Exception: " + ex.getMessage());
            } finally {
                if (cancelationRecord.getCbStatus() != null) {
                    cancelationRecord.setBankDirectDebitCancelationFile(this);
                    cancelationRecord.validateInsert();
                    records.add(cancelationRecord);
                }
            }
        }

        BankDirectDebitCancelationRecordRepository repo =
                Setup.getRepository(BankDirectDebitCancelationRecordRepository.class);
        repo.save(records);

        br.close();

        InterModuleConnector moduleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);
        Map<String, Object> map = new HashMap<>();
        map.put("id", this.getId());
        map.put("oldestPendingForCancellationDD", smallestCheckerDate);
        try {
            moduleConnector.postJsonAsync("accounting/bddcancelationfiles/update", map);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logger.getLogger(BankStatementFileController.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException("Problem with accounting bddcancelationfiles Update" + ex.getMessage());
        }

    }

    private String getCellValue(String value) {
        if (value.startsWith("\'"))
            return value.substring(1);
        return value;
    }

}
