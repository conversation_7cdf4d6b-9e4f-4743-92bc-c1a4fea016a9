package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.ContractPaymentConfirmationToDoService;
import com.magnamedia.service.DisableAccountingNotificationService;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Ma<PERSON>.Masod on 7/4/2021.
 */

@Entity
public class ContractPaymentConfirmationToDo extends BaseEntity {

    public enum Source implements LabelValueEnum {

        // added manually by user
        ERP("Online Credit Card Payment Reminders"),
        // paid by client in CC app or prospect app
        CC_APP("CC_APP"),
        OTHER("OTHER"),
        INITIAL_PAYMENTS("Initial Payment By Alternative Method Flow"),
        VISA_OVERSTAY_FEE("Visa Overstay Fee"),

        // paid via accounting flows
        AFTER_CASH_FLOW("Client Paid Cash No Signature Provided"),
        CLIENT_PAYING_VIA_Credit_Card("Client Paying Via Credit Card Flow"),
        ONE_MONTH_AGREEMENT("One Month Agreement"),
        BOUNCED_PAYMENT_FLOW("Bounced Payment Flow"),
        FAQ("FAQ"),
        SWITCH_NATIONALITY("Switch Nationality"),
        PAYMENT_REMINDER("Payment Reminder"),

        // refund flow
        CLIENT_REFUND("Client Refund"),

        // Recurring flow
        AUTHORIZATION("Authorization");

        private final String label;

        Source(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }

    public enum Purpose {
        CC_APP_ADD_NEW_CARD,
        CC_APP_RECURRING_CLIENT_INITIATED,
        NEW_GPT
    }

    @Transient
    private Contract contract;
    
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;
    
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem paymentType;

    //ACC-3863
    @Column
    private String typesOfPayments;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PaymentMethod paymentMethod;

    //CMA-2112 selected case: 1 for case B, 2 for case C, 3 for case D
    @Column
    private Integer selectedCase;

    @Column
    @Temporal(TemporalType.DATE)
    private Date expectedDate;

    @Column(columnDefinition = "boolean default false")
    private boolean payingOnline = Boolean.FALSE;

    @Column(columnDefinition = "boolean default false")
    private boolean replacementOfBouncedPayment = Boolean.FALSE;

    @Lob
    private String description;

    @Column(columnDefinition = "boolean default true")
    private boolean showOnERP = Boolean.TRUE;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmed = Boolean.FALSE;

    @OneToMany(mappedBy = "contractPaymentConfirmationToDo", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    List<ContractPaymentWrapper> contractPaymentList = new ArrayList();

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Source source;

    @Column
    private String transferReference;

    @Column
    private String payTabsResponseMessage;

    @Column
    private String checkoutResponseMessage;

    @Column
    @Lob
    @JsonIgnore
    private String creditCardInfo;

    @Column(columnDefinition = "boolean default false")
    private boolean notificationSent = Boolean.FALSE;

    @Transient
    private Long notificationId = null;

    @Transient
    private String oldContracts;

    private String authorizationCode;

    private String cleanAuthorizationCode;

    private boolean disabled = false;

    private boolean payingViaCreditCard = false;

    @Column(columnDefinition = "boolean default false")
    private boolean creditCardOffer = false;

    @Column(columnDefinition = "boolean default false")
    private boolean reactivationPayment = false;

    @Column(columnDefinition = "boolean default false")
    private boolean required = false;

    private Date cardPaymentReceivedDate;

    @Column
    private Long relatedEntityId;

    @Column
    private String relatedEntityType;


    @Column(columnDefinition = "boolean default false")
    private boolean ignoreSendNotification = false;

    @Enumerated(EnumType.STRING)
    @Column
    private Purpose purpose;

    @Transient
    private Map<String, Object> clientTodoInfo;

    public ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public PicklistItem getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PicklistItem paymentType) {
        this.paymentType = paymentType;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Integer getSelectedCase() {
        return selectedCase;
    }

    public void setSelectedCase(Integer selectedCase) {
        this.selectedCase = selectedCase;
    }

    public Date getExpectedDate() {
        return expectedDate;
    }

    public void setExpectedDate(Date expectedDate) {
        this.expectedDate = expectedDate;
    }

    public boolean isPayingOnline() {
        return payingOnline;
    }

    public void setPayingOnline(boolean payingOnline) {
        this.payingOnline = payingOnline;
    }

    public boolean isReplacementOfBouncedPayment() {
        return replacementOfBouncedPayment;
    }

    public void setReplacementOfBouncedPayment(boolean replacementOfBouncedPayment) {
        this.replacementOfBouncedPayment = replacementOfBouncedPayment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isShowOnERP() {
        return showOnERP;
    }

    public void setShowOnERP(boolean showOnERP) {
        this.showOnERP = showOnERP;
    }

    public boolean isConfirmed() {
        return confirmed;
    }

    public void setConfirmed(boolean confirmed) {
        this.confirmed = confirmed;
    }

    public List<ContractPaymentWrapper> getContractPaymentList() {
        return contractPaymentList;
    }

    public void setContractPaymentList(List<ContractPaymentWrapper> contractPaymentList) {
        this.contractPaymentList = contractPaymentList;
        if (this.contractPaymentList != null) {
            for (ContractPaymentWrapper contractPaymentWrapper : this.contractPaymentList) {
                contractPaymentWrapper.setContractPaymentConfirmationToDo(this);
            }
        }
    }

    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    public String getTransferReference() {
        return transferReference;
    }

    public void setTransferReference(String transferReference) {
        this.transferReference = transferReference;
    }

    public String getPayTabsResponseMessage() {
        return payTabsResponseMessage;
    }

    public void setPayTabsResponseMessage(String payTabsResponseMessage) {
        this.payTabsResponseMessage = payTabsResponseMessage;
    }

    public String getCheckoutResponseMessage() { return checkoutResponseMessage; }

    public void setCheckoutResponseMessage(String checkoutResponseMessage) {
        this.checkoutResponseMessage = checkoutResponseMessage;
    }

    public String getCreditCardInfo() {
        return creditCardInfo;
    }

    public void setCreditCardInfo(String creditCardInfo) {
        this.creditCardInfo = creditCardInfo;
    }
    
    public boolean isNotificationSent() {
        return notificationSent;
    }

    public void setNotificationSent(boolean notificationSent) {
        this.notificationSent = notificationSent;
    }
    
    public Double getTotalAmount() {
        if (contractPaymentList == null) return 0.0;

        return (!paymentMethod.equals(PaymentMethod.WIRE_TRANSFER))? contractPaymentList.stream()
                .mapToDouble(contractPayment -> contractPayment.getAmount()).sum():
                contractPaymentList.stream()
                .mapToDouble(contractPayment -> contractPayment.getActualReceivedAmount()).sum();
    }
    
    public Double getMainTotalAmount() {
        if (contractPaymentList == null) return 0.0;

        return contractPaymentList.stream()
                .mapToDouble(contractPayment -> contractPayment.getAmount()).sum();
    }

    public Long getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(Long notificationId) {
        this.notificationId = notificationId;
    }
    
    public String getTypesOfPayments() {
        return typesOfPayments;
    }

    @Basic(fetch = FetchType.LAZY)
    public String getTypesOfPaymentsLabels() {
        if (getTypesOfPayments() != null && !getTypesOfPayments().isEmpty()) {
            String[] payments = getTypesOfPayments().split(",");
            List<String> paymentsCodes = new ArrayList<>();
            for (String paymentTypeId: payments) {
                PicklistItem picklistItem = Setup.getRepository(PicklistItemRepository.class).findOne(Long.parseLong(paymentTypeId));
                if (picklistItem != null)
                    paymentsCodes.add(picklistItem.getName()) ;
            }
            if (!paymentsCodes.isEmpty())
                return String.join(", ", paymentsCodes);
        }
        return paymentType.getName();
    }

    public void setTypesOfPayments(String typesOfPayments) {
        this.typesOfPayments = typesOfPayments;
    }

    @BeforeDelete
    public void onDelete() {
        FlowProcessorEntityRepository flowRepository = Setup.getRepository(FlowProcessorEntityRepository.class);

        List<FlowProcessorEntity> reminderFlows = flowRepository
                .findByFlowEventConfig_NameAndContractPaymentConfirmationToDo(
                        FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS, this);

        for(FlowProcessorEntity f : reminderFlows) {
            flowRepository.delete(f);
        }

        Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .deleteDisabledToDoExistingPayment(this);
    }

    public String getAuthorizationCode() { return authorizationCode; }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
        setCleanAuthorizationCode(authorizationCode);
    }

    public String getCleanAuthorizationCode() { return cleanAuthorizationCode; }

    public void setCleanAuthorizationCode(String cleanAuthorizationCode) {
        this.cleanAuthorizationCode = cleanAuthorizationCode == null || cleanAuthorizationCode.isEmpty() ?
                null : cleanAuthorizationCode.replaceFirst("^0+(?!$)", "");
    }

    public boolean isDisabled() { return disabled; }

    public void setDisabled(boolean disabled) {
        boolean updatedToDisabled = disabled && !this.disabled;
        this.disabled = disabled;

        if (!updatedToDisabled) return;

        Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .deleteDisabledToDoExistingPayment(this);
    }

    public boolean isPayingViaCreditCard() { return payingViaCreditCard; }

    public void setPayingViaCreditCard(boolean payingViaCreditCard) { this.payingViaCreditCard = payingViaCreditCard; }

    @JsonIgnore
    public String getOldContracts() { return oldContracts; }

    public boolean isRequired() { return required; }

    public void setRequired(boolean required) { this.required = required; }

    public void setOldContracts(String oldContracts) { this.oldContracts = oldContracts; }

    public boolean isCreditCardOffer() { return creditCardOffer; }

    public void setCreditCardOffer(boolean creditCardOffer) { this.creditCardOffer = creditCardOffer; }

    public boolean isReactivationPayment() { return reactivationPayment; }

    public void setReactivationPayment(boolean reactivationPayment) { this.reactivationPayment = reactivationPayment; }

    public Date getCardPaymentReceivedDate() { return cardPaymentReceivedDate; }

    public void setCardPaymentReceivedDate(Date cardPaymentReceivedDate) {
        this.cardPaymentReceivedDate = cardPaymentReceivedDate;
    }

    public Long getRelatedEntityId() { return relatedEntityId; }

    public void setRelatedEntityId(Long relatedEntityId) { this.relatedEntityId = relatedEntityId; }

    public String getRelatedEntityType() { return relatedEntityType; }

    public void setRelatedEntityType(String relatedEntityType) { this.relatedEntityType = relatedEntityType; }

    public boolean isIgnoreSendNotification() { return ignoreSendNotification; }

    public void setIgnoreSendNotification(boolean ignoreSendNotification) { this.ignoreSendNotification = ignoreSendNotification; }

    public Purpose getPurpose() { return purpose; }

    public void setPurpose(Purpose purpose) { this.purpose = purpose; }

    public Map<String, Object> getClientTodoInfo() { return clientTodoInfo; }

    public void setClientTodoInfo(Map<String, Object> clientTodoInfo) { this.clientTodoInfo = clientTodoInfo; }
}