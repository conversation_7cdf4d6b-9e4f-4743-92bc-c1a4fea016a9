package com.magnamedia.entity.OnlineCardStatement;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.serializer.IdOnlySerializer;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

// ACC-5587
@Entity
public class OnlineCardStatementRecord extends BaseEntity {

    public enum OnlineCardStatementRecordStatus {
        MATCHED,
        UNMATCHED,
        MATCHED_REFUND,
        UNMATCHED_REFUND,
        CREATED_FOR_CHECKOUT_POS_TRANSACTION
    }

    public enum BreakdownType {
        CAPTURE, REFUND,
        AUTHORIZATION_FIXED_FEE, REFUND_FIXED_FEE,
        PREMIUM_VARIABLE_FEE, SCHEME_VARIABLE_FEE,
        SCHEME_FIXED_FEE, INTER_CHANGE_FIXED_FEE,
        NETWORK_TOKEN_UPDATE_FIXED_FEE, NETWORK_TOKEN_PROVISIONING_FIXED_FEE,

        // TAX
        REFUND_FIXED_FEE_TAX, AUTHORIZATION_FIXED_FEE_TAX,
        SCHEME_VARIABLE_FEE_TAX, SCHEME_FIXED_FEE_TAX,
        PREMIUM_VARIABLE_FEE_TAX, INTERCHANGE_FIXED_FEE_TAX,
        // ACC-8581
        NETWORK_TOKEN_UPDATE_FIXED_FEE_TAX, NETWORK_TOKEN_PROVISIONING_FIXED_FEE_TAX;

        public static BreakdownType getEnum(String s) {
            switch (s) {
                case "Capture" :
                    return CAPTURE;
                case "Refund" :
                    return REFUND;
                case "Authorization Fixed Fee" :
                    return AUTHORIZATION_FIXED_FEE;
                case "Refund Fixed Fee" :
                    return REFUND_FIXED_FEE;
                case "Premium Variable Fee" :
                    return PREMIUM_VARIABLE_FEE;
                case "Scheme Variable Fee" :
                    return SCHEME_VARIABLE_FEE;
                case "Scheme Fixed Fee" :
                    return SCHEME_FIXED_FEE;
                case "Interchange Fixed Fee" :
                    return INTER_CHANGE_FIXED_FEE;
                case "Network Token Update Fixed Fee" :
                    return NETWORK_TOKEN_UPDATE_FIXED_FEE;
                case "Network Token Provisioning Fixed Fee" :
                    return NETWORK_TOKEN_PROVISIONING_FIXED_FEE;

                // TAX
                case "Refund Fixed Fee Tax" :
                    return REFUND_FIXED_FEE_TAX;
                case "Authorization Fixed Fee Tax" :
                    return AUTHORIZATION_FIXED_FEE_TAX;
                case "Premium Variable Fee Tax" :
                    return PREMIUM_VARIABLE_FEE_TAX;
                case "Scheme Variable Fee Tax" :
                    return SCHEME_VARIABLE_FEE_TAX;
                case "Scheme Fixed Fee Tax" :
                    return SCHEME_FIXED_FEE_TAX;
                case "Interchange Fixed Fee Tax" :
                    return INTERCHANGE_FIXED_FEE_TAX;
                case "Network Token Update Fixed Fee Tax" :
                    return NETWORK_TOKEN_UPDATE_FIXED_FEE_TAX;
                case "Network Token Provisioning Fixed Fee Tax" :
                    return NETWORK_TOKEN_PROVISIONING_FIXED_FEE_TAX;
                default:
                    return null;
            }
        }
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private OnlineCardStatementFile onlineCardStatementFile;

    @OneToMany(mappedBy = "onlineCardStatementRecord", fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private List<OnlineCardStatementTransaction> onlineCardStatementTransactions;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private ContractPaymentConfirmationToDo contractPaymentConfirmationToDo;

    private Date paymentDate;
    private Double amount;
    private String authorizationCode;
    private String transferReference;
    @Enumerated(EnumType.STRING)
    private OnlineCardStatementRecordStatus status = OnlineCardStatementRecordStatus.UNMATCHED;
    private boolean matchedManually = false;

    @Enumerated(EnumType.STRING)
    private BreakdownType breakdownType;

    public OnlineCardStatementFile getOnlineCardStatementFile() { return onlineCardStatementFile; }

    public void setOnlineCardStatementFile(OnlineCardStatementFile onlineCardStatementFile) {
        this.onlineCardStatementFile = onlineCardStatementFile;
    }

    public List<OnlineCardStatementTransaction> getOnlineCardStatementTransactions() {
        return onlineCardStatementTransactions;
    }

    public void setOnlineCardStatementTransactions(List<OnlineCardStatementTransaction> onlineCardStatementTransactions) {
        this.onlineCardStatementTransactions = onlineCardStatementTransactions;
    }

    public String getTransferReference() { return transferReference; }

    public void setTransferReference(String transferReference) { this.transferReference = transferReference; }

    public ContractPaymentConfirmationToDo getContractPaymentConfirmationToDo() {
        return contractPaymentConfirmationToDo;
    }

    public void setContractPaymentConfirmationToDo(ContractPaymentConfirmationToDo contractPaymentConfirmationToDo) {
        this.contractPaymentConfirmationToDo = contractPaymentConfirmationToDo;
    }

    public Date getPaymentDate() { return paymentDate; }

    public void setPaymentDate(Date paymentDate) { this.paymentDate = paymentDate; }

    public Double getAmount() { return amount; }

    public void setAmount(Double amount) { this.amount = amount; }

    public String getAuthorizationCode() { return authorizationCode; }

    public void setAuthorizationCode(String authorizationCode) { this.authorizationCode = authorizationCode; }

    public OnlineCardStatementRecordStatus getStatus() { return status; }

    public void setStatus(OnlineCardStatementRecordStatus status) { this.status = status; }

    public boolean isMatchedManually() { return matchedManually; }

    public void setMatchedManually(boolean matchedManually) { this.matchedManually = matchedManually; }

    public BreakdownType getBreakdownType() { return breakdownType; }

    public void setBreakdownType(BreakdownType breakdownType) { this.breakdownType = breakdownType; }
}