package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import org.hibernate.envers.NotAudited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1227
 */

@Entity
public class HousemaidTransaction extends BaseEntity {
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Column
    private Double amount;

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof HousemaidTransaction)) {
            return false;
        } else {
            HousemaidTransaction other = (HousemaidTransaction) object;
            return this.getId().equals(other.getId())
                    && this.housemaid.getId().equals(other.housemaid.getId()) && this.transaction.getId().equals(other.transaction.getId());
        }
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Double getAmount() { return amount; }

    public void setAmount(Double amount) { this.amount = amount; }
}
