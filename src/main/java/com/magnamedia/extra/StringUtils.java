package com.magnamedia.extra;

import com.magnamedia.entity.Client;
import com.magnamedia.entity.Housemaid;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 10, 2020
 */

public class StringUtils {

    public static String vowels = "aeiou";

    public static boolean startsWithVowel(String text) {
        return vowels.indexOf(Character.toLowerCase(text.charAt(0))) != -1;
    }

    public static boolean isEmpty(String str) {
        return str == null || "".equals(str.trim());
    }

    public static String getHousemaidNationality(String name) {

        return (startsWithVowel(name) ? "an " : "a ") + name;
    }

    public static String getHousemaidName(Housemaid housemaid) {

        return housemaid != null && housemaid.getName() != null ?
                getNameOfHousemaid(housemaid.getName()) : "your maid";
    }

    public static String getHousemaidFirstName(Housemaid housemaid) {

        return housemaid != null && housemaid.getFirstName() != null ?
                getNameOfHousemaid(housemaid.getFirstName()) : "your maid";
    }

    public static String getNameOfHousemaid(String name) {

        return name.contains("unknown") ? "your maid" : name;
    }

    public static String getClientNicknameOrName(Client client) {

        return getClientNicknameOrName(client, true);
    }

    public static String getClientNicknameOrName(Client client, boolean withTitle) {

        return client != null ?
                ((withTitle && client.getTitle() != null ? client.getTitle().getName() + ". " : "") +
                        (client.getNickName() != null && !client.getNickName().isEmpty() ?
                                client.getNickName() :
                                client.getName() != null && !client.getName().isEmpty() ?
                                        client.getName() :
                                        "")):
                "";
    }

    public static String getClientNicknameOrFirstName(Client client) {

        return getClientNicknameOrFirstName(client, true);
    }

    public static String getClientNicknameOrFirstName(Client client, boolean withTitle) {

        return client != null ?
                client.getNickName() != null && !client.getNickName().isEmpty() ?
                        client.getNickName(withTitle) :
                        client.getName() != null && !client.getName().isEmpty() ?
                                client.getFirstName(withTitle) :
                                "":
                "";
    }

    public static String NormalizePhoneNumber(String number) {

        String NormalizedNumber = number;
        NormalizedNumber = NormalizedNumber.replace("-", "");
        NormalizedNumber = NormalizedNumber.replace("+", "");
        NormalizedNumber = NormalizedNumber.replace(")", "");
        NormalizedNumber = NormalizedNumber.replace("(", "");
        NormalizedNumber = NormalizedNumber.replace(" ", "");

        NormalizedNumber = removeFirst(NormalizedNumber, "00");

        if (NormalizedNumber.startsWith("0")) {
            NormalizedNumber = removeFirst(NormalizedNumber, "0");
            if (!NormalizedNumber.startsWith("971")) {
                NormalizedNumber = "971" + NormalizedNumber;
            }
        }
        if (NormalizedNumber.startsWith("9710")) {
            NormalizedNumber = removeFirst(NormalizedNumber, "9710");
            NormalizedNumber = "971" + NormalizedNumber;
        }
        return NormalizedNumber;
    }

    public static String removeFirst(String s, String toRemove) {
        if (s.startsWith(toRemove)) {
            s = s.substring(toRemove.length());
        }
        return s;
    }

    public static String getHousemaidNationalityOrLiveStatus(Housemaid housemaid, boolean switchSameNationality) {

        return switchSameNationality ?
                    "a " + (housemaid.getLiveOut() ? "live-out" : "live-in") :
                getHousemaidNationality(housemaid.getNationality().getName()) +
                            (housemaid.getLiveOut() ? " - live out " : "");
    }
}
