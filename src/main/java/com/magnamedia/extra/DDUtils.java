package com.magnamedia.extra;

import com.google.api.client.util.ArrayMap;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import com.magnamedia.service.OneMonthAgreementFlowService;
import com.magnamedia.service.PaymentService;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Months;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Nov 07, 2020
 * 
 */

public class DDUtils {
    private final static Logger logger = Logger.getLogger(DDUtils.class.getName());

    public static void setDirectDebitsDescription(ContractPaymentTerm cpt, List<DirectDebit> dds) {
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);

        if (dds == null) return;
        Contract contract = cpt.getContract();
        dds = dds.stream().sorted(Comparator.comparing(DirectDebit::getStartDate)).collect(Collectors.toList());

        logger.log(Level.SEVERE, "prospect type" + contract.getContractProspectType().getCode());
        logger.log(Level.SEVERE, "is prorated" + contract.getIsProRated());
        logger.log(Level.SEVERE, "discount effective after" + cpt.getDiscountEffectiveAfter());
        logger.log(Level.SEVERE, "DDs size" + dds.size());


        List<DirectDebit> monthlyDD = dds.stream().filter(
                dd -> !dd.getType().equals(DirectDebitType.ONE_TIME)).collect(Collectors.toList());

        if (contract.getContractProspectType().getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))
                && contract.getIsProRated() && cpt.getDiscountEffectiveAfter() == 12 && monthlyDD.size() == 2 &&
                !directDebitRepository.existsByContractPaymentTermAndStatusAndStartDateGreaterThan(cpt, DirectDebitStatus.CONFIRMED, monthlyDD.get(1).getStartDate())
                && (Months.monthsBetween(new DateTime(monthlyDD.get(0).getStartDate()), new DateTime(monthlyDD.get(0).getExpiryDate())).getMonths() + 1) == 12) {

            monthlyDD.get(0).setDescription("1st year starting " + DateUtil.formatDayNumber(dds.get(0).getStartDate()) + " " + DateUtil.formatSimpleMonth(dds.get(0).getStartDate()));

            monthlyDD.get(1).setDescription("2nd year & thereafter");

            return;
        }

        Iterator<DirectDebit> it = dds.iterator();

        while (it.hasNext()) {
            DirectDebit dd = it.next();
            if (contract.getContractProspectType().getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {

                if (dd.getType().equals(DirectDebitType.ONE_TIME)) {
                    dd.setDescription(DateUtil.formatNotDashedFullDate(dd.getStartDate()));
                    continue;
                }

                if (!it.hasNext() && !directDebitRepository.existsByContractPaymentTermAndStatusAndStartDateGreaterThan(cpt, DirectDebitStatus.CONFIRMED, dd.getStartDate())) {
                    dd.setDescription(DateUtil.formatDayNumber(dd.getStartDate()) + " " + DateUtil.formatSimpleMonth(dd.getStartDate()) + " & thereafter");
                    continue;
                }

                if (!dd.getType().equals(DirectDebitType.ONE_TIME)) {
                    if (!it.hasNext())
                        dd.setDescription(DateUtil.formatNotDashedFullDate(dd.getStartDate()) + " and onwards");
                    else
                        dd.setDescription(DateUtil.formatNotDashedFullDate(dd.getStartDate()) + " till " + DateUtil.formatNotDashedFullDate(dd.getExpiryDate()));
                }
            }

            if (contract.getContractProspectType().getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                boolean isNewContract = !directDebitRepository.existsByContractPaymentTerm_Contract(contract);

                if (dd.getType().equals(DirectDebitType.ONE_TIME)) {
                    dd.setDescription(DateUtil.formatNotDashedFullDate(dd.getStartDate()));
                    continue;
                }

                if (!it.hasNext() && isNewContract) {
                    dd.setDescription("Then on the 1st of each month");
                    continue;
                }

                if (!it.hasNext())
                    dd.setDescription(DateUtil.formatNotDashedFullDate(dd.getStartDate()) + " and onwards");
                else dd.setDescription(DateUtil.formatNotDashedFullDate(dd.getStartDate()) +
                        " till " + DateUtil.formatNotDashedFullDate(dd.getExpiryDate()));

            }
        }
    }

    public static String getSignPaymentInfoForCcApp(
            List<DirectDebit> directDebits,
            ContractPaymentTerm cpt) {

        if (directDebits.isEmpty()) return "";

        DDUtils.setAllDirectDebitsDescriptionForCCAPP(cpt, directDebits);

        boolean clientPayingVat = cpt.getContract().getClientPaidVat();
        StringBuilder oneTimeDesc = new StringBuilder();
        StringBuilder directDebitDesc = new StringBuilder();
        for (DirectDebit directDebit : directDebits) {
            if (directDebit.getType() == DirectDebitType.ONE_TIME) {
                List<ContractPayment> payments = directDebit.getPayments();
                if (payments != null && !payments.isEmpty()) {
                    for (ContractPayment payment : payments) {

                        oneTimeDesc.append(PaymentHelper.getPaymentDescriptionForCCAPP(payment)).append(":")
                                .append(PaymentHelper.getCC_APP_PaymentVatDescription(payment, clientPayingVat))
                                .append("</br>");
                    }
                }
            } else if (directDebit.getType() == DirectDebitType.MONTHLY) {
                List<ContractPayment> payments = directDebit.getPayments();
                if (payments != null && !payments.isEmpty()) {
                    ContractPayment payment = payments.get(0);

                    directDebitDesc.append(directDebit.getDescription()).append(":")
                            .append(PaymentHelper.getCC_APP_PaymentVatDescription(payment, clientPayingVat))
                            .append("</br>");
                }
            }
        }

        oneTimeDesc.append(directDebitDesc);
        return oneTimeDesc.toString();
    }

    public static void setAllDirectDebitsDescriptionForCCAPP(ContractPaymentTerm cpt, List<DirectDebit> dds) {
        if (dds == null) return;

        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        Contract contract = cpt.getContract();
        dds = dds.stream().sorted(Comparator.comparing(DirectDebit::getStartDate)).collect(Collectors.toList());

        logger.log(Level.SEVERE, "prospect type" + contract.getContractProspectType().getCode());
        logger.log(Level.SEVERE, "is prorated" + contract.getIsProRated());
        logger.log(Level.SEVERE, "discount effective after" + cpt.getDiscountEffectiveAfter());
        logger.log(Level.SEVERE, "DDs size" + dds.size());


        List<DirectDebit> monthlyDD = dds.stream().filter(
                dd -> !dd.getType().equals(DirectDebitType.ONE_TIME)).collect(Collectors.toList());

        if (contract.getContractProspectType().getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))
                && contract.getIsProRated() && cpt.getDiscountEffectiveAfter() == 12 && monthlyDD.size() == 2 &&
                !directDebitRepository.existsByContractPaymentTermAndStatusAndStartDateGreaterThan(cpt, DirectDebitStatus.CONFIRMED, monthlyDD.get(1).getStartDate())
                && (Months.monthsBetween(new DateTime(monthlyDD.get(0).getStartDate()), new DateTime(monthlyDD.get(0).getExpiryDate())).getMonths() + 1) == 12) {

            monthlyDD.get(0).setDescription("1st year starting " + DateUtil.formatCCAPPDate(dds.get(0).getStartDate()));

            monthlyDD.get(1).setDescription("2nd year & onwards");

            return;
        }

        Iterator<DirectDebit> it = dds.iterator();

        while (it.hasNext()) {
            DirectDebit dd = it.next();
            if (contract.getContractProspectType().getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {

                if (dd.getType().equals(DirectDebitType.ONE_TIME)) {
                    dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()));
                    continue;
                }

                if (!it.hasNext() && !directDebitRepository.existsByContractPaymentTermAndStatusAndStartDateGreaterThan(cpt, DirectDebitStatus.CONFIRMED, dd.getStartDate())) {
                    dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " & onwards");
                    continue;
                }

                if (!dd.getType().equals(DirectDebitType.ONE_TIME)) {
                    if (!it.hasNext())
                        dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " and onwards");
                    else dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " till " + DateUtil.formatCCAPPDate(dd.getExpiryDate()));
                }
            }

            if (contract.getContractProspectType().getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                boolean isNewContract = !directDebitRepository.existsByContractPaymentTerm_Contract(contract);


                if (!it.hasNext() && isNewContract) {
                    dd.setDescription("Then on the 1st of each month");
                    continue;
                }

                if (isNewContract) {
                    dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()));
                } else {
                    if (!it.hasNext())
                        dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " and onwards");
                    else dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " till " + DateUtil.formatCCAPPDate(dd.getExpiryDate()));
                }
            }
        }
    }

    public static void setInitialDirectDebitsDescriptionForCCAPP(ContractPaymentTerm cpt, List<DirectDebit> dds) {
        if (dds == null) return;
        Contract contract = cpt.getContract();
        dds = dds.stream().sorted(Comparator.comparing(DirectDebit::getStartDate)).collect(Collectors.toList());

        logger.log(Level.SEVERE, "prospect type" + contract.getContractProspectType().getCode());
        logger.log(Level.SEVERE, "is prorated" + contract.getIsProRated());
        logger.log(Level.SEVERE, "discount effective after" + cpt.getDiscountEffectiveAfter());
        logger.log(Level.SEVERE, "DDs size" + dds.size());

        Iterator<DirectDebit> it = dds.iterator();

        while (it.hasNext()) {
            DirectDebit dd = it.next();
            if (dd.getType().equals(DirectDebitType.ONE_TIME)) {
                dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()));
                continue;
            }

            if (!it.hasNext()) {
                dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " and onwards");
                continue;
            }
            
            dd.setDescription(DateUtil.formatCCAPPDate(dd.getStartDate()) + " till " + DateUtil.formatCCAPPDate(dd.getExpiryDate()));
        }
    }


    public static String getDirectDebitDescriptionForCCAPP_SwitchMaid_One_Time(Date ddStartDate, Long ddAmountWithoutVat) {

        String description = "";

        description += DateUtil.formatDayNumber(ddStartDate) + " " + DateUtil.formatSimpleMonth(ddStartDate);
        description += " onwards: AED " + ddAmountWithoutVat + "/month";

        return description;
    }

    public static String getDirectDebitDescriptionForCCAPP_SwitchMaid_Monthly(ContractPaymentTerm newCPT, Date ddStartDate, Date ddExpiryDate, Long ddAmountWithoutVat,
                                                                              Long oldDDAmount, boolean clientPaidVat) {


        Date today = new DateTime().withTimeAtStartOfDay().toDate();
        DateTime ddStartDateTime = new DateTime(ddStartDate);
        DateTime ddExpiryDateTime = new DateTime(ddExpiryDate);

        if (ddStartDateTime.isBefore(today.getTime())) {
            ddStartDateTime = new DateTime(today).plusMonths(1).dayOfMonth().withMinimumValue();
            ddStartDate = ddStartDateTime.toDate();
        }

        String description = "";
        if (ddStartDateTime.getYear() == ddExpiryDateTime.getYear() && ddStartDateTime.getMonthOfYear() == ddExpiryDateTime.getMonthOfYear()) {
            description += DateUtil.formatCCAPPDate(ddStartDate) + ": ";
        } else {
            description += DateUtil.formatCCAPPDate(ddStartDate) + " till " + DateUtil.formatCCAPPDate(ddExpiryDate) + ": ";
        }

        description += "AED";

        if (ddAmountWithoutVat < oldDDAmount) {
            description += "<span style='color:red;text-decoration:line-through'>" +
                    "  <span style='color:black'> " + oldDDAmount + "</span></span>";
        }

        description += " " + ddAmountWithoutVat + "/month";
        if (clientPaidVat) {
            description += " + VAT";
        }

        return description;
    }

    public static String getDirectDebitDescriptionForCCAPP_SwitchMaid_Last_Monthly_DD(ContractPaymentTerm newCPT, Date ddStartDate, Long ddAmountWithoutVat) {


        if (ddStartDate.before(new Date())) {
            ddStartDate = new DateTime().plusMonths(1).dayOfMonth().withMinimumValue().toDate();
        }

        String description = "<b>";
        //description += DateUtil.formatDayNumber(ddStartDate) + " " + DateUtil.formatSimpleMonth(ddStartDate);
        description += "AED " + ddAmountWithoutVat + "/month</b>";

        return description;
    }

    public static String getDirectDebitsDescriptionForCCAPP_SwitchMaid(
            ContractPaymentTerm newCPT, ContractPaymentTerm oldCPT,
            List<DirectDebit> dds, String newNationalityName) {

        if (dds == null) return "";

        boolean isPayingViaCreditCard = oldCPT.getContract().isPayingViaCreditCard() ||
                (oldCPT.getContract().isOneMonthAgreement() &&
                        Setup.getApplicationContext()
                                .getBean(OneMonthAgreementFlowService.class)
                                .isPayingViaCreditCard(oldCPT.getContract()));

        String description = isPayingViaCreditCard ?
                "Your new payments will be:<br/>" :
                "Your monthly bank payment form will be:<br/>";

        dds = mergeDDs(dds);
        dds = dds.stream().sorted(Comparator.comparing(DirectDebit::getStartDate)).filter(x -> x.getType().equals(DirectDebitType.MONTHLY)).collect(Collectors.toList());

        logger.log(Level.SEVERE, "DDs size" + dds.size());

        if (dds.size() > 0) {
            DirectDebit dd = dds.get(0);

            double ddAmount = DiscountsWithVatHelper.getAmountWithoutVat(dd.getAmount());
            logger.info("Start Simulating Generating DDS: dd Amount " + dd.getAmount());
            logger.info("Start Simulating Generating DDS: AmountWithoutVat " + ddAmount);
            //Long ddAmountWithoutVat = Math.round(clientPaidVat ? Utils.getAmountWithoutVat(dd.getAmount()) : dd.getAmount());
            Long ddAmountWithoutVat = Math.round(Math.floor(ddAmount));

            description += getDirectDebitDescriptionForCCAPP_SwitchMaid_Last_Monthly_DD(newCPT, dd.getStartDate(), ddAmountWithoutVat);

            if (!newNationalityName.toLowerCase().contains("indonesian") && !newNationalityName.toLowerCase().contains("indian"))
                description += "<a href=\"popup://\"><img src=\"asset:assets/icons/new_i_design.png\" width=\"16\" height=\"16\"></a>";
        }

        return description;
    }

    public static LinkedHashMap getPaymentsBreakdowns(
            PaymentTermConfig ptc, Boolean isWeekly,
            Boolean isLiveOut, List<DirectDebit> dds, Contract c) {

        LinkedHashMap paymentBreakdowns = PaymentReceiptHelper.getPaymentBreakdownsFromSales(ptc, isWeekly, isLiveOut);

        Long amountWithoutVat = 0l;
        Long totalAmountFromSales = 0l;
        if (dds != null) {

            dds = mergeDDs(dds);
            dds = dds.stream().sorted(Comparator.comparing(DirectDebit::getStartDate)).filter(x -> x.getType().equals(DirectDebitType.MONTHLY)).collect(Collectors.toList());

            logger.log(Level.SEVERE, "DDs size" + dds.size());

            if (dds.size() > 0) {
                DirectDebit dd = dds.get(0);
                amountWithoutVat = Math.round(Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(dd.getAmount())));
                //Long amountWithoutVat = Math.round(clientPaidVat ? Utils.getAmountWithoutVat(dd.getAmount()) : dd.getAmount());
                logger.log(Level.SEVERE, "Start Simulating Generating DDS: Utils.getAmountWithoutVat(dd.getAmount()) " + amountWithoutVat);
                logger.log(Level.SEVERE, "Start Simulating Generating DDS: dd.getAmount() " + dd.getAmount());
            }
        }

        if (paymentBreakdowns != null && paymentBreakdowns.containsKey("totalAmount") && paymentBreakdowns.get("totalAmount") != null) {
            if (isWeekly) {
                Long weeklyAmountWithoutVat = 0l;
                double weeklyAmount = ptc.getWeeklyAmount();
                double weeklyAmountFeeFromSales = paymentBreakdowns.containsKey("weeklyAmount") && paymentBreakdowns.get("weeklyAmount") != null ? Double.parseDouble(paymentBreakdowns.get("weeklyAmount").toString()) : 0.0;
                weeklyAmountWithoutVat = Math.round(Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(weeklyAmount) + weeklyAmountFeeFromSales));
                paymentBreakdowns.put("weeklyAmount", weeklyAmountWithoutVat);
            }
            totalAmountFromSales = Long.parseLong(paymentBreakdowns.get("totalAmount").toString());
        }
        Double liveOutAmount = paymentBreakdowns.containsKey("liveOutAmount") ? Double.parseDouble(String.valueOf(paymentBreakdowns.get("liveOutAmount"))) : 0D;
        String liveOutAllowenceNote = "";
        if (isLiveOut) {
            Map<String, String> parameters = new HashMap<>();
            parameters.put("live_out_amount", String.valueOf(liveOutAmount.intValue()));
            liveOutAllowenceNote = TemplateUtil.compileTemplate(CcAppCmsTemplate.LIVE_OUT_ALLOWENCE_NOTE.toString(), c, parameters);
        }

        paymentBreakdowns.put("flexiblePackage", "AED " + (amountWithoutVat - totalAmountFromSales));
        paymentBreakdowns.put("totalAmount", amountWithoutVat);
        paymentBreakdowns.put("liveOutAmount", liveOutAmount);
        paymentBreakdowns.put("flexiblePackageNote",
                TemplateUtil.compileTemplate(CcAppCmsTemplate.FLEXIBLE_PACKAGE_NOTE.toString(), c, new HashMap<>()));

        paymentBreakdowns.put("liveOutAllowenceNote", liveOutAllowenceNote);
        return paymentBreakdowns;
    }

    public static String getDirectDebitsDescriptionForCCAPP_SwitchMaidForWeekly(double weeklyAmount, String oldNationalityName, String newNationalityName) {

        String description = "";
        if (newNationalityName.equalsIgnoreCase("filipina") && !oldNationalityName.equalsIgnoreCase("filipina")) {
            description += newNationalityName + " maids are only available for weekly contracts.<br>";

            logger.log(Level.SEVERE, "---weeklyAmount:  " + weeklyAmount);
            Long weeklyAmountWithoutVat = Math.round(Math.floor(weeklyAmount));
            description += "Your new payments will be:<br><br>";
            description += "<b>AED " + weeklyAmountWithoutVat + "/week (billed monthly)<b/>";

            if (!newNationalityName.toLowerCase().contains("indonesian") && !newNationalityName.toLowerCase().contains("indian"))
                description += "<a href=\"popup://\"><img src=\"asset:assets/icons/new_i_design.png\" width=\"16\" height=\"16\"></a>";

        }

        return description;
    }

    public static Map<String, Map> getPricingDifferenceForCCAPP_SwitchMaid(ContractPaymentTerm oldCPT, List<DirectDebit> dds, boolean withoutOneTime, boolean clientPaidVat) {
        Map<String, Map> pricingDifferenceList = new LinkedHashMap();

        logger.log(Level.SEVERE, "DDs size" + dds.size());

        dds = mergeDDs(dds);
        dds = dds.stream().sorted(Comparator.comparing(DirectDebit::getStartDate)).collect(Collectors.toList());

        logger.log(Level.SEVERE, "DDs size" + dds.size());

        Iterator<DirectDebit> it = dds.iterator();

        int index = 1;
        while (it.hasNext()) {
            DirectDebit dd = it.next();

            if (withoutOneTime && dd.getType().equals(DirectDebitType.ONE_TIME)) continue;

            Long ddAmountWithoutVat = clientPaidVat ? DiscountsWithVatHelper.getAmountWithoutVat(dd.getAmount()).longValue() : Math.round(dd.getAmount());

            CalculateDiscountsWithVatService calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);
            Double oldDDAmount = calculateDiscountsWithVatService.getCPTAmountAtTime(oldCPT, dd.getStartDate());
            if (clientPaidVat) {
                oldDDAmount = DiscountsWithVatHelper.getAmountWithoutVat(oldDDAmount);
            }

            if (!ddAmountWithoutVat.equals(oldDDAmount.longValue())) {
                Map<String, Object> pricingDifferenceMap = new HashMap<>();
                pricingDifferenceMap.put("oldPrice", oldDDAmount.longValue());
                pricingDifferenceMap.put("newPrice", ddAmountWithoutVat);
                pricingDifferenceMap.put("fromDate", dd.getStartDate());
                pricingDifferenceMap.put("toDate", dd.getExpiryDate());
                pricingDifferenceMap.put("ddType", dd.getType());

                pricingDifferenceList.put("period" + index++, pricingDifferenceMap);
            }
        }

        return pricingDifferenceList;
    }

    public static Map<String, String> getSwitchMaidTerms_OneTime(DateTime replacementDate, Double proratedRate, Double nextMonthNew, Double nextMonthOld,
                                                                 Date date,
                                                                 String newNationalityName, String oldNationalityName, boolean hasNotCompletedReplacement) {
        Map<String, String> terms = new LinkedHashMap();
        if ((nextMonthNew - nextMonthOld) < 1.0 && proratedRate < 1L) return terms;

        String label = "", value = "<ul style='padding: 0'>";


        label += "On " + DateUtil.formatMonthDayOfMonthWithSuffix(date);
        label += ":";

        if ((nextMonthNew - nextMonthOld) >= 1.0) {
            value += "<li>";
            value += "AED " + String.format("%.0f", nextMonthNew - nextMonthOld) + " + VAT, one time fee to cover the difference between the price of hiring "
                    + StringUtils.getHousemaidNationality(newNationalityName)
                    + " and " + StringUtils.getHousemaidNationality(oldNationalityName)
                    + " maid (AED " + String.format("%.0f", nextMonthNew) + " - " + String.format("%.0f", nextMonthOld) + ").<br/>"
                    + "This will cover your required payments for the month of " + DateUtil.formatMonth(date) + ".";
            value += "</li>";
        }

        logger.info("Replacement Date: " + replacementDate);
        int numOfDaysToEOF = getDaysToEoM(replacementDate);
        Long proratedAmountAsLong = Math.round(proratedRate * numOfDaysToEOF);

        logger.info("Replacement Date: " + replacementDate);
        String startDateText = hasNotCompletedReplacement ? DateUtil.formatMonthDayOfMonthWithSuffix(replacementDate.toDate()) : "today";
        String endDateText = hasNotCompletedReplacement ? DateUtil.formatMonthDayOfMonthWithSuffix(replacementDate.dayOfMonth().withMaximumValue().toDate()) : "the end of the month";

        if (proratedAmountAsLong >= 1L) {
            value += "<li>";
            value += "AED " + proratedAmountAsLong + " + VAT, one time fee to cover the period between " + startDateText + " and " + endDateText + " (" + numOfDaysToEOF + " days * daily rate of AED "
                    + String.format("%.1f", proratedRate) + ")";
            value += "</li>";
        }

        value += "</ul>";
        terms.put(label, value);

        logger.info("Result: " + System.lineSeparator());
        terms.entrySet().forEach(entry -> logger.info(entry.getKey() + " " + entry.getValue()));

        return terms;
    }

    public static String getSwitchMaidTerms_OneTime_NewDesign(
            DateTime replacementDate, Double proratedRate, Double nextMonthNew, Double nextMonthOld,
            Date date, String newNationalityName, boolean isLiveOut) {
        if ((nextMonthNew - nextMonthOld) < 1.0 && proratedRate < 1L) return "";

        String label = "";

        label +="<div class=\"row-content\">" +
                "<div class=\"label-container\">" +
                "<p class=\"content-paragraph-label\">" ;

        label += "On " + DateUtil.formatMonthDayOfMonthWithSuffix(date);
        label += ":</p> </div>" +
                "<div class=\"value-container\">";
        int numOfDaysToEOF = getDaysToEoM(replacementDate);
        Long proratedAmountAsLong = Math.round(proratedRate * numOfDaysToEOF);

        if (proratedAmountAsLong >= 1L) {
            label += "<div class=\"value-content\">" +
                    "<p class=\"currency-paragraph\">" +
                    "<span class=\"currency-span\" >AED</span> <span>" +
                    PaymentHelper.df.format(proratedAmountAsLong.intValue()) + " + VAT</span></p> " +
                    "<p class=\"value-paragraph\" >" +
                    "This one time fee will cover the difference in the daily rate for hiring " +
                    StringUtils.getHousemaidNationality(newNationalityName) +
                    (isLiveOut ? " live-out" : "") + " maid " +
                    "for the the last " + numOfDaysToEOF + " days of "+ DateUtil.formatMonth(replacementDate.toDate())  +
                    "</p> </div>";
        }

        logger.info("Replacement Date: " + replacementDate);

        if ((nextMonthNew - nextMonthOld) >= 1.0){

            label +="<div class=\"value-content\">" +
                    "<p class=\"currency-paragraph\">" +
                    "<span class=\"currency-span\">" +
                    "AED</span> <span>" +
                    PaymentHelper.df.format(nextMonthNew.intValue() - nextMonthOld.intValue()) + " + VAT </span></p>" +
                    "<p class=\"value-paragraph\">" +
                    "This one time fee will cover the difference in hiring " +
                    StringUtils.getHousemaidNationality(newNationalityName) +
                    (isLiveOut ? " live-out" : "") + " maid for the month of  " + DateUtil.formatMonth(date) +
                    "</p> </div>";
        }

        label += "</div></div>";

        logger.info("Result: "  + label);

        return label;
    }

    public static int getDaysToEoM(DateTime fromDate) {
        DateTime eomDate = fromDate.dayOfMonth().withMaximumValue();
        int daysToEOM = Days.daysBetween(fromDate, eomDate).getDays() + 1;
        return daysToEOM;
    }

    public static Map<String, String> getSwitchMaidTerms_Monthly(Map<String, Map> pricingMap, DateTime replacementDate, String newNationalityName) {
        Map<String, String> terms = new LinkedHashMap();

        Iterator<String> it = pricingMap.keySet().iterator();

        while (it.hasNext()) {
            String key = it.next();

            Map pricingDiff = pricingMap.get(key);
            Long newPrice = (Long) pricingDiff.get("newPrice");
            Date fromDate = (Date) pricingDiff.get("fromDate");
            Date toDate = (Date) pricingDiff.get("toDate");
            DirectDebitType ddType = (DirectDebitType) pricingDiff.get("ddType");

            Date nextOfNextMonthOfReplacement = replacementDate.plusMonths(2).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate();
            if (fromDate.before(nextOfNextMonthOfReplacement)) {
                if (toDate.before(nextOfNextMonthOfReplacement)) continue;

                fromDate = nextOfNextMonthOfReplacement;
            }

            String label = "", value = "<ul style='padding: 0'>";

            if (ddType.equals(DirectDebitType.MONTHLY)) {
                DateTime dateTimeFrom = new DateTime(fromDate);
                DateTime dateTimeTo = new DateTime(toDate);

                label = DateUtil.formatMonth(fromDate) + " " + DateUtil.formatDayNumber(fromDate).replaceFirst("^0+(?!$)", "") + DateUtil.getOrdinalSuffix(dateTimeFrom.getDayOfMonth());

                if (it.hasNext()) {
                    label += " to " + DateUtil.formatMonth(toDate) + " " + DateUtil.formatDayNumber(toDate).replaceFirst("^0+(?!$)", "") + DateUtil.getOrdinalSuffix(dateTimeTo.getDayOfMonth());
                } else {
                    label += " Onwards";
                }

                label += ":";

                value += "<li>";
                value += "AED " + newPrice + "/month + VAT. This is the monthly rate of hiring " +
                        StringUtils.getHousemaidNationality(newNationalityName) + " maid.";
                value += "</li>";
            }

            value += "</ul>";
            terms.put(label, value);
        }

        return terms;
    }

    public static String getSwitchMaidTerms_Monthly_NewDesign(
            Map<String, Map> pricingMap, DateTime replacementDate, String newNationalityName, boolean isLiveOut) {
        StringBuilder result = new StringBuilder();
        Iterator<String> it = pricingMap.keySet().iterator();

        while (it.hasNext()) {
            String key = it.next();

            Map pricingDiff = pricingMap.get(key);
            Long newPrice = (Long) pricingDiff.get("newPrice");
            Date fromDate = (Date) pricingDiff.get("fromDate");
            Date toDate = (Date) pricingDiff.get("toDate");
            DirectDebitType ddType = (DirectDebitType) pricingDiff.get("ddType");

            Date nextOfNextMonthOfReplacement = replacementDate.plusMonths(2).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate();
            if (fromDate.before(nextOfNextMonthOfReplacement)) {
                if (toDate.before(nextOfNextMonthOfReplacement)) continue;

                fromDate = nextOfNextMonthOfReplacement;
            }

            String label = "";

            if (ddType.equals(DirectDebitType.MONTHLY)) {
                DateTime dateTimeFrom = new DateTime(fromDate);
                DateTime dateTimeTo = new DateTime(toDate);

                    label = "<div class=\"row-content\">" +
                        "<div class=\"label-container\" >" +
                        "<p class=\"content-paragraph-label\">";

                label += "From " + DateUtil.formatMonth(fromDate) + " " + DateUtil.formatDayNumber(fromDate).replaceFirst("^0+(?!$)", "") + DateUtil.getOrdinalSuffix(dateTimeFrom.getDayOfMonth());

                if (it.hasNext()) {
                    label += " to " + DateUtil.formatMonth(toDate) + " " + DateUtil.formatDayNumber(toDate).replaceFirst("^0+(?!$)", "") + DateUtil.getOrdinalSuffix(dateTimeTo.getDayOfMonth());
                } else {
                    label += " Onwards";
                }

                label += ":</p></div>"+
                        "<div class=\"value-container\">" +
                        "<div class=\"value-content\">" +
                        "<p  class=\"currency-paragraph\">" +
                        "<span class=\"currency-span last\" >AED</span> <span>" +
                        PaymentHelper.df.format(newPrice.intValue()) + "/month + VAT</span></p> " +
                        "<p class=\"value-paragraph\">" +
                        "The monthly rate of hiring " +
                        StringUtils.getHousemaidNationality(newNationalityName) +
                        (isLiveOut ? " live-out" : "") + " maid" +
                        "</p></div></div></div>";
                result.append(label);
            }
        }

        return result.toString();
    }

    public static String getSwitchMaidTermsAmendDDNoteLiveOutNewDesign(boolean isLiveOut) {

        boolean showNote = Boolean.parseBoolean(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_SHOW_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE));

        if (!isLiveOut || !showNote) {
            return "";
        }

        String text = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_TEXT_IN_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE);

        String label =
            "<div class=\"row-content\">" +
                "<p class=\"note-paragraph\">" +
                    "<strong>" +
                        "Note: " +
                    "</strong>" +
                    text +
                "</p>" +
            "</div>";

        return label;
    }

    public static String getAmendDdFormattedDate(DateTime d) {
        return DateUtil.formatMonth(d.toDate()) + " " +
                DateUtil.formatDayNumber(d.toDate()).replaceFirst("^0+(?!$)", "") +
                DateUtil.getOrdinalSuffix(d.getDayOfMonth());
    }

    /*public static String getPaymentTermsForNewContract(ContractPaymentTerm cpt) {
        String text = "";
        DateTime contractStartDate = cpt.getContract().getStartOfContract() != null ?
                new DateTime(cpt.getContract().getStartOfContract()) : DateTime.now();
        List<ContractPaymentType> contractPaymentTypes = cpt.getContractPaymentTypes();

        ContractPaymentType type = contractPaymentTypes.stream().filter(
                        item -> item.getType().getCode().equals(PaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                .findFirst().orElse(null);
        boolean isProrated = type != null &&
                BooleanUtils.toBoolean(cpt.getContract().getIsProRated()) &&
                type.getStartsOn().equals(0) && cpt.getFirstMonthPayment() > 0;
        boolean isProratedPlusMonth = type != null &&
                BooleanUtils.toBoolean(cpt.getContract().getProRatedPlusMonth());

        // prorated payment
        if (isProrated || isProratedPlusMonth) {
            String frequency = "One Time Payment ";
            String startsOn = getCommencesOn(contractStartDate, contractStartDate);
            Double amount = cpt.getFirstMonthPayment();
            if (isProratedPlusMonth) {
                Map<String, Object> map = new HashMap<>();
                map.put("periodicalAdditionalDiscount", cpt.getPeriodicalAdditionalDiscount());
                map.put("affectedByAdditionalDiscount", type.getAffectedByAdditionalDiscount());
                map.put("additionalDiscountMonths", cpt.getAdditionalDiscountMonths());
                map.put("paymentsDuration", cpt.getContract().getPaymentsDuration());
                map.put("monthlyPaymentDiscount", type.getDiscount());
                map.put("monthlyPaymentDiscountEffectiveAfter", type.getDiscountEffectiveAfter());
                map.put("additionalDiscount", cpt.getAdditionalDiscount());
                map.put("firstMonthPayment", cpt.getFirstMonthPayment());
                map.put("monthlyPaymentAmount", type.getAmount());
                amount = PaymentReceiptHelper.getProPlusMonthPaymentAmount(map).get("amount");

            }
            text += getFormattedPaymentTermForNewContract(type.getDescription(),
                    DiscountsWithVatHelper.getAmountWithoutVat(amount), frequency, startsOn);
        }

        for (ContractPaymentType contractPaymentType : contractPaymentTypes) {
            boolean isMonthlyPayment = contractPaymentType.getType().getCode()
                    .equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
            DateTime commencesOnDate = new DateTime(contractStartDate).plusMonths(contractPaymentType.getStartsOn());

            if (isMonthlyPayment) {
                int shiftMonths = isProratedPlusMonth ? 2 : isProrated ? 1 : 0;
                commencesOnDate = commencesOnDate.plusMonths(shiftMonths);
            }

            text += getPaymentTermForNewContract(cpt, contractStartDate, contractPaymentType, commencesOnDate);
        }

        return text;
    }*/

   /* public static String getPaymentTermForNewContract(
            ContractPaymentTerm cpt,
            DateTime contractStartDate,
            ContractPaymentType contractPaymentType,
            DateTime commencesOnDate) {

        String text = "";

        boolean isProratedPlusMonth = BooleanUtils.toBoolean(cpt.getContract().getProRatedPlusMonth());
        Integer additionalDiscountedPaymentsCount = DDUtils.getAdditionalDiscountPaymentsCount(
                cpt, contractPaymentType, isProratedPlusMonth);
        Double additionalDiscountAmountPerPayment = DDUtils.getAdditionalDiscountAmountPerPayment(
                cpt, contractPaymentType);
        Integer discountEffectiveAfter = DDUtils.getDiscountAffectedAfter(
                contractPaymentType, isProratedPlusMonth);

        if (BooleanUtils.toBoolean(contractPaymentType.getAffectedByAdditionalDiscount()) &&
                additionalDiscountAmountPerPayment != null && additionalDiscountAmountPerPayment.doubleValue() != 0 &&
                additionalDiscountedPaymentsCount != null && additionalDiscountedPaymentsCount.intValue() > 0) {
            text += getAdditionalDiscountedPaymentTermForNewContract(
                    cpt, contractPaymentType, contractStartDate, commencesOnDate,
                    discountEffectiveAfter, additionalDiscountedPaymentsCount,
                    additionalDiscountAmountPerPayment);

            return text;
        }

        String frequency = getPaymentFrequencyStatement(contractPaymentType);
        String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);

        text += getFormattedPaymentTermForNewContract(contractPaymentType.getDescription(),
                DiscountsWithVatHelper.getAmountWithoutVat(contractPaymentType.getAmount()),
                frequency, startsOn);

        if (discountEffectiveAfter != null && discountEffectiveAfter > 0) {
            commencesOnDate = commencesOnDate.plusMonths(discountEffectiveAfter);
            text += getDiscountedPaymentTermForNewContract(cpt, contractPaymentType,
                    commencesOnDate, discountEffectiveAfter, 0, 0D);
        }

        return text;
    }*/

    /*public static String getAdditionalDiscountedPaymentTermForNewContract(ContractPaymentTerm cpt,
                                                                          ContractPaymentType contractPaymentType, DateTime contractStartDate,
                                                                          DateTime commencesOnDate,
                                                                          Integer discountEffectiveAfter,
                                                                          Integer additionalDiscountedPaymentsCount, Double additionalDiscountAmountPerPayment) {
        String text = "";
        boolean isMonthlyPayment = contractPaymentType.getType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        boolean paymentContinuesAfterAdditionalDiscount = true;
        if (!isMonthlyPayment) {
            String description = contractPaymentType.getDescription();
            String frequency;

            String startsOn = getCommencesOn(commencesOnDate, contractStartDate);
            Double amount = contractPaymentType.getAmount() - additionalDiscountAmountPerPayment - Utils.getVatAmount(additionalDiscountAmountPerPayment);

            int numOfAdditionalDiscountedPayments = 1;
            if (additionalDiscountedPaymentsCount == 1) {
                frequency = "One Time Payment";
            } else {
                frequency = DDUtils.getPaymentFrequencyStatement(contractPaymentType);
                numOfAdditionalDiscountedPayments = additionalDiscountedPaymentsCount;
            }

            if (contractPaymentType.getEndsAfter() != null && contractPaymentType.getEndsAfter() <= (additionalDiscountedPaymentsCount * contractPaymentType.getRecurrence())) {
                numOfAdditionalDiscountedPayments = contractPaymentType.getEndsAfter() / contractPaymentType.getRecurrence();
                paymentContinuesAfterAdditionalDiscount = false;
            }

            text += getFormattedPaymentTermForNewContract(description, Utils.getAmountWithoutVat(amount),
                    frequency, startsOn);
            commencesOnDate = commencesOnDate.plusMonths(numOfAdditionalDiscountedPayments * contractPaymentType.getRecurrence());
        } else {
            int numOfMonths = discountEffectiveAfter == null || discountEffectiveAfter.equals(0) ?
                    additionalDiscountedPaymentsCount :
                    Math.min(additionalDiscountedPaymentsCount, discountEffectiveAfter);
            String frequency = getPaymentFrequencyStatement(contractPaymentType, numOfMonths);

            String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);
            startsOn += String.format(" for %s month", numOfMonths) + (numOfMonths > 1 ? "s" : "");
            Double amount = contractPaymentType.getAmount() - additionalDiscountAmountPerPayment - Utils.getVatAmount(additionalDiscountAmountPerPayment);
            String description = contractPaymentType.getDescription();

            text += getFormattedPaymentTermForNewContract(description, Utils.getAmountWithoutVat(amount),
                    frequency, startsOn);

            commencesOnDate = commencesOnDate.plusMonths(numOfMonths);
        }

        if (!paymentContinuesAfterAdditionalDiscount) return text;

        if (!isMonthlyPayment || (discountEffectiveAfter != null && !discountEffectiveAfter.equals(0) && additionalDiscountedPaymentsCount < discountEffectiveAfter)) {
            String frequency = getPaymentFrequencyStatement(contractPaymentType);
            String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);
            if (isMonthlyPayment) {
                int fullMonthlyPaymentsCount = discountEffectiveAfter - additionalDiscountedPaymentsCount;
                commencesOnDate = commencesOnDate.plusMonths(fullMonthlyPaymentsCount);
                frequency = getPaymentFrequencyStatement(contractPaymentType, fullMonthlyPaymentsCount);
                startsOn += String.format(" for %s month", fullMonthlyPaymentsCount) + (fullMonthlyPaymentsCount > 1 ? "s" : "");
            }

            text += getFormattedPaymentTermForNewContract(contractPaymentType.getDescription(), Utils.getAmountWithoutVat(contractPaymentType.getAmount()),
                    frequency, startsOn);
        }

        if (isMonthlyPayment && discountEffectiveAfter != null) {
            text += getDiscountedPaymentTermForNewContract(cpt, contractPaymentType, commencesOnDate, discountEffectiveAfter, additionalDiscountedPaymentsCount, additionalDiscountAmountPerPayment);
        }

        return text;
    }*/

    /*public static String getDiscountedPaymentTermForNewContract(ContractPaymentTerm cpt, ContractPaymentType contractPaymentType, DateTime commencesOnDate,
                                                                Integer discountEffectiveAfter,
                                                                Integer additionalDiscountedPaymentsCount, Double additionalDiscountAmountPerPayment) {
        String text = "";
        DateTime contractStartDate = cpt.getContract().getStartOfContract() != null ? new DateTime(cpt.getContract().getStartOfContract()) : DateTime.now();
        boolean isMaidVisa = contractPaymentType.getContractPaymentTerm().getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));
        boolean isMonthlyPayment = contractPaymentType.getType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        if (!isMonthlyPayment) return text;

        Double workerSalary = cpt.getContract().getWorkerSalary();
        boolean includeWorkerSalary = doesIncludeWorkerSalary(contractPaymentType.getContractPaymentTerm().getContract().getContractProspectType().getCode(),
                contractPaymentType.getType().getCode(), cpt.getDiscount(), contractPaymentType.getDiscount());
        Double discountedAmount = contractPaymentType.getAmount() - contractPaymentType.getDiscount();
        if (discountEffectiveAfter != null && !discountEffectiveAfter.equals(0) &&
                additionalDiscountedPaymentsCount > discountEffectiveAfter) {
            int numOfMonths = additionalDiscountedPaymentsCount - discountEffectiveAfter;
            DateTime endAdditionalDiscountDate = commencesOnDate.plusMonths(numOfMonths);
            String description = contractPaymentType.getDescription() + (isMaidVisa ? " + Maid Salary" : "");
            String frequency = getPaymentFrequencyStatement(contractPaymentType, numOfMonths);
            String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);
            startsOn += String.format(" for %s month", numOfMonths) + (numOfMonths > 1 ? "s" : "");
            double additionalDiscountedAmountWithoutVat;
            if (includeWorkerSalary) {
                additionalDiscountedAmountWithoutVat = Math.floor(Utils.getAmountWithoutVat(discountedAmount - workerSalary)) - additionalDiscountAmountPerPayment + workerSalary;
            } else {
                additionalDiscountedAmountWithoutVat = Utils.getAmountWithoutVat(discountedAmount - additionalDiscountAmountPerPayment - Utils.getVatAmount(additionalDiscountAmountPerPayment));
            }

            text += getFormattedPaymentTermForNewContract(description, additionalDiscountedAmountWithoutVat,
                    frequency, startsOn);

            commencesOnDate = endAdditionalDiscountDate;
        }

        String description = contractPaymentType.getDescription() + (isMaidVisa ? " + Maid Salary" : "");
        String frequency = getPaymentFrequencyStatement(contractPaymentType);
        String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);

        Double discountedAmountWithoutVat;
        if (includeWorkerSalary) {
            discountedAmount -= workerSalary;
            discountedAmountWithoutVat = Math.floor(Utils.getAmountWithoutVat(discountedAmount)) + workerSalary;
        } else {
            discountedAmountWithoutVat = Utils.getAmountWithoutVat(discountedAmount);
        }

        text += getFormattedPaymentTermForNewContract(description, discountedAmountWithoutVat, frequency, startsOn);

        return text;
    }*/

    // ACC-4905
    /*public static String getPaymentTermForOneMonthAgreementContract(ContractPaymentTerm cpt) {
    
        String text = "";
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);

        DateTime contractStartDate = cpt.getContract().getStartOfContract() != null ?
                new DateTime(cpt.getContract().getStartOfContract()) : DateTime.now();

        ContractPaymentType type = cpt.getContractPaymentTypes()
                .stream().filter(item -> item.getType().getCode().equals(
                        PaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                .findFirst().orElse(null);
        Contract contract = cpt.getContract();
        DateTime commencesOnDate;

        DateTime paidEndDate = paymentService.getLastReceivedMonthlyPaymentDate(cpt.getContract());

        if (paidEndDate == null) {
            String frequency = "One Time Payment ";
            text += getFormattedPaymentTermForNewContract(
                    type.getDescription(),
                    Utils.getAmountWithoutVat(cpt.getMonthlyPayment()), frequency,
                    DateUtil.formatMonthDayYear(contractStartDate.toDate()));
            commencesOnDate = new DateTime(contractStartDate).plusMonths(1);
        } else {
            commencesOnDate = new DateTime(contractStartDate).plusMonths(
                    Months.monthsBetween(contractStartDate, paidEndDate).getMonths() + 1);
        }
        
        // prorated payment
        String frequency = "One Time Payment ";
        Map<String, Object> map = new ArrayMap<>();
        map.put("dailyRateAmount", cpt.getDailyRateAmount());
        map.put("monthlyPaymentAmount", cpt.getMonthlyPayment());
        map.put("proRatedDate", commencesOnDate.toLocalDate());
        map.put("isOneMonthAgreement", contract.isOneMonthAgreement());
        map.put("firstMonthPayment", cpt.getFirstMonthPayment());

        text += getFormattedPaymentTermForNewContract(
                type.getDescription(),
                Utils.getAmountWithoutVat(paymentService.getProRatedAmount(map)),
                frequency,
                DateUtil.formatMonthDayYear(commencesOnDate.toDate()));
        commencesOnDate = commencesOnDate.plusMonths(1);

        Integer additionalDiscountedPaymentsCount = DDUtils.getAdditionalDiscountPaymentsCount(
                cpt, type,false);
        Double additionalDiscountAmountPerPayment = DDUtils.getAdditionalDiscountAmountPerPayment(
                cpt, type);
        Integer discountEffectiveAfter = DDUtils.getDiscountAffectedAfter(
                type, false);
        
        if (BooleanUtils.toBoolean(type.getAffectedByAdditionalDiscount()) &&
                additionalDiscountAmountPerPayment != 0.0 &&
                additionalDiscountedPaymentsCount > 0) {

            text += getAdditionalDiscountedPaymentTermForOneAgreementMonthContract(
                    cpt, type, contractStartDate, commencesOnDate,
                    discountEffectiveAfter, additionalDiscountedPaymentsCount,
                    additionalDiscountAmountPerPayment);

            if (!text.isEmpty()) return text;
        }
        
        int numberOfPayment = (Months.monthsBetween(contractStartDate, commencesOnDate).getMonths() -1);

        if (discountEffectiveAfter == null || discountEffectiveAfter == 0 ||
                numberOfPayment < discountEffectiveAfter) {

            frequency = getPaymentFrequencyStatement(type);
            String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);

            text += getFormattedPaymentTermForNewContract(type.getDescription(),
                    Utils.getAmountWithoutVat(type.getAmount()),
                    frequency, startsOn);

            if (discountEffectiveAfter != null && discountEffectiveAfter > 0) {
                commencesOnDate = commencesOnDate.plusMonths(
                        discountEffectiveAfter - numberOfPayment);
            }
        }
        
        if (discountEffectiveAfter != null && discountEffectiveAfter > 0) {
            text += getDiscountedPaymentTermForOneMonthAgreementContract(
                    cpt, type, commencesOnDate, discountEffectiveAfter, 0, 0D);
        }

        return text;
    }*/

    // ACC-4905
    /*public static String getAdditionalDiscountedPaymentTermForOneAgreementMonthContract(
            ContractPaymentTerm cpt, ContractPaymentType contractPaymentType,
            DateTime contractStartDate, DateTime commencesOnDate, Integer discountEffectiveAfter,
            Integer additionalDiscountedPaymentsCount, Double additionalDiscountAmountPerPayment) {
        
        String text = "";
        int numOfMonths = discountEffectiveAfter == null || discountEffectiveAfter.equals(0) ?
                additionalDiscountedPaymentsCount :
                Math.min(additionalDiscountedPaymentsCount, discountEffectiveAfter);

        if (cpt.getContract().isOneMonthAgreement()) {
            numOfMonths -= (Months.monthsBetween(contractStartDate, commencesOnDate).getMonths() -1);
            if (numOfMonths < 1) return text;
        }

        String frequency = getPaymentFrequencyStatement(contractPaymentType, numOfMonths);
        String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);
        startsOn += String.format(" for %s month", numOfMonths) + (numOfMonths > 1 ? "s" : "");
        
        double amount = contractPaymentType.getAmount() - additionalDiscountAmountPerPayment - Utils.getVatAmount(additionalDiscountAmountPerPayment);
        String description = contractPaymentType.getDescription();
        
        text += getFormattedPaymentTermForNewContract(
                description, Utils.getAmountWithoutVat(amount), frequency, startsOn);
        
        commencesOnDate = commencesOnDate.plusMonths(numOfMonths);
        
        if (discountEffectiveAfter != null && !discountEffectiveAfter.equals(0) &&
                additionalDiscountedPaymentsCount < discountEffectiveAfter) {

            startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);

            int fullMonthlyPaymentsCount = discountEffectiveAfter - Months.monthsBetween(
                    contractStartDate, commencesOnDate).getMonths() + 1;

            if (fullMonthlyPaymentsCount >= 1) {
                commencesOnDate = commencesOnDate.plusMonths(fullMonthlyPaymentsCount);
                frequency = getPaymentFrequencyStatement(contractPaymentType, fullMonthlyPaymentsCount);
                startsOn += String.format(" for %s month", fullMonthlyPaymentsCount) + (fullMonthlyPaymentsCount > 1 ? "s" : "");
                
                text += getFormattedPaymentTermForNewContract(
                        contractPaymentType.getDescription(),
                        Utils.getAmountWithoutVat(contractPaymentType.getAmount()),
                        frequency, startsOn);
            }
        }
        
        if (discountEffectiveAfter != null) {
            text += getDiscountedPaymentTermForOneMonthAgreementContract(
                    cpt, contractPaymentType, commencesOnDate, discountEffectiveAfter,
                    additionalDiscountedPaymentsCount, additionalDiscountAmountPerPayment);
        }
        
        return text;
    }*/

    // ACC-4905
    /*public static String getDiscountedPaymentTermForOneMonthAgreementContract(
            ContractPaymentTerm cpt, ContractPaymentType contractPaymentType,
            DateTime commencesOnDate, Integer discountEffectiveAfter,
            Integer additionalDiscountedPaymentsCount, Double additionalDiscountAmountPerPayment) {

        String text = "";
        DateTime contractStartDate = cpt.getContract().getStartOfContract() != null ?
                new DateTime(cpt.getContract().getStartOfContract()) : DateTime.now();
        
        double discountedAmount = contractPaymentType.getAmount() - contractPaymentType.getDiscount();
        if (discountEffectiveAfter != null && !discountEffectiveAfter.equals(0) &&
                additionalDiscountedPaymentsCount > discountEffectiveAfter) {
            
            int numOfMonths = additionalDiscountedPaymentsCount
                    - (Months.monthsBetween(contractStartDate, commencesOnDate).getMonths() -1) ;

            if (numOfMonths > 0) {
                String startsOn = "starting " + getCommencesOn(commencesOnDate, contractStartDate);
                startsOn += String.format(" for %s month", numOfMonths) + (numOfMonths > 1 ? "s" : "");
                double additionalDiscountedAmountWithoutVat =
                        Utils.getAmountWithoutVat(discountedAmount - additionalDiscountAmountPerPayment -
                                Utils.getVatAmount(additionalDiscountAmountPerPayment));
                
                text += getFormattedPaymentTermForNewContract(
                        contractPaymentType.getDescription(), additionalDiscountedAmountWithoutVat,
                        getPaymentFrequencyStatement(contractPaymentType, numOfMonths), startsOn);

                commencesOnDate = commencesOnDate.plusMonths(numOfMonths);
            }
        }
        
        text += getFormattedPaymentTermForNewContract(contractPaymentType.getDescription(),
                Utils.getAmountWithoutVat(discountedAmount),
                getPaymentFrequencyStatement(contractPaymentType),
                "starting " + getCommencesOn(commencesOnDate, contractStartDate));
        
        return text;
    }*/

    public static String getFormattedPaymentTermForNewContract(
            String description,
            Double amount,
            String frequency,
            String startsOn) {

        String text = description + ": ";
        text += "<b>AED " + String.format("%.0f", Math.floor(amount)) + "</b> ";
        text += frequency + " ";
        text += startsOn;

        text += "<br/>";
        return text;
    }

    public static String getPaymentFrequencyStatement(
            AbstractPaymentTypeConfig contractPaymentType,
            Integer numOfMonthlyPayment) {

        if (contractPaymentType.getType().getCode().equals(
                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) &&
                numOfMonthlyPayment != null && numOfMonthlyPayment.equals(1)) {
            return "One Time Payment";
        }

        return getPaymentFrequencyStatement(contractPaymentType);
    }

    public static String getPaymentFrequencyStatement(
            AbstractPaymentTypeConfig contractPaymentType) {

        if (contractPaymentType.getType().getCode().equals(
                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE)) {
            return "Monthly";
        }

        Integer recurrence = contractPaymentType.getRecurrence();
        if (recurrence == null || recurrence == 0) return "One Time Payment";

        boolean yearly = recurrence % 12 == 0;
        if (yearly) recurrence = recurrence / 12;

        boolean plural = !recurrence.equals(1);
        String frequency = "Once every " + recurrence + (yearly ? " year" : " month");

        if (plural) frequency += "s";

        return frequency;
    }

    public static String getCommencesOn(DateTime targetDate, DateTime contractStartDate) {
        String commencesOn = Days.daysBetween(targetDate, DateTime.now()).getDays() == 0 ? "Today" :
                DateUtil.isSameMonth(targetDate.toDate(), contractStartDate.toDate()) ? DateUtil.formatMonthDayYear(targetDate.toDate()) :
                        DateUtil.formatMonthDayYear(targetDate.withDayOfMonth(1).toDate());

        return commencesOn;
    }

    public static String getCommencesOnForNonMonthly(DateTime targetDate) {

        return Days.daysBetween(targetDate, DateTime.now()).getDays() == 0 ?
                "Today" : DateUtil.formatMonthDayYear(targetDate.toDate());
    }

    public static DateTime getCommencesOnDate(DateTime targetDate, DateTime contractStartDate) {
        return  DateUtil.isSameMonth(targetDate.toDate(), contractStartDate.toDate()) ?
                targetDate : targetDate.withDayOfMonth(1);
    }

    public static DateTime getPaymentEndsOn(DateTime startDate, DateTime endDate) {
        return  DateUtil.isSameMonth(startDate.toDate(), endDate.toDate()) ?
                endDate.dayOfMonth().withMaximumValue() :
                endDate.minusMonths(1).dayOfMonth().withMaximumValue();
    }

    public static Integer getAdditionalDiscountPaymentsCount(ContractPaymentTerm cpt, ContractPaymentType contractPaymentType, boolean isProratedPlusMonth) {
        boolean isMonthlyPayment = contractPaymentType.getType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        Integer additionalDiscountedPaymentsCount = cpt.getAdditionalDiscountMonthsCount(contractPaymentType.getType().getCode());
        if (isMonthlyPayment && isProratedPlusMonth && additionalDiscountedPaymentsCount != null) {
            additionalDiscountedPaymentsCount--;
        }

        return additionalDiscountedPaymentsCount;
    }

    public static Double getAdditionalDiscountAmountPerPayment(ContractPaymentTerm cpt, ContractPaymentType contractPaymentType) {
        Integer additionalDiscountedPaymentsCount = getAdditionalDiscountPaymentsCount(cpt, contractPaymentType, false);
        Double additionalDiscountAmountPerPayment = cpt.getAdditionalDiscount() != null && additionalDiscountedPaymentsCount != null && additionalDiscountedPaymentsCount != 0 ?
                cpt.getAdditionalDiscount() / additionalDiscountedPaymentsCount : 0D;

        return additionalDiscountAmountPerPayment;
    }

    public static Integer getDiscountAffectedAfter(AbstractPaymentTypeConfig contractPaymentType, boolean isProratedPlusMonth) {
        boolean isMonthlyPayment = contractPaymentType.getType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        Integer discountEffectiveAfter = contractPaymentType.getDiscountEffectiveAfter();
        if (isMonthlyPayment && isProratedPlusMonth && discountEffectiveAfter != null) {
            discountEffectiveAfter--;
        }

        return discountEffectiveAfter;
    }

    private static List<DirectDebit> mergeDDs(List<DirectDebit> directDebits) {
        List<DirectDebit> result = new ArrayList();
        DirectDebit previousDD = null;

        logger.info("Before Merge: " + directDebits.size());
        for (DirectDebit dd : directDebits) {
            if (previousDD != null && previousDD.getAmount() == dd.getAmount() &&
                    previousDD.getCategory().equals(DirectDebitCategory.B) && dd.getCategory().equals(DirectDebitCategory.B)) {
                previousDD.setExpiryDate(dd.getExpiryDate());
                continue;
            }

            result.add(dd);
            previousDD = dd;
        }

        logger.info("After Merge: " + result.size());

        return result;
    }

    public static String getTermsAsString(Map<String, String> terms) {
        String result = "";
        if (terms == null || terms.isEmpty()) return result;

        int index = 1;
        for (String key : terms.keySet()) {
            result += "<br/>" + "<b>" + index++ + ". " + key + "</b><br/>" + terms.get(key).toString();
        }

        return result;
    }

    public static boolean isDDConfirmed(DirectDebit directDebit) {
        if (directDebit == null) return false;

        if (directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED) ||
                directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
            return true;
        }

        return false;
    }

    public static boolean isDDPendingSent(DirectDebit directDebit) {
        if (directDebit == null)
            return false;

        if (directDebit.getStatus().equals(DirectDebitStatus.PENDING) || directDebit.getMStatus().equals(DirectDebitStatus.PENDING)) {
            return isDDSent(directDebit);
        }

        return false;
    }

    public static boolean isDDPendingNotSent(DirectDebit directDebit) {
        if (directDebit == null)
            return false;

        if (directDebit.getStatus().equals(DirectDebitStatus.PENDING) || directDebit.getMStatus().equals(DirectDebitStatus.PENDING)) {
            return !isDDSent(directDebit);
        }

        return false;
    }

    public static boolean isDDSent(DirectDebit directDebit) {
        if (directDebit == null || directDebit.getDirectDebitFiles() == null || directDebit.getDirectDebitFiles().isEmpty())
            return false;

        List<DirectDebitFile> ddfs = directDebit.getDirectDebitFiles();
        return ddfs.stream().anyMatch(ddf -> ddf.getStatus().equals(DirectDebitFileStatus.SENT) && ddf.getDdStatus().equals(DirectDebitStatus.PENDING));
    }

    public static boolean isDDCanceledExpired(DirectDebit directDebit) {
        if (directDebit == null)
            return false;

        if (directDebit.getStatus().equals(DirectDebitStatus.CANCELED) || directDebit.getMStatus().equals(DirectDebitStatus.CANCELED) ||
                directDebit.getStatus().equals(DirectDebitStatus.EXPIRED) || directDebit.getMStatus().equals(DirectDebitStatus.EXPIRED)) {
            return true;
        }

        return false;
    }

    public static DirectDebitRejectionToDo copyRejectionFlowToDDA(DirectDebitRejectionToDo rejectionToDo) {
        DirectDebitRejectionToDoRepository rejectionToDoRepository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);

        List<String> currentTasks = rejectionToDo.getCurrentTasks();
        DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
        DirectDebitRejectionToDoType startStep = getDDRejectionToDoNextStep(step, DirectDebitCategory.A);

        DirectDebitRejectionToDo newRejectionToDo = rejectionToDo.clone(startStep, DirectDebitCategory.A);

        rejectionToDo.setStopped(true);
        rejectionToDo.setCompleted(true);
        rejectionToDoRepository.save(rejectionToDo);

        return rejectionToDoRepository.save(newRejectionToDo);
    }


    public static DirectDebitRejectionToDoType getDDRejectionToDoNextStep(DirectDebitRejectionToDoType step, DirectDebitCategory ddCategory) {
        DirectDebitRejectionToDoType startStep = null;

        switch (step) {
            case WAITING_CLIENT_SIGNATURE:
            case WAITING_CLIENT_SIGNATURE_B_CASE_D: {
                if (ddCategory.equals(DirectDebitCategory.A)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                } else if (ddCategory.equals(DirectDebitCategory.B)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D;
                }
            }
            case WAITING_BANK_RESPONSE:
            case WAITING_BANK_RESPONSE_B:
            case WAITING_BANK_RESPONSE_B_CASE_D: {
                if (ddCategory.equals(DirectDebitCategory.A)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE;
                } else if (ddCategory.equals(DirectDebitCategory.B)) {
                    startStep = step;
                }
                break;
            }
            case WAITING_ACCOUNTANT_ACTION:
            case WAITING_ACCOUNTANT_ACTION_B_CASE_D:
                if (ddCategory.equals(DirectDebitCategory.A)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION;
                } else if (ddCategory.equals(DirectDebitCategory.B)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_CASE_D;
                }
                break;
            case WAITING_RE_SCHEDULE_B:
                if (ddCategory.equals(DirectDebitCategory.A)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE;
                } else if (ddCategory.equals(DirectDebitCategory.B)) {
                    startStep = step;
                }
                break;
        }

        return startStep;
    }

    public static boolean doesDDCoverDate(DirectDebit directDebit, Date date) {
        if (directDebit == null) return false;
        List<ContractPayment> contractPayments = directDebit.getPayments();

        List<DirectDebitStatus> excludeStatuses = Arrays.asList(DirectDebitStatus.CANCELED,
                DirectDebitStatus.REJECTED, DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED);

        for (ContractPayment contractPayment : contractPayments) {
            if (ContractPaymentHelper.doesMonthlyContractPaymentCoverDate(
                    contractPayment, new DateTime(date), excludeStatuses)) {

                logger.info("Monthly DD: " + directDebit.getId() + "; covers date: " + date);
                return true;
            }
        }

        return false;
    }

    public static Map<String, String> getDDBankDetails(DirectDebit directDebit) {
        Map<String, String> map = new HashMap();
        String eid = "";
        String accountName = "";
        String iban = "";
        String bankName = "";

        if (directDebit != null) {
            eid = directDebit.getEid();
            accountName = directDebit.getAccountName();
            iban = directDebit.getIbanNumber();
            bankName = directDebit.getBankName();
        }

        map.put("eid", eid);
        map.put("accountName", accountName);
        map.put("iban", iban);
        map.put("bankName", bankName);

        return map;
    }

    public static void mergePendingDataEntryDDsIntoOneToDo(List<DirectDebit> directDebits) {
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitFileRepository directDebitFileRepository = Setup.getRepository(DirectDebitFileRepository.class);

        List<Long> ddIDs = directDebits.stream()
                .map(directDebit -> directDebit.getId()).collect(Collectors.toList());

        for (Long ddID : ddIDs) {
            DirectDebit directDebit = directDebitRepository.findOne(ddID);

            if (!directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) && !directDebit.getMStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                return;

            List<Long> otherPendingDataEntryDDsBankInfoGroups = directDebitFileRepository.getPendingDDFsByCPTAndDDIdNotInGroupByDDBankInfoGroup(directDebit.getContractPaymentTerm(),
                    ddIDs);

            if (otherPendingDataEntryDDsBankInfoGroups == null || otherPendingDataEntryDDsBankInfoGroups.isEmpty())
                return;

            Long bankInfoGroup = otherPendingDataEntryDDsBankInfoGroups.get(0);
            directDebit.setDdBankInfoGroup(bankInfoGroup);
            directDebitRepository.save(directDebit);
        }
    }

    public static void fireClientAndTerminateContractFromClientMgmt(Long contractId){
        String url = "/clientmgmt/contractTerminationNew/fireClientAndTerminateContract?contractId=" + contractId.toString();

        logger.log(Level.SEVERE, "fireClientAndTerminateContractFromClientMgmt URL: " + url);
        Setup.getApplicationContext().getBean(InterModuleConnector.class).postJsonAsync(url, new HashMap<>());;
    }

    public static void sendFiredClientSmsAndEmailFromSales(Long contractId){
        String url = "/sales/contract/sendfiredclientsmsandemail?contract=" + contractId.toString();

        logger.log(Level.SEVERE, "sendFiredClientSmsAndEmail URL: " + url);
        Setup.getApplicationContext().getBean(InterModuleConnector.class).getAsync(url, LinkedHashMap.class);
    }

    public static void createComplaintNoMaidFromComplaints(Long clientId, Contract contract){
        try {
            String contractType = contract.getContractProspectType() != null && contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) ? "CC" : "MV";
            String url = "/complaints/complaint/createComplaintNoMaid/" + clientId.toString();
            url += "?contractId=" + contract.getId().toString();
            url += "&contractType=" + contractType;
            url += "&contractDate=" + DateUtil.formatDateDashedWithTime(contract.getCreationDate());

            logger.log(Level.SEVERE, "createComplaintNoMaidFromComplaints URL: " + url);
            Setup.getApplicationContext().getBean(InterModuleConnector.class).postJsonAsync(url, new HashMap<>());
        }catch (Exception ex){
            logger.log(Level.SEVERE, "Exeption in createComplaintNoMaidFromComplaints: " + ex.getMessage());
        }
    }

    public static DirectDebit getPaymentDD(Payment payment, ContractPaymentTerm cpt, DirectDebitCategory category) {
        logger.info("getPaymentDD; payment: " + payment.getId() + "; CPT: " + cpt.getId() + "; category: " + category);
        
        Optional<DirectDebit> o = Setup.getRepository(DirectDebitRepository.class)
                .findByContractPaymentTermAndCategoryOrderByCreationDateDesc(cpt, category)
                .stream()
                .filter(dd -> {
                    boolean returned = DDUtils.doesDDCoverDate(dd, payment.getDateOfPayment()) &&
                        !DDUtils.isDDCanceledExpired(dd);
                    
                    logger.info("DD: " + dd.getId() + "; returned: " + returned);
                    return returned;
                })
                .findFirst();
        
        if (o != null && o.isPresent()) return o.get();
        
        return null;
    }
    
    public static DirectDebit getDirectDebit(ContractPaymentTerm cpt, DirectDebitCategory category) {
        logger.info("getDirectDebit; CPT: " + cpt.getId() + "; category: " + category);
        
        Optional<DirectDebit> o = Setup.getRepository(DirectDebitRepository.class)
                .findByContractPaymentTermAndCategoryOrderByCreationDateDesc(cpt, category)
                .stream()
                .filter(dd -> {
                    boolean returned = !DDUtils.isDDCanceledExpired(dd);
                    
                    logger.info("DD: " + dd.getId() + "; returned: " + returned);
                    return returned;
                })
                .findFirst();
        
        if (o != null && o.isPresent()) return o.get();
        
        return null;
    }
}
