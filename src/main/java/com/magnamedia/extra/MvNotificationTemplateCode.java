package com.magnamedia.extra;

public enum MvNotificationTemplateCode {
    MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION,
    MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION,


    MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION, //row 13 "If we receive an amount from the client when he doesn't owe us any amount (IF client has DD) if he already cancelled"
    MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION, //row 14 If we receive an amount from the client when he doesn't owe us any amount (IF client has DD), IF his contract is “scheduled for termination” AND we don’t need that amount
    // ACC-5214
    MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION,
    MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION,
    MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION,
    MV_DD_PENDING_INFO_NOTIFICATION,
    MV_PAYTABS_THANKS_MESSAGE_NOTIFICATION,

    // ACC-4591
    MV_ACCOUNTING_PAY_ACCOMMODATION_FEE_NOTIFICATION,
    MV_ACCOUNTING_PAY_CC_TO_MV_NOTIFICATION,
    MV_ACCOUNTING_PAY_MONTHLY_PAYMENT_NOTIFICATION,
    MV_ACCOUNTING_PAY_OVERSTAY_FEES_NOTIFICATION,
    MV_ACCOUNTING_PAY_PCR_TEST_NOTIFICATION,
    MV_ACCOUNTING_PAY_URGENT_VISA_CHARGES_NOTIFICATION,
    MV_ACCOUNTING_PAY_INSURANCE_NOTIFICATION,
    MV_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_NOTIFICATION,

    // ACC-6943
    MV_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP,

    MV_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION,
    MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION,
    MV_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION,

    // ACC-5533 CMA-3750
    MV_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_WITH_PAY_LINK_NOTIFICATION,
    MV_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_NOTIFICATION,

    MV_ACCOUNTING_REFUND_REJECTED_NOTIFICATION,
    MV_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION,
    MV_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION,
    MV_ACCOUNTING_REFUND_REJECTED_CREDIT_CARD_NOTIFICATION,
    MV_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION,
    MV_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION,

    MV_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION,

    MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT,
    MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT,
    MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_RECURRING_CONTRACT,
    MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_CREDIT_CARD_CONTRACT,
    MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_CREDIT_CARD_CONTRACT,

    MV_MAID_VISA_J2_8_20_NOTIFICATION,
}