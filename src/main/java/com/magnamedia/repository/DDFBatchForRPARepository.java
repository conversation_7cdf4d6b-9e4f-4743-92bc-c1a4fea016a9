package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDFBatchForRPA;
import com.magnamedia.module.type.DDFBatchStatus;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Sep 19, 2020
 *         Jirra ACC-2571
 */

@Repository
public interface DDFBatchForRPARepository extends BaseRepository<DDFBatchForRPA> {

    List<DDFBatchForRPA> findByIdsContainsAndStatusNotIn(String id, List<DDFBatchStatus> statuses);

    boolean existsByIdsContainsAndStatus(String id, DDFBatchStatus s);
}
