package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankDirectDebitActivationFile;
import com.magnamedia.entity.BankDirectDebitActivationRecord;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 10, 2018
 * Jirra ACC-329
 */
@Repository
public interface BankDirectDebitActivationRecordRepository extends BaseRepository<BankDirectDebitActivationRecord> {

    @Query("Select bdd from BankDirectDebitActivationRecord bdd inner join DirectDebitFile ddf " +
            "on bdd.directDebitFileId = ddf.id where bdd.id in (?1) order by ddf.ddMethod desc ")
    List<BankDirectDebitActivationRecord> findByIdInOrderByDirectDebitFile_DdMethodDesc(List<Long> ids);

    @Query("select max (b.processingTimeStamp) from BankDirectDebitActivationRecord b where b.bankDirectDebitActivationFile = :file and b.processing = true group by b.processingTimeStamp")
    Long findByMaxProcessingTimeStamp(@Param("file") BankDirectDebitActivationFile file);

    @Query("select count (b.id) from BankDirectDebitActivationRecord b where b.bankDirectDebitActivationFile = :file and b.processingTimeStamp = :time and b.confirmed = true")
    int findConfirmedCountByProcessingTimeStamp(@Param("time") long timestamp,@Param("file") BankDirectDebitActivationFile file);

    @Query("select count (b.id) from BankDirectDebitActivationRecord b where b.bankDirectDebitActivationFile = :file and b.processingTimeStamp = :time")
    int findCountByProcessingTimeStamp(@Param("time") long timestamp,@Param("file") BankDirectDebitActivationFile file);

    @Query("select b from BankDirectDebitActivationRecord b " +
            "where b.bankDirectDebitActivationFile.id = ?1 and b.status in ('ACCEPTED', 'REJECTED') and " +
            "b.directDebitFileId is not null and b.ddStatus = 'PENDING'")
    List<BankDirectDebitActivationRecord> findMatchedRecordsIdsByFileId(Long fileId);

    List<BankDirectDebitActivationRecord> findByDirectDebitFileId(Long ddfId);

    List<BankDirectDebitActivationRecord> findByIdInOrderByContractIdAsc(List<Long> ids);

    @Query("select new Map(r as record, coalesce(ddf1.id, ddf2.id) as ddfId) " +
            "from BankDirectDebitActivationRecord r " +
            "left join DirectDebitFile ddf1 on " +
                "ddf1.applicationId = r.contract and ddf1.amount = r.amount and " +
                "ddf1.startDate = r.startDate and ddf1.expiryDate = r.expiryDate and " +
                "ddf1.accountName = r.account and ddf1.ibanNumber = r.iban " +
            "left join DirectDebitFile ddf2 on ddf2.applicationId = r.contract and ddf1.id is null " +
            "where r.bankDirectDebitActivationFile.id = ?1 " +
            "group by r.id")
    List<Map<String, Object>> findRecordWithMatchedDdfByBankDirectDebitActivationFileId(Long fileId, Pageable pageable);
}