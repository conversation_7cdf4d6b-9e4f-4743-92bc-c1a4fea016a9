package com.magnamedia.repository;

import java.util.Date;
import java.util.List;

import com.magnamedia.core.entity.BackgroundTaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AccountingEntityProperty;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 15, 2020
 *         Jirra ACC-1435
 */

@Repository
public interface AccountingEntityPropertyRepository extends BaseRepository<AccountingEntityProperty> {

    @Query("select count(p) > 0 from AccountingEntityProperty p " +
            "where p.originId = :#{#origin.id} and p.originType = :#{#origin.entityType} and " +
            "p.key in :keys and p.isDeleted = false")
    boolean existsByOriginAndKeys(@Param("origin") BaseEntity origin, @Param("keys") List<String> keys);

    @Query("select p from AccountingEntityProperty p where p.key = :#{#key} and p.originType = :#{#origin.entityType} and p.originId = :#{#origin.id} and p.isDeleted = false")
    AccountingEntityProperty findByKeyAndOriginAndDeletedFalse(@Param("key") String key, @Param("origin") BaseEntity origin);

    AccountingEntityProperty findByKeyAndIsDeletedFalse(String key);

    Page<AccountingEntityProperty> findByKeyAndOriginTypeAndIsDeletedFalse(String key, String originType, Pageable pageable);

    // ACC-9005 ACC-9004
    @Query("select a from AccountingEntityProperty a " +
            "where a.isDeleted = false and a.key = ?1 and " +
                "not exists (select 1 from BackgroundTask b where b.name = a.purpose and b.status not in ?2) " +
            "group by a.purpose " +
            "order by a.creationDate")
    List<AccountingEntityProperty> findByKeyAndDoesntHaveRunningBGT(String key, List<BackgroundTaskStatus> statuses);

    @Query("select a from AccountingEntityProperty a " +
            "where a.isDeleted = false and a.key = ?1 and " +
            "not exists (select 1 from BackgroundTask b where a.purpose = b.name and b.status not in ?2)")
    List<AccountingEntityProperty> findByKeyAndDoesntHaveRunningBGTAndPurpose(String key, List<BackgroundTaskStatus> statuses);
     //ACC-3597
     //ACC-5056
     @Query("select p from AccountingEntityProperty p " +
            "where p.originId = ?1 and p.originType = 'Contract' and  p.key = ?2 and p.isDeleted = false")
     AccountingEntityProperty findByOriginAndKeyAndDeletedFalse(Long id, String key);

    @Transactional
    @Modifying
    @Query("delete from AccountingEntityProperty p where p.key = :#{#key} and p.originType = :#{#origin.entityType} and p.originId = :#{#origin.id}")
    void deleteByKeyAndOrigin(@Param("key") String key, @Param("origin") BaseEntity origin);

    @Query("select a from AccountingEntityProperty a where a.key in ?1 and a.creationDate > ?2 and a.creationDate <= ?3 and a.isDeleted = false")
    List<AccountingEntityProperty> findByKeyAndCreationDateAndDeletedFalse(List<String> keys, Date from, Date to);

    @Query("select a from AccountingEntityProperty a " +
            "where a.key = ?1 and a.creationDate <= ?2 and a.isDeleted = false")
    List<AccountingEntityProperty> findByKeyAndCreationDateAndDeletedFalse(String key, Date d);
    
    @Query("select a from AccountingEntityProperty a " +
           "where a.key = ?1 and a.originType = ?2 and Date(a.value) between ?3 and ?4 and a.isDeleted = false")
    List<AccountingEntityProperty> findByKeyAndOriginTypeAndValueDateBetweenAndDeletedFalse(
        String key, String originType, Date fromDate, Date toDate);

    @Query("select count(a.id) > 0 from AccountingEntityProperty a " +
            "where a.key in ?1 and a.originId = ?2 and a.originType = 'Contract' and " +
            "(a.isDeleted = false or (a.isDeleted = true and a.lastModificationDate >= ?3)) " +
            "order by a.creationDate desc")
    boolean existsByIsDeletedTrueKeyInAndOriginAndAfterDateOfTermination(List<String> keys, Long contractId, Date dateOfTermination);

    @Query("select max(a.deletedCount) from AccountingEntityProperty a where a.originId = ?1 and a.originType = ?2 and a.key = ?3 and a.isDeleted = true")
    Integer countDeletedByKeyUniqueConstraints(Long originId, String originType, String key);

    @Query("select p from AccountingEntityProperty p " +
            "where p.key = :#{#key} and p.originType = :#{#origin.entityType} and p.originId = :#{#origin.id} order by p.isDeleted")
    List<AccountingEntityProperty> findByKeyAndOrigin(@Param("key") String key, @Param("origin") BaseEntity origin);

    @Query("select count(a.id) > 0 from AccountingEntityProperty a " +
            "where a.key in ?1 and a.originId = ?2 and a.originType = 'Contract' and a.isDeleted = false")
    boolean existsByKeyInAndOriginAndDeletedFalse(String key, Long contractId);

    @Query("select a from AccountingEntityProperty a where a.id > ?1 and a.key = ?2 and " +
            "a.originType = ?3 and a.messageSendDate <= ?4 and a.isDeleted = false")
    Page<AccountingEntityProperty> findByKeyAndOriginTypeAndMessageSentDateBeforeAndDeletedFalse(
            Long id, String key, String originType, Date d, Pageable pageable);

    @Query("select p from AccountingEntityProperty p where p.key = :#{#key} and " +
            "p.originType = :#{#origin.entityType} and p.originId = :#{#origin.id} and " +
            "p.purpose = :#{#purpose} and p.isDeleted = false")
    AccountingEntityProperty findByKeyAndOriginAndDeletedFalseAndPurposeEquals(
            @Param("key") String key, @Param("origin") BaseEntity origin, @Param("purpose") String purpose);
}
