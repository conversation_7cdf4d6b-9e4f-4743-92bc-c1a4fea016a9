package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.type.DirectDebitRejectCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Repository
public interface DirectDebitRejectionToDoRepository extends WorkflowRepository<DirectDebitRejectionToDo> {

    Page<DirectDebitRejectionToDo> findByTaskNameInAndIdNotIn(List<String> taskNames, List<Long> ids, Pageable pageable);

    Page<DirectDebitRejectionToDo> findByTaskNameInAndLastRejectCategoryInAndIdNotIn(
            List<String> taskNames, List<DirectDebitRejectCategory> rejectCategories, 
            List<Long> ids, Pageable pageable);

    @Query("select d from DirectDebitRejectionToDo d where d.taskName is not null and d.taskName <> '' and d.completed = false and d.creationDate > ?1 and d.creationDate <= ?2")
    List<DirectDebitRejectionToDo> findInCompletedTodos(Date from, Date to);

    @Query("select d from DirectDebitRejectionToDo d " +
            "where d.taskName is not null and d.completed = false and d.stopped = false")
    List<DirectDebitRejectionToDo> findActiveRejectionFlows();

    @Query("select todo from DirectDebitRejectionToDo todo " +
            "inner join DirectDebit d on d.directDebitRejectionToDo = todo " +
            "where d.contractPaymentTerm = :cpt and todo.completed = false and todo.stopped = false " +
            "order by todo.lastModificationDate desc")
    List<DirectDebitRejectionToDo> findActiveByContractPaymentTerm(
            @Param("cpt") ContractPaymentTerm contractPaymentTerm);

    @Query("select todo from DirectDebitRejectionToDo todo " +
            "inner join DirectDebit d on d.directDebitRejectionToDo = todo " +
            "where d.contractPaymentTerm.contract = :ct and todo.completed = false and todo.stopped = false and " +
                "(:todoId is null or todo.id <> :todoId) " +
            "order by todo.lastModificationDate desc")
    List<DirectDebitRejectionToDo> findActiveByContract(
            @Param("ct") Contract contract, @Param("todoId") Long todoId);

    @Query("SELECT count(t.id) > 0 FROM DirectDebit d " +
            "JOIN d.directDebitRejectionToDo t " +
            "WHERE t.stopped = 0 AND t.completed = 0 AND " +
                "d.contractPaymentTerm.isActive = 1 AND d.contractPaymentTerm.contract = ?1")
    boolean existsActiveByContract(Contract ct);

    @Query("select count(todo.id) > 0 " +
            "from DirectDebit d  " +
            "join d.directDebitRejectionToDo todo " +
            "where d.contractPaymentTerm.contract.id = :contractId and " +
                "todo.taskName is not null and todo.completed = false and " +
                "todo.stopped = false and (:category is null or todo.lastRejectCategory = :category)")
    Boolean existsActiveTodoByCategory(@Param("contractId") Long contractId, @Param("category") DirectDebitRejectCategory category);

    @Query("select count(todo.id) > 0 " +
            "from DirectDebit d  " +
            "join d.directDebitRejectionToDo todo " +
            "where d.contractPaymentTerm.contract = ?1 and " +
                "todo.taskName is not null and todo.completed = false and " +
                "todo.stopped = false and d.category = 'B'")
    Boolean existsActiveTodoOnDdb(Contract c);

    @Query("select count(todo.id) > 0 " +
            "from DirectDebit d  " +
            "left join d.directDebitRejectionToDo todo " +
            "left join d.directDebitBouncingRejectionToDo bouncedTodo " +
            "where d.contractPaymentTerm.contract.id = ?1 and " +
            "   ((d.category = 'A' and d.MStatus = 'IN_COMPLETE') or (d.category = 'B' and d.status = 'IN_COMPLETE')) and " +
            "   ((todo.taskName is not null and todo.completed = false and todo.stopped = false) or " +
            "       (bouncedTodo.taskName is not null and bouncedTodo.completed = false and bouncedTodo.stopped = false))")
    Boolean existsIncompleteDDRelatedToRejectionFlow(Long id);
}
