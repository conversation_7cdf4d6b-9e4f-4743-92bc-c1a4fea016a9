package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.DDFDDDataEntryDto;
import com.magnamedia.module.type.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 5, 2019
 *         Jirra ACC-475
 */
@Repository
public interface DirectDebitFileRepository extends BaseRepository<DirectDebitFile> {

    List<DirectDebitFile> findByIdInAndStatus(List<Long> ids, DirectDebitFileStatus status);

    // ACC-1367
    List<DirectDebitFile> findByIdIn(List<Long> ids);

    Page<DirectDebitFile> findByIdIn(List<Long> ids, Pageable pageable);

    boolean existsByDirectDebitAndStatus(DirectDebit directDebit, DirectDebitFileStatus directDebitFileStatus);

    @Query("select ddf.directDebit from DirectDebitFile ddf where ddf.id = :id")
    DirectDebit getDirectDebitFromDirectDebitFile(@Param("id") Long id);

    // SAL-2034
    DirectDebitFile findFirstByStatusAndDirectDebit_ContractPaymentTerm_Contract_ClientOrderByCreationDateDesc(
            DirectDebitFileStatus status, Client client);

    // ACC-1587
    @Query("select ddf.id from DirectDebitFile ddf " +
            "where ddf.applicationId = ?1 AND ddf.startDate = ?2 AND " +
                "ddf.expiryDate = ?3 AND ddf.amount = ?4 AND " +
                "ddf.accountName = ?5 AND ddf.ibanNumber = ?6 AND " +
                "((ddf.applicationId like 'A%' or ddf.applicationId like 'M%') OR " +
                    "(ddf.status = 'SENT' AND (ddf.applicationId NOT like 'A%' AND ddf.applicationId NOT like 'M%'))) ")
    List<Long> findByApplicationIdAndStartDateAndExpiryDateAndAmountAndAccountNameAndIbanNumber(
            String applicationId, Date startDate, Date expiryDate, double amount, String account, String iban);

    // ACC-1134, ACC-1587
    List<DirectDebitFile> findByDdaRefNo(String ddaRefNo);

    @Query("select ddf from DirectDebitFile ddf where ddf.ddMethod = :ddMethod and ddf.ddFrequency = :ddType and " +
            "ddf.directDebit.contractPaymentTerm.contract = :contract and " +
            "(ddf.applicationId like 'A%' or ddf.applicationId like 'M%') " +
            "order by ddf.applicationId DESC")
    List<DirectDebitFile> findByDdMethodAndDdFrequencyAndDirectDebit_ContractPaymentTerm_ContractOrderByApplicationIdDesc(
            @Param("ddMethod") DirectDebitMethod method, @Param("ddType") DirectDebitType type, @Param("contract") Contract contract);

    // ACC-1588
    @Query("SELECT ddf.id FROM DirectDebitFile ddf  WHERE ddf.directDebitSignature is null")
    List<BigInteger> findDDFWithNoSignature();

    // ACC-1811
    @Query("select distinct ddf from DirectDebitFile ddf " +
            "join ddf.directDebit.contractPaymentTerm.contract con " +
            // ACC-6003
            "left join Attachment a on ddf.directDebit.ddcId is not null and a.ownerId = ddf.directDebit.ddcId and a.ownerType = 'AppsServiceDDApprovalTodo' and a.tag = 'client_approval' " +
            "where ((ddf.ddMethod = 'AUTOMATIC' and ddf.directDebit.status = 'PENDING') or " +
                    "(ddf.ddMethod = 'MANUAL' and ddf.directDebit.MStatus = 'PENDING')) and " +
                "ddf.ddStatus = 'PENDING' and ddf.status = 'NOT_SENT' and ddf.downloadedByRPA = false and " +
                "(((ddf.applicationId like 'CONTX-%' or ddf.applicationId like 'contx%' or " +
                    "ddf.applicationId like 'Contr-%' or ddf.applicationId like 'Conta-%') and " +
                    "(NOT EXISTS (SELECT 1 FROM DirectDebitFile ddf1 " +
                            "WHERE ddf1.status in ('SENT', 'APPROVED') and ddf1.directDebit = ddf.directDebit))) or " +
                "(ddf.applicationId not like 'CONTX-%' and ddf.applicationId not like 'contx%' and " +
                    "ddf.applicationId not like 'Contr-%' and ddf.applicationId not like 'Conta-%')) and " +
                "ddf.directDebit.contractPaymentTerm.isActive = true and ddf.confirmedBankInfo = 1 and " +
                "con.client.name like CONCAT('%',:clientName,'%') and " +
                "(:#{#bankName == null} = true or ddf.bankName = :bankName) and " +
                "(:#{#contractId == null} = true or con.id = :contractId) and " +
                "(:#{#paymentTypeId == null} = true or " +
                    "(exists (select 1 from ContractPayment cp " +
                            "where cp.directDebit.id = ddf.directDebit.id and cp.paymentType.id = :paymentTypeId))) and " +
                "con.status not in ('CANCELLED','EXPIRED') and " +
                "con.cancelledWithinFirstXDays = 0 and " +
                "(ddf.directDebit.category = 'A' or ddf.forBouncingPayment = 1 or ddf.startDate >= :date or " +
                    "(ddf.ddMethod = 'MANUAL' and not exists(SELECT 1 FROM DirectDebitFile ddf2 " +
                        "WHERE ddf2.status = 'NOT_SENT' and ddf2.directDebit = ddf.directDebit and " +
                            "ddf2.ddMethod = 'AUTOMATIC' and ddf.startDate < :date ))) and" +
                "(a is not null or con.creationDate < :startDate) " +
            "group by ddf.applicationId")
    Page<DirectDebitFile> getPendingDDFsGroupByApplicationIdFilteredByContract(
            @Param("clientName") String clientName, @Param("contractId") Long contractId,
            @Param("bankName") String bankName, @Param("paymentTypeId") Long paymentTypeId,
            @Param("date") Date date, @Param("startDate") Date startDate, Pageable pageable);

    @Query("select new map(ddf.id as ddfId, dd.id as ddId, cpt.id as ddCptId, " +
                "cpt.isActive as ddCptIsActive, con.id as ddContractId, cl.id as ddClientId, cl.name as ddClientName," +
                "ddf.applicationId as applicationId, ddf.startDate as startDate, ddf.creationDate as creationDate, " +
                "ddf.amount as amount, ddf.ddMethod as ddMethod,ddf.ddFrequency as ddFrequency, ddf.bankName as bankName, ddf.notes as notes) " +
            "from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "join dd.contractPaymentTerm cpt " +
            "join cpt.contract con " +
            "join con.client cl " +
            // ACC-6003
            "left join Attachment a on dd.ddcId is not null and a.ownerId = dd.ddcId and a.ownerType = 'AppsServiceDDApprovalTodo' and a.tag = 'client_approval' " +
            "where ddf.id > :lastId and " +
            "con.status not in ('CANCELLED','EXPIRED') and con.cancelledWithinFirstXDays = 0 and " +
            "ddf.ddStatus = 'PENDING' and ddf.status = 'NOT_SENT' and ddf.downloadedByRPA = false and " +
            "cpt.isActive = true and ddf.confirmedBankInfo = 1 and " +
            "cl.name like CONCAT('%',:clientName,'%') and " +
            "((ddf.ddMethod = 'AUTOMATIC' and dd.status = 'PENDING') or " +
                "(ddf.ddMethod = 'MANUAL' and dd.MStatus = 'PENDING')) and " +
            "(((ddf.applicationId like 'CONTX-%' or ddf.applicationId like 'contx%' or " +
                    "ddf.applicationId like 'Contr-%' or ddf.applicationId like 'Conta-%') and " +
                        "(NOT EXISTS (SELECT 1 FROM DirectDebitFile ddf1 " +
                            "WHERE ddf1.status in ('SENT', 'APPROVED') and ddf1.directDebit = dd))) or " +
                    "(ddf.applicationId not like 'CONTX-%' and ddf.applicationId not like 'contx%' and " +
                    "ddf.applicationId not like 'Contr-%' and ddf.applicationId not like 'Conta-%')) and " +
            "(:#{#bankName == null} = true or ddf.bankName = :bankName) and " +
            "(:#{#contractId == null} = true or con.id = :contractId) and " +
            "(:#{#paymentTypeId == null} = true or " +
                "(exists (select 1 from ContractPayment cp " +
                    "where cp.directDebit.id = dd.id and cp.paymentType.id = :paymentTypeId))) and " +
                "(dd.category = 'A' or ddf.forBouncingPayment = 1 or ddf.startDate >= :date or " +
                    "(ddf.ddMethod = 'MANUAL' and not exists(SELECT 1 FROM DirectDebitFile ddf2 " +
                        "WHERE ddf2.status = 'NOT_SENT' and ddf2.directDebit = dd and " +
                        "ddf2.ddMethod = 'AUTOMATIC' and ddf.startDate < :date ))) and" +
                "(a is not null or con.creationDate < :startDate) " +
            "group by ddf.applicationId")
    Page<Map> getPendingDDFsGroupByApplicationIdFilteredByContractAcc8544(
            @Param("lastId") Long lastId, @Param("clientName") String clientName, @Param("contractId") Long contractId,
            @Param("bankName") String bankName, @Param("paymentTypeId") Long paymentTypeId,
            @Param("date") Date date, @Param("startDate") Date startDate, Pageable pageable);

    //Jirra ACC-3630
    @Query("select distinct ddf from DirectDebitFile ddf " +
            "join ddf.directDebit.contractPaymentTerm.contract con " +
            // ACC-6003
            "left join Attachment a on ddf.directDebit.ddcId is not null and a.ownerId = ddf.directDebit.ddcId and a.ownerType = 'AppsServiceDDApprovalTodo' and a.tag = 'client_approval' " +
            "where ddf.id IN (:ddfIds) and " +
                "((ddf.ddMethod = 'AUTOMATIC' and ddf.directDebit.status = 'PENDING') or " +
                    "(ddf.ddMethod = 'MANUAL' and ddf.directDebit.MStatus = 'PENDING')) and " +
                "ddf.ddStatus = 'PENDING' and ddf.status = 'NOT_SENT' and ddf.downloadedByRPA = false and " +
                "(((ddf.applicationId like 'CONTX-%' or ddf.applicationId like 'contx%' or " +
                    "ddf.applicationId like 'Contr-%' or ddf.applicationId like 'Conta-%') and " +
                    "(NOT EXISTS (SELECT 1 FROM DirectDebitFile ddf1 " +
                        "WHERE ddf1.status in ('SENT', 'APPROVED') and ddf1.directDebit = ddf.directDebit))) or " +
                "(ddf.applicationId not like 'CONTX-%' and ddf.applicationId not like 'contx%' and " +
                     "ddf.applicationId not like 'Contr-%' and ddf.applicationId not like 'Conta-%')) and " +
                "ddf.directDebit.contractPaymentTerm.isActive = true and ddf.confirmedBankInfo = 1 and " +
                "con.client.name like CONCAT('%',:clientName,'%') and " +
                "(:#{#bankName == null} = true or ddf.bankName = :bankName) and " +
                "(:#{#contractId == null} = true or con.id = :contractId) and " +
                "(:#{#paymentTypeId == null} = true or " +
                    "(exists (select 1 from ContractPayment cp " +
                        "where cp.directDebit.id = ddf.directDebit.id and cp.paymentType.id = :paymentTypeId))) and " +
                "con.status not in ('CANCELLED','EXPIRED') and " +
                "con.cancelledWithinFirstXDays = 0 and " +
                "(ddf.directDebit.category = 'A' or " +
                                    "ddf.forBouncingPayment = 1 or " + // ACC-6503
                                            "ddf.startDate >= :date or " +
                    "(ddf.ddMethod = 'MANUAL' and not exists(SELECT 1 FROM DirectDebitFile ddf2 " +
                        "WHERE ddf2.status = 'NOT_SENT' and ddf2.directDebit = ddf.directDebit and " +
                            "ddf2.ddMethod = 'AUTOMATIC' and ddf.startDate < :date ))) and " +
                "(a is not null or con.creationDate < :startDate) " +
            "group by ddf.applicationId")
    List<DirectDebitFile> getPendingDDFsGroupByApplicationIdFilteredByContractAndIdsIn(
            @Param("clientName") String clientName, @Param("contractId") Long contractId,
            @Param("bankName") String bankName, @Param("paymentTypeId") Long paymentTypeId,
            @Param("date") Date date, @Param("startDate") Date startDate, @Param("ddfIds") List<Long> ddfIds);

    // ACC-3630
    @Query("select ddf from DirectDebitFile ddf " +
            "where ddf.id IN ?1 and " +
                "((ddf.ddMethod = 'AUTOMATIC' and ddf.directDebit.status = 'PENDING') or " +
                    "(ddf.ddMethod = 'MANUAL' and ddf.directDebit.MStatus = 'PENDING')) and " +
                "ddf.ddStatus = 'PENDING' and ddf.status = 'NOT_SENT' and ddf.downloadedByRPA = ?3 and " +
                "(((ddf.applicationId like 'CONTX-%' or ddf.applicationId like 'contx%' or " +
                    "ddf.applicationId like 'Contr-%' or ddf.applicationId like 'Conta-%') " +
                    "and (NOT EXISTS (SELECT 1 FROM DirectDebitFile ddf1 " +
                    "WHERE ddf1.status in ('SENT', 'APPROVED') and ddf1.directDebit = ddf.directDebit))) or " +
                "(ddf.applicationId not like 'CONTX-%' and ddf.applicationId not like 'contx%' " +
                    "and ddf.applicationId not like 'Contr-%' and ddf.applicationId not like 'Conta-%')) and " +
                "ddf.directDebit.contractPaymentTerm.isActive = true and ddf.confirmedBankInfo = 1 and " +
                "ddf.directDebit.contractPaymentTerm.contract.status not in ('CANCELLED','EXPIRED') and " +
                "ddf.directDebit.contractPaymentTerm.contract.cancelledWithinFirstXDays = 0 and " +
                "(ddf.directDebit.category = 'A' or ddf.startDate >= ?2 or " +
                    "(ddf.ddMethod = 'MANUAL' and not exists(SELECT 1 FROM DirectDebitFile ddf2 " +
                        "WHERE ddf2.status = 'NOT_SENT' and ddf2.directDebit = ddf.directDebit and " +
                            "ddf2.ddMethod = 'AUTOMATIC' and ddf.startDate < ?2))) " +
            "group by ddf.applicationId")
    List<DirectDebitFile> getPendingDDFsAndIdsIn(
            List<Long> ddfIds, Date date, boolean downloadedByRPA);

    // ACC-1811
    @Query("select ddf from DirectDebitFile ddf " +
            "left join ddf.directDebit dd " +
            "left join dd.contractPaymentTerm cpt " +
            "left join cpt.contract c " +
            "left join c.client " +
            "left join Attachment a on ddf.directDebit.ddcId is not null and a.ownerId = dd.ddcId and a.ownerType = 'AppsServiceDDApprovalTodo' and a.tag = 'client_approval' " +
            "where ddf.ddStatus = 'PENDING_DATA_ENTRY' and " +
                "ddf.directDebit.contractPaymentTerm.contract.status in ('PLANNED_RENEWAL', 'ACTIVE') " +
                "and (NOT EXISTS (SELECT 1 FROM Payment p " +
                    "where p.directDebitId = ddf.directDebit.id and p.bouncedFlowPausedForReplacement = true)) and " +
            "(a is not null or c.creationDate < :startDate) " +
            "group by ddf.directDebit.ddBankInfoGroup")
    Page<DirectDebitFile> getPendingDDFsGroupByDDBankInfoGroup(@Param("startDate") Date startDate, Pageable pageable);

    @Query("select new com.magnamedia.entity.dto.DDFDDDataEntryDto(ddf.id, dd.id, cpt.id, c.id, cl.id, cl.name, prospectType.id," +
                "prospectType.code, dd.dataEntryNotes, ddf.needAccountantReConfirmation, ddf.rejectCategory, ddf.rejectionReason, ddf.creationDate) " +
            "from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "join dd.contractPaymentTerm cpt " +
            "join cpt.contract c " +
            "join c.contractProspectType prospectType " +
            "join c.client cl " +
            "left join Attachment a on dd.ddcId is not null and a.ownerId = dd.ddcId and a.ownerType = 'AppsServiceDDApprovalTodo' and a.tag = 'client_approval' " +
            "where ddf.ddStatus = 'PENDING_DATA_ENTRY' and c.status in ('PLANNED_RENEWAL', 'ACTIVE') " +
                "and not exists (select 1 from Payment p " +
                    "where p.directDebitId = ddf.directDebit.id and p.bouncedFlowPausedForReplacement = true) and " +
            "(a is not null or c.creationDate < :startDate) " +
            "group by dd.ddBankInfoGroup")
    Page<DDFDDDataEntryDto> getPendingDDFsGroupByDDBankInfoGroupAcc8544(@Param("startDate") Date startDate, Pageable pageable);

    @Query("select ddf.directDebit.ddBankInfoGroup " +
            "from DirectDebitFile ddf " +
            "left join ddf.directDebit dd " +
            "left join dd.contractPaymentTerm cpt " +
            "left join cpt.contract c left join c.client " +
            "where ddf.ddStatus = 'PENDING_DATA_ENTRY' and " +
                "ddf.directDebit.contractPaymentTerm.contract.status in ('PLANNED_RENEWAL', 'ACTIVE') and " +
                "ddf.directDebit.contractPaymentTerm = ?1 and " +
                "ddf.directDebit.id not in ?2 " +
            "group by ddf.directDebit.ddBankInfoGroup order by ddf.lastModificationDate DESC")
    List<Long> getPendingDDFsByCPTAndDDIdNotInGroupByDDBankInfoGroup(ContractPaymentTerm cpt, List<Long> ddsIds);

    // ACC-2059
    boolean existsByDirectDebit_DdBankInfoGroupAndDdDataEntryNotificationSentAndDdStatus(Long ddBankInfoGroup, boolean ddDataEntryNotificationSent, DirectDebitStatus ddStatus);

    // ACC-2136
    List<DirectDebitFile> findByDirectDebit_Id(Long ddId);

    // ACC-2136
    List<DirectDebitFile> findByDirectDebitAndDdMethod(DirectDebit directDebit, DirectDebitMethod method);

    // ACC-2225
    boolean existsByDirectDebitAndDdStatusNotInAndIdNotInAndDdMethod(DirectDebit directDebit, List<DirectDebitStatus> statuses, List<Long> ids, DirectDebitMethod directDebitMethod);

    boolean existsByDirectDebitAndDdMethod(DirectDebit directDebit, DirectDebitMethod directDebitMethod);

    List<DirectDebitFile> findByDirectDebitOrderByIdDesc(DirectDebit directDebit);

    Page<DirectDebitFile> findByDdStatusAndStatusChangeDateBefore(DirectDebitStatus ddStatus, Date statusChangeDate, Pageable pageable);

    boolean existsByDirectDebit_ContractPaymentTerm(ContractPaymentTerm contractPaymentTerm);

    @Query(nativeQuery = true,
            value = "select temp.id from ( " +
            "select distinct d.id, d.STATUS, d.DD_STATUS, " +
            "case WHEN ct.STATUS = 'ACTIVE' and d.DD_STATUS = 'CONFIRMED' then 0 " +
                "WHEN ct.STATUS = 'ACTIVE' and d.DD_STATUS = 'EXPIRED' then 1 " +
                "WHEN ct.STATUS = 'ACTIVE' AND d.DD_STATUS = 'PENDING_FOR_CANCELLATION' AND dr.ID IS NOT NULL THEN 1 " +
                "WHEN ct.STATUS = 'ACTIVE' AND d.DD_STATUS = 'CANCELED' AND dr.ID IS NOT NULL THEN 2 " +
                "WHEN (ct.STATUS = 'CANCELLED' or ct.STATUS = 'EXPIRED') and d.DD_STATUS = 'CONFIRMED' then 3 " +
                "WHEN (ct.STATUS = 'CANCELLED' or ct.STATUS = 'EXPIRED') and d.DD_STATUS = 'EXPIRED' then 4 " +
                "WHEN (ct.STATUS = 'CANCELLED' OR ct.STATUS = 'EXPIRED') AND d.DD_STATUS = 'PENDING_FOR_CANCELLATION' AND dr.ID IS NOT NULL THEN 4 " +
                "WHEN (ct.STATUS = 'CANCELLED' OR ct.STATUS = 'EXPIRED') AND d.DD_STATUS = 'CANCELED' AND dr.ID IS NOT NULL THEN 5 " +
                "ELSE 6 end as priority, dr2.LAST_MODIFICATION_DATE " +
            "from DIRECTDEBITFILES d " +
            "inner join DIRECTDEBITSIGNATURES sign on d.DIRECT_DEBIT_SIGNATURE_ID = sign.id " +
            "inner join ATTACHMENTS a on a.TAG = 'direct_debit_signature' AND " +
                    "a.OWNER_ID = sign.id and a.OWNER_TYPE = 'DirectDebitSignature' " +
            "inner join DIRECTDEBITS dd on d.DIRECT_DEBIT_ID = dd.id " +
            "inner join CONTRACTPAYMENTTERMS c on dd.CONTRACT_PAYMENT_TERM_ID = c.id " +
            "inner join CONTRACTS ct on c.CONTRACT_ID = ct.id " +
            "left outer join DIRECTDEBITFILES_REVISIONS dr on d.id = dr.id AND dr.REVISION = " +
                "(SELECT drr.REVISION FROM DIRECTDEBITFILES_REVISIONS drr " +
                "WHERE drr.id = d.id AND drr.DD_STATUS = 'CONFIRMED' LIMIT 1) " +
            "left outer join DIRECTDEBITFILES_REVISIONS dr2 on d.id = dr2.id AND dr2.REVISION = " +
                "(SELECT drr.REVISION FROM DIRECTDEBITFILES_REVISIONS drr " +
                "WHERE drr.id = d.id AND drr.STATUS = 'APPROVED' AND drr.STATUS_MODIFIED = 1 " +
                "ORDER BY drr.LAST_MODIFICATION_DATE DESC LIMIT 1) " +
            "where d.status = 'APPROVED' and ct.CLIENT_ID = ?1 and sign.SIGNATURE_STATUS != 'REJECTED' AND dd.IS_DELETED = 0 " +
            "order by priority , dr2.LAST_MODIFICATION_DATE desc , d.id desc ) as temp " +
                "where temp.priority < 6 limit 1")
    BigInteger getLastApprovedSignatureByClient(@Param("client") Client client);

    @Query("select ddf from DirectDebitFile ddf where ddf.id <> ?1 and ddf.directDebitSignature = ?2 and ddf.status = 'SENT'")
    List<DirectDebitFile> findByDirectDebitSignatureAndStatusIsSent(Long id, DirectDebitSignature directDebitSignature);

    @Query("select count(d.id) > 1 from DirectDebitFile d " +
            "where d.directDebit.contractPaymentTerm.contract.client = ?1 " +
            "and exists (select 1 from Attachment a where a.ownerId = d.id and a.ownerType = 'DirectDebitFile' and a.tag = 'bank_info_account_name')")
    boolean existsByDirectDebit_ContractPaymentTerm_Contract_ClientAndRejectCategory(Client client);

    boolean existsByDirectDebit_ContractPaymentTerm_Contract_ClientAndRejectCategory(Client client, DirectDebitRejectCategory rejectCategory);

    boolean existsByDirectDebit_ContractPaymentTerm_ContractAndRejectCategory(Contract contract, DirectDebitRejectCategory rejectCategory);

    @Query("select ddf from DirectDebitFile ddf " +
            "where ((ddf.ddMethod = 'AUTOMATIC' and ddf.directDebit.status = 'PENDING') or " +
                    "(ddf.ddMethod = 'MANUAL' and ddf.directDebit.MStatus = 'PENDING')) and " +
                "ddf.ddStatus = 'PENDING' and ddf.status = 'NOT_SENT' and ddf.downloadedByRPA = false and " +
                "(((ddf.applicationId like 'CONTX-%' or ddf.applicationId like 'contx%' or " +
                    "ddf.applicationId like 'Contr-%' or ddf.applicationId like 'Conta-%') " +
                    "and (NOT EXISTS (SELECT 1 FROM DirectDebitFile ddf1 " +
                        "WHERE ddf1.status in ('SENT', 'APPROVED') and ddf1.directDebit = ddf.directDebit))) or " +
                "(ddf.applicationId not like 'CONTX-%' and ddf.applicationId not like 'contx%' " +
                    "and ddf.applicationId not like 'Contr-%' and ddf.applicationId not like 'Conta-%')) and " +
                "ddf.directDebit.contractPaymentTerm.isActive = true and ddf.confirmedBankInfo = 1 and " +
                "ddf.directDebit.contractPaymentTerm.contract.status not in ('CANCELLED','EXPIRED') and " +
                "ddf.directDebit.contractPaymentTerm.contract.cancelledWithinFirstXDays = 0 and " +
                "(ddf.directDebit.category = 'B' and ddf.startDate < ?1 and " +
                    "(ddf.forBouncingPayment = 0 or ddf.forBouncingPayment is null) and " +
                    "(ddf.ddMethod = 'AUTOMATIC' or (ddf.ddMethod = 'MANUAL' and " +
                        "exists(SELECT 1 FROM DirectDebitFile ddf2 " +
                            "WHERE ddf2.status = 'NOT_SENT' and ddf2.directDebit = ddf.directDebit " +
                                "and ddf2.ddMethod = 'AUTOMATIC' and ddf.startDate < ?1)))) " +
            "group by ddf.applicationId")
    List<DirectDebitFile> findNewDdfCreatedStartDatePassed(Date date);

    @Query("SELECT COUNT (d.id) > 0 FROM DirectDebitFile ddf " +
            "join ddf.directDebit d " +
            "where d.contractPaymentTerm = ?1 and ddf.status = 'SENT' and d.category = 'B' " +
            "and (d.status = ?2 or d.MStatus = ?2) ")
    Boolean existsByContractPaymentTermAndDdCategoryBAndDdStatusInAndStatusSent(
            ContractPaymentTerm cpt, DirectDebitStatus statuses);
}