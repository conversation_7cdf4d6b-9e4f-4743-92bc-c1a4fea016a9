package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
@Repository
public interface BankDirectDebitCancelationRecordRepository
        extends BaseRepository<BankDirectDebitCancelationRecord> {

    List<BankDirectDebitCancelationRecord> findByBankDirectDebitCancelationFile(BankDirectDebitCancelationFile bankDirectDebitCancelationFile);

    @Query("select b.id from BankDirectDebitCancelationRecord b " +
            "left join b.directDebitFile ddf " +
            "where b.bankDirectDebitCancelationFile.id = ?1 and ddf is not null and " +
                "ddf.ddStatus = 'PENDING_FOR_CANCELLATION' and b.confirmed = false and b.status = 'REJECTED'")
    List<Long> findIdsByBankDirectDebitCancellationFileId(Long fileId);

    @Query("select count(record) > 0 from BankDirectDebitCancelationRecord record where record.bankDirectDebitCancelationFile = :file " +
            "and (record.cbStatus is null or record.cbStatus <> :cbStatus) and record.confirmed = :confirmed")
    boolean existsByCbStatusNotAndBankDirectDebitCancelationFileAndConfirmed(@Param("cbStatus") String cbStatus, @Param("file") BankDirectDebitCancelationFile file, @Param("confirmed") boolean confirmed);

    List<BankDirectDebitCancelationRecord> findByCbStatusAndBankDirectDebitCancelationFile(String cbStatus, BankDirectDebitCancelationFile file);
}