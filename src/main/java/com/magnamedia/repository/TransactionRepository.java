package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementFile;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.Transaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Created at Nov 24, 2017
 */
@Repository
public interface TransactionRepository extends BaseRepository<Transaction> {

    @Query("SELECT t FROM Transaction t WHERE (t.fromBucket = ?1 OR t.toBucket = ?1)AND t.creationDate > ?2")
    List<Transaction> getAffectedTransactions(Bucket bucket, Date transactionDate);

    @Query("SELECT t FROM Transaction t WHERE (t.fromBucket = ?1 OR t.toBucket = ?1 OR t.fromBucket = ?2 OR t.toBucket = ?2) AND t.creationDate > ?3")
    List<Transaction> getAffectedTransactions(Bucket bucketFrom, Bucket bucketTo, Date transactionDate);

    Transaction findOneById(Long id);

    List<Transaction> findByCreationDateGreaterThanEqualOrderByCreationDateAscIdAsc(Date creationDate);

    @Query("SELECT amount FROM Transaction t where t = ?1")
    Double findAmountByTransaction(Transaction t);

    List<Transaction> findByFromBucket(Bucket fromBucket);

    List<Transaction> findByToBucket(Bucket toBucket);

    List<Transaction> findByBusinessObjectIdAndBusinessObjectType(String businessObjectId, String businessObjectType);

    boolean existsByPaymentId(Long paymentId);

    boolean existsByBusinessObjectIdAndBusinessObjectType(String businessObjectId, String businessObjectType);

    Transaction findFirstByPaymentId(Long paymentId);

    // ACC-1995
    List<Transaction> findByBankStatementFileAndDateGreaterThanEqual(BankStatementFile file, Date date);

    @Query("SELECT t FROM Transaction t WHERE t.paymentId IS NOT NULL and " +
            "(not EXISTS (SELECT 1 from Attachment att where att.ownerType = 'Transaction' and att.ownerId = t.id and tag like 'tr_payment_tax_invoice%')) and " +
            "(EXISTS (SELECT 1 from Attachment att where att.ownerType = 'Payment' and att.ownerId = t.paymentId and tag like 'payment_tax_invoice%')) and " +
            "t.creationDate >= ?1")
    Page<Transaction> getDataForACC2492(Date date, Pageable pageable);

    @Query("SELECT t FROM Transaction t WHERE t.paymentId IS NOT NULL and " +
            "(not EXISTS (SELECT 1 from Attachment att where att.ownerType = 'Transaction' and att.ownerId = t.id and tag like 'tr_payment_tax_credit_note%')) and " +
            "(EXISTS (SELECT 1 from Attachment att where att.ownerType = 'Payment' and att.ownerId = t.paymentId and tag like 'payment_tax_credit_note%')) and " +
            "t.creationDate >= ?1")
    Page<Transaction> getDataForACC2492_2(Date date, Pageable pageable);

    @Query(nativeQuery = true,
            value = "select tr_rev.ID, tr_rev.REVISION from TRANSACTIONS_REVISIONS AS tr_rev INNER JOIN TRANSACTIONS tr ON tr_rev.ID = tr.ID " +
                    "where tr_rev.CREATION_TRIGGERED_AUTOMATICALLY = 0 AND " +
                        "tr_rev.CREATION_DATE >= :#{#fromDate} AND tr_rev.CREATION_DATE <= :#{#toDate} AND tr.DONE_BY_COO = false AND " +
                        "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = tr.ID and cooQ.RELATED_ENTITY_TYPE = tr.ENTITY_TYPE)) AND " +
                        "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = tr.ID and cooQ.RELATED_ENTITY_TYPE = tr.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = ''))) " +
                    "GROUP BY tr_rev.ID")
    Page<ExpensePaymentRepository.IdAndRevision> getQuestionedCreatedManualTransactionsByDate(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);

    @Query(nativeQuery = true,
            value = "select tr.ID from TRANSACTIONS tr " +
                    "where tr.CREATION_TRIGGERED_AUTOMATICALLY = 0 AND " +
                        "tr.CREATION_DATE >= :#{#fromDate} AND tr.CREATION_DATE <= :#{#toDate} AND " +
                        "tr.DONE_BY_COO = false")
    Page<BigInteger> getCreatedManualTransactionsByDateWithNoAchive(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);
    
    @Query(nativeQuery = true,
            value = "select tr.ID from TRANSACTIONS tr " +
                    "where tr.CREATION_TRIGGERED_AUTOMATICALLY = 0 AND " +
                        "tr.CREATION_DATE >= :#{#fromDate} AND tr.CREATION_DATE <= :#{#toDate}")
    Page<BigInteger> getCreatedManualTransactionsByDateWithAchive(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);
    
    List<Transaction> findByExpensePaymentId(Long expensePaymentId);

    @Query("select t from Transaction t " +
            "where t.fromBucket = ?1 and t.expensePaymentId is null and t.date >= ?2 " +
            "and (?3 is null or t.id = ?3) " +
            "and not exists(select 1 from CreditCardReconciliationStatementDetails c where c.transactionId = t.id)")
    List<Transaction> findAlreadyMatchedByBucketAndDateAndNotLinkedToDetails(Bucket bucket, Date date, String search);

    @Query("select sum(coalesce(h.amount, t.amount)) " +
            "from HousemaidTransaction h " +
            "join h.transaction t " +
            "where h.housemaid.id = ?1 and t.date >= ?2 and t.date <= ?3")
    Double calculateHousemaidTransactionAmount(Long housemaidId, Date s, Date e);
}