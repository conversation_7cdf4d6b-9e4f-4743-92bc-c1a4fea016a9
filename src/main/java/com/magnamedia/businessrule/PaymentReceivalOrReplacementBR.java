package com.magnamedia.businessrule;

import com.magnamedia.controller.DDMessagingToDoController;
import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.AccountingModuleMainJob;
import com.magnamedia.service.*;
import com.magnamedia.service.payment.PaymentChangedToReceivedOrReplacedService;
import com.magnamedia.service.paymentstate.handlers.received.PaymentReceivedNotificationHandler;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.joda.time.DateTime;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Sep 02, 2020
 *         Jirra ACC-2503
 */


// set RequiredForBouncing false
// cancel dds if contract scheduled for termination and if we do not want money from the client
// stop rejection flow if there are no other bounced payment
// cancel ddf for bouncing payment
// hide bounced flow messages
// create payrollAccountantTodos for conditional refund
// runDDExpiredJob
// handle by switching bank account flow
// handle by OEC flow
@BusinessRule(entity = Payment.class, events = { BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate },
        fields = {"id", "contract.id", "directDebit.id", "status", "dateOfPayment", "requiredForBouncing",
                "replaced", "amountOfPayment", "clientInformedAboutRefund", "contract.id", "requiredForUnfitToWork",
                "typeOfPayment.id", "typeOfPayment.code", "parentWrapperId", "methodOfPayment", "replacementFor.id", "online"})
public class PaymentReceivalOrReplacementBR implements BusinessAction<Payment> {
    
    private static final Logger logger = Logger.getLogger(PaymentReceivalOrReplacementBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "PaymentReceivalOrReplacementBR Validation.");
        logger.log(Level.SEVERE, prefix + prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        logger.log(Level.SEVERE, prefix + "event: " + event);
        logger.log(Level.SEVERE, prefix + "status: " + entity.getStatus());
        

        PicklistItem paymentType = Setup.getRepository(PicklistItemRepository.class).findOne(entity.getTypeOfPayment().getId());
        if(paymentType.hasTag("refund")) {
            return false;
        }
        
        if(entity.getStatus().equals(PaymentStatus.RECEIVED) && event.equals(BusinessEvent.AfterCreate)) {
            return true;
        }
        
        return PaymentHelper.isChangedToReceived(entity) || PaymentHelper.isChangedToReplaced(entity);
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "PaymentReceivalOrReplacementBR Execution.");
        DirectDebitCancelationToDoController directDebitCancelationToDoController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        DDMessagingToDoController ddMessagingToDoCtrl = Setup.getApplicationContext().getBean(DDMessagingToDoController.class);
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);

        Contract contract = contractRepository.findOne(entity.getContract().getId());

        Map<String, Object> map = Setup.getApplicationContext()
                .getBean(PaymentChangedToReceivedOrReplacedService.class)
                .apply(entity, contract);

        if (PaymentHelper.isChangedToReplaced(entity)) {
            logger.info(prefix + "Payment is changed to Replaced");
            // ACC-2503
            if (entity.getContract() != null && entity.getContract().getId() != null) {
                logger.log(Level.SEVERE, prefix + "contract id: " + (contract != null ? contract.getId() : "contract Not Found"));
                if (entity.getRequiredForBouncing() != null && entity.getRequiredForBouncing() && contract != null &&
                        contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                    map.put("requiredForBouncing", Boolean.FALSE);
                    // ACC-7661
                    if (!contract.getStatus().equals(ContractStatus.ACTIVE) || (contract.getScheduledDateOfTermination() != null &&
                            DirectDebitService.shouldRunDDsCancellationScheduledJob(true) &&
                            !BouncingFlowService.shouldRetractBouncingFlowTermination(entity))) {
                        logger.log(Level.INFO, "start calling createToDoIfValid to cancel the contract: " + contract.getId() + " for payment: " + entity.getId());
                        directDebitCancelationToDoController.createToDoIfValid(contract, Arrays.asList(entity.getId()),
                                DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
                        logger.log(Level.INFO, "finish calling createToDoIfValid to cancel the contract: " + contract.getId() + " for payment: " + entity.getId());
                    }
                }
            }

            // ACC-2577
            if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
                DirectDebit directDebit = directDebitRepository.findOne(entity.getDirectDebit().getId());
                if (directDebit == null) {
                    logger.log(Level.SEVERE, prefix + "DD Not Found");
                    return map;
                }

                boolean isThereOtherBouncedNotReplacedPayment = QueryService.existsEntity(Payment.class,
                        "e.directDebitId = :p0 and e.id <> :p1 and e.status = :p2 and e.replaced = false",
                        new Object[]{directDebit.getId(), entity.getId(), PaymentStatus.BOUNCED});

                if (!isThereOtherBouncedNotReplacedPayment) {
                    if (directDebit.getDirectDebitBouncingRejectionToDo() != null) {
                        DirectDebitRejectionToDo rejectionToDo = directDebit.getDirectDebitBouncingRejectionToDo();
                        rejectionToDo.setCompleted(true);
                        rejectionToDo.setStopped(true);
                        directDebitRejectionToDoRepository.save(rejectionToDo);
                    }

                    List<DirectDebitStatus> ddStatuses = Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.PENDING_DATA_ENTRY);
                    //IN_COMPLETE or PENDING_DATA_ENTRY or (PENDING and NOT_SENT)
                    if (directDebit.getDirectDebitFiles() != null) {
                        for (DirectDebitFile ddf : directDebit.getDirectDebitFiles().stream().filter(ddf -> ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment() &&
                                (ddStatuses.contains(ddf.getDdStatus()) || (ddf.getDdStatus().equals(DirectDebitStatus.PENDING) && ddf.getStatus().equals(DirectDebitFileStatus.NOT_SENT))))
                                .collect(Collectors.toList())) {
                            logger.log(Level.INFO, "cancelling ddf#" + ddf.getId() + ", with DD Status: " + ddf.getDdStatus().getValue());
                            ddf.setIgnoreDDRejectionFlow(true);
                            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                                .cancelDDf(ddf, DirectDebitCancellationToDoReason.BOUNCED_PAYMENT_REPLACEMENT);
                            logger.log(Level.INFO, "canceled");
                        }
                    }
                } else {
                    logger.log(Level.INFO, "isThereOtherBouncedNotReplacedPayment = TRUE");
                }
            }
            // ACC-3005
            ddMessagingToDoCtrl.closeRelatedToDos(entity.getId(), Arrays.asList(DDMessagingType.BouncedPayment));

        } else if (entity.getStatus().equals(PaymentStatus.RECEIVED)) {
            logger.info(prefix + "Payment is changed to Received");
            
            // ACC-3968
            map.putAll(Setup.getApplicationContext().getBean(PaymentService.class).processACC3968(entity));
            
            map.putAll(checkIfContractCancelledWithinFirstXDays(entity));

            DirectDebitGenerationPlanService directDebitGenerationPlanService = Setup.getApplicationContext()
                    .getBean(DirectDebitGenerationPlanService.class);
            directDebitGenerationPlanService.hideGenerationPlanPushNotification(entity);

            if (!entity.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)) {
            //ACC-4941
            PicklistItem paymentType = Setup.getRepository(PicklistItemRepository.class)
                    .findOne(entity.getTypeOfPayment().getId());

                if ((map.get("isReplacement") == null || !(boolean) map.get("isReplacement")) &&
                        paymentType.getCode().equals("monthly_payment")) {

                    Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                            .create(new BackgroundTask.builder(
                                "ValidateStopDdRejectionFlowAfterPaymentPdc" + new java.util.Date().getTime(),
                                "accounting",
                                "directDebitRejectionFlowService",
                                "validateStopDdRejectionFlowAfterPaymentPdc")
                                .withRelatedEntity("Payment", entity.getId())
                                .withParameters(
                                        new Class[] {Long.class},
                                        new Object[] {entity.getId()})
                                .withQueue(BackgroundTaskQueues.SequentialQueue)
                                .withDelay(60L * 1000L)
                                .build());
                }
            }

            disableOnlineCardPaymentLinksOnPaymentReceived(entity);

            // ACC-6587
            Setup.getApplicationContext()
                    .getBean(PaymentReceivedNotificationHandler.class)
                    .handlePayment(entity);

            // ACC-8228
            firstPaymentReceivedRule(entity, contract);

            // ACC-8796
            // If the MV contract Not flagged as pre-collected then client pay the pre-collected salary payment type, system should flag it.
            Setup.getApplicationContext()
                    .getBean(MaidVisaFailedMedicalCheckService.class)
                    .handlePreCollectedContract(entity, contract);
        }

        updateContractPaymentPaid(entity); // ACC-6055

        checkConditionalRefunds(entity); // ACC-3168

        checkRelatedDD(entity); // ACC-3309

        checkSwitchingBankAccount(entity); // ACC-3546

        checkOECFlow(entity); // ACC-3974

        return map;
    }

    private void updateContractPaymentPaid(Payment entity) {
        if(!entity.getMethodOfPayment().equals(PaymentMethod.CARD) || !entity.isOnline()) return;

        ContractPaymentRepository contractPaymentRep = Setup.getRepository(ContractPaymentRepository.class);

        List<ContractPayment> contractPayments = contractPaymentRep.findAllMatchedContractPayment(
                entity.getContract().getId(), entity.getMethodOfPayment(), entity.getTypeOfPayment().getId(),
                new DateTime(entity.getDateOfPayment()).withHourOfDay(0).withMinuteOfHour(0)
                        .withSecondOfMinute(0).toDate(),
                new DateTime(entity.getDateOfPayment()).withHourOfDay(23).withMinuteOfHour(59)
                        .withSecondOfMinute(59).toDate(),
                entity.getAmountOfPayment());

        if (contractPayments != null && !contractPayments.isEmpty()) {
            contractPayments.get(0).setPaid(Boolean.TRUE);
            contractPaymentRep.save(contractPayments.get(0));
        }
    }

    // ACC-3168
    private void checkConditionalRefunds(Payment entity) {
        logger.info("Check Conditional Refunds");
        ClientRefundTodoRepository clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
        List<ClientRefundToDo> clientRefundToDos = clientRefundTodoRepository.findByConditionalRefundAndRequiredPayment(entity);
        
        for (ClientRefundToDo clientRefundToDo : clientRefundToDos) {
            if (clientRefundToDo.getRequiredPayments().stream()
                    .anyMatch(p -> !p.equals(entity) &&
                            !p.getStatus().equals(PaymentStatus.RECEIVED) &&
                            !p.isReplaced())) {

                logger.info("Payments are still not received -> do nothing");
                continue;
            }

            logger.info("No more Required Payments -> continue with Client Refund#" + clientRefundToDo.getId());

            clientRefundToDo.setCheckForRequiredPayments(false);

            // ACC-7611
            // set transfer reference from conditional payment upon received
            if (ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL.toString()
                    .equals(clientRefundToDo.getTaskName()) ||
                    ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL
                            .toString().equals(clientRefundToDo.getTaskName())) {

                if (clientRefundToDo.isConditionalRefund() && !clientRefundToDo.getRequiredPayments().isEmpty() &&
                        (clientRefundToDo.getTransferReference() == null || clientRefundToDo.getTransferReference().isEmpty())) {

                    List<ContractPaymentConfirmationToDo> l = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                            .findTodosByPayment(clientRefundToDo.getRequiredPayments().get(0).getId());
                    if (!l.isEmpty() && l.get(0).getTransferReference() != null && !l.get(0).getTransferReference().isEmpty()) {
                        clientRefundToDo.setTransferReference(l.get(0).getTransferReference());
                    }
                }

                switch (ClientRefundTodoType.valueOf(clientRefundToDo.getTaskName())) {
                    case CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL:
                        clientRefundToDo.setTaskName(ClientRefundTodoType.WAITING_COO_APPROVAL.toString());
                        clientRefundTodoRepository.save(clientRefundToDo);
                        break;
                    case CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL:
                        clientRefundToDo.setTaskName(ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString());
                        clientRefundTodoRepository.save(clientRefundToDo);
                        Setup.getApplicationContext()
                                .getBean(ClientRefundService.class)
                                .sendNotificationToManagerUponRefundCreated(clientRefundToDo, clientRefundToDo.getPurpose());
                        break;
                }
                continue;
            }

            // Create PayrollTodo or ConfirmationTodo
            Setup.getApplicationContext()
                    .getBean(ClientRefundService.class)
                    .createTodoUponAutoApprove(clientRefundToDo);
        }
    }

    private void checkRelatedDD(Payment entity) {
        logger.info("Check Related DD");

        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
            DirectDebit directDebit = Setup.getRepository(DirectDebitRepository.class).findOne(entity.getDirectDebit().getId());
            if (directDebit == null) {
                logger.log(Level.SEVERE, prefix + "Related DD Not Found");
                return;
            }

            if (directDebit.getCategory().equals(DirectDebitCategory.A)) {
                logger.info("Related DD#" + directDebit.getId() + " is DDA -> set Status as EXPIRED");

                if (!Setup.getRepository(PaymentRepository.class)
                        .existsByDirectDebitIdAndStatusNotInAndReplacedAndIdNotIn(
                                directDebit.getId(), Arrays.asList(PaymentStatus.RECEIVED), false, Arrays.asList(entity.getId()))) {
                    
                    Setup.getApplicationContext().getBean(DirectDebitController.class)
                            .runDDExpiredJob(directDebit.getId());
                } else {
                    logger.info("DD still has not Received/Replaced Payments");
                }
            }
        }
    }

    //Jirra ACC-3546
    public void checkSwitchingBankAccount(Payment entity) {
        Setup.getApplicationContext().getBean(SwitchingBankAccountService.class)
                .handleReceivingPaymentStatus(entity);
    }

    //Jirra ACC-3555
    public Map checkIfContractCancelledWithinFirstXDays(Payment entity) {
        Map map = new HashMap();
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());

        logger.info("is Contract Cancelled within First X Days: " + contract.isCancelledWithinFirstXDays());

        if (!contract.isCancelledWithinFirstXDays()) return map;

        AccountingEntityPropertyRepository accountingEntityPropertyRepository =
                Setup.getRepository(AccountingEntityPropertyRepository.class);

        if (accountingEntityPropertyRepository.existsByOriginAndKeys(
                entity, Arrays.asList(AccountingModuleMainJob.DELAYED_REFUND))) return map;

        AccountingEntityProperty property = new AccountingEntityProperty();
        property.setKey(AccountingModuleMainJob.DELAYED_REFUND);
        property.setOrigin(entity);
        property.setPurpose("checkIfContractCancelledWithinFirstXDays");
        accountingEntityPropertyRepository.save(property);

        /*map.putAll(doFullRefund(contract, contract.getClient(), entity, entity.getAmountOfPayment()));
        createToDoToCancelFutureDD(contract);*/

        return map;
    }

    //Jirra ACC-3974
    public void checkOECFlow(Payment entity) {
        Setup.getApplicationContext().getBean(OecAmendDDsService.class).handleReceivingOldPaymentStatus(entity);
    }

    // ACC-6941
    private void disableOnlineCardPaymentLinksOnPaymentReceived(Payment entity) {
        if (!Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                .existsOnlineCardTodosByPayment(
                        entity.getContract().getId(),
                        entity.getTypeOfPayment().getId(),
                        entity.getDateOfPayment())) return;

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "disableOnlineCardPaymentLinksOnPaymentReceived_" + entity.getId(),
                        "accounting",
                        "paymentService",
                        "disableOnlineCardPaymentLinksOnPaymentReceived")
                        .withRelatedEntity("Payment", entity.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {entity.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    private void firstPaymentReceivedRule(Payment entity, Contract contract) {
        if (contract.getHousemaid() == null || contract.getHousemaid().getId() == null) {
            return;
        }

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "firstPaymentReceivedRule_Payment_" +
                                entity.getId() + "_Housemaid_" + contract.getHousemaid().getId(),
                        "visa",
                        "firstPaymentReceivedRule",
                        "execute")
                        .withRelatedEntity("Payment", entity.getId())
                        .withParameters(new Class[]  { Long.class },
                                new Object[] { contract.getHousemaid().getId() })
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());
    }
}
