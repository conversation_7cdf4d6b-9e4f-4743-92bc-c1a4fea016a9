package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.PaymentService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Created on 2019-01-29
 */

// From CM ACC-6103
@BusinessRule(moduleCode = "", entity = DirectDebit.class, events = {
        BusinessEvent.AfterUpdate}, fields = {"id", "type", "category", "status", "MStatus", "oecSalary",
            "manualDdfFile.id", "autoDdfFile.id", "generateRelatedPayments" , "contractPaymentTerm.contract.id"})

public class DirectDebitBusinessRule implements BusinessAction<DirectDebit> {

    @Override
    public boolean validate(DirectDebit entity, BusinessEvent event) {
        Logger logger = Logger.getLogger(DirectDebitBusinessRule.class.getName());
        logger.log(Level.INFO, "Direct Debit Business Rule Validation");
        logger.log(Level.INFO,"DD Status: " + entity.getStatus().toString());
        logger.log(Level.INFO,"M Status: " + entity.getMStatus().toString());
        logger.log(Level.INFO,"DD Category: " + entity.getCategory().toString());
        logger.log(Level.INFO,"A File: " +(entity.getAutoDdfFile() != null && entity.getAutoDdfFile().getId() != null?entity.getAutoDdfFile().getId():"null"));
        logger.log(Level.INFO,"M File: " +(entity.getManualDdfFile() != null && entity.getManualDdfFile().getId() != null?entity.getManualDdfFile().getId():"null"));


        return entity.getStatus().equals(DirectDebitStatus.CONFIRMED)
                || entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)
                || (entity.getCategory().equals(DirectDebitCategory.B) && (
                (entity.getStatus().equals(DirectDebitStatus.REJECTED) && entity.getMStatus().equals(DirectDebitStatus.REJECTED))
                        || (entity.getStatus().equals(DirectDebitStatus.CANCELED) && entity.getMStatus().equals(DirectDebitStatus.CANCELED))))
                ||
                (entity.getCategory().equals(DirectDebitCategory.A)
                        && (entity.getMStatus().equals(DirectDebitStatus.REJECTED) || (entity.getMStatus().equals(DirectDebitStatus.CANCELED))))
                || ( entity.getStatus() != null &&
                Arrays.asList(DirectDebitStatus.REJECTED , DirectDebitStatus.CANCELED).contains(entity.getStatus()) &&
                entity.getMStatus() != null && entity.getMStatus().equals(DirectDebitStatus.NOT_APPLICABLE) &&
                entity.getCategory() != null && entity.getCategory().equals(DirectDebitCategory.B));
    }

    @Override
    public Map<String, Object> execute(DirectDebit entity, BusinessEvent even) {
        Logger logger = Logger.getLogger(DirectDebitBusinessRule.class.getName());
        logger.log(Level.INFO, "Direct Debit Business Rule execution");
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebit directDebit = directDebitRepository.findOne(entity.getId());
        if(directDebit==null)
            return null;
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);
        DirectDebitFile manualDirectDebitFile = null, autoDirectDebitFile = null;
        if (entity.getManualDdfFile() != null && entity.getManualDdfFile().getId() != null)
            manualDirectDebitFile = Setup.getRepository(DirectDebitFileRepository.class).findOne(entity.getManualDdfFile().getId());
        if (entity.getAutoDdfFile() != null && entity.getAutoDdfFile().getId() != null)
            autoDirectDebitFile = Setup.getRepository(DirectDebitFileRepository.class).findOne(entity.getAutoDdfFile().getId());

        if (entity.getStatus().equals(DirectDebitStatus.CONFIRMED) || entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
            if (entity.getCategory().equals(DirectDebitCategory.A)) {
                if (entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                    logger.log(Level.INFO,"DD is Cat A and mStatus = Confirmed");
                    directDebit.transferPaymentsToPdpOrCreate(manualDirectDebitFile);
                }
            } else if (entity.getCategory().equals(DirectDebitCategory.B)) {
                if (entity.getStatus().equals(DirectDebitStatus.CONFIRMED)) {
                    directDebit.transferPaymentsToPdpOrCreate(autoDirectDebitFile);
                    logger.log(Level.INFO,"DD is Cat B and ddStatus = Confirmed");
                } else if (!entity.getStatus().equals(DirectDebitStatus.CONFIRMED)
                        && entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                    directDebit.transferPaymentsToPdpOrCreate(manualDirectDebitFile);
                    logger.log(Level.INFO,"DD is Cat B and mStatus = Confirmed and ddStatus != Confirmed");

                }
                if (entity.getStatus().equals(DirectDebitStatus.CONFIRMED)
                        && entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                    logger.log(Level.INFO,"ddStatus = Confirmed and the mStatus = Confirmed.");
                    if (autoDirectDebitFile != null && manualDirectDebitFile != null) {
                        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
                        List<Payment> pdpPaymentsLinkedToManualDirectDebitFile =
                                paymentRepository.findByDirectDebitIdAndDirectDebitFileIdAndStatus(
                                        directDebit.getId(), manualDirectDebitFile.getId(), PaymentStatus.PDC);
                        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
                        if (!pdpPaymentsLinkedToManualDirectDebitFile.isEmpty()) {
                            for (Payment payment : pdpPaymentsLinkedToManualDirectDebitFile) {
                                if(payment.getDateOfPayment().before(autoDirectDebitFile.getStartDate()))
                                    continue;
                                payment.setDirectDebitFileId(autoDirectDebitFile.getId());
                                paymentService.updatePaymentSilent(payment);
                            }
                        }
                        else {
                            logger.log(Level.INFO,"couldn't find any PDC payment linked to the manual file");
                        }
                    }
                    else {
                        logger.log(Level.INFO,"autoDirectDebitFile or manualDirectDebitFile not linked to DD ");
                    }
                }
            }
        } else {
            if((entity.getCategory().equals(DirectDebitCategory.B) && (
                    (entity.getStatus().equals(DirectDebitStatus.REJECTED) && entity.getMStatus().equals(DirectDebitStatus.REJECTED))
                            )) ||
                    (entity.getCategory().equals(DirectDebitCategory.A)
                            && (entity.getMStatus().equals(DirectDebitStatus.REJECTED) ))) {
                directDebit.deleteRelatedPrePdpPayments();
            } else {
                directDebit.deleteRelatedPrePdpAndPdpPayments();
            }

            if(directDebit.getContractPaymentTerm() != null && directDebit.getContractPaymentTerm().getContract() != null) {
               //ACC-6103 get the content of closeContractNotificationsByType directly
                Contract contract = Setup.getRepository(ContractRepository.class).findOne(directDebit.getContractPaymentTerm().getContract().getId());
                ArrayList<String> types = new ArrayList<String>(){{
                        add("CM_SMS_3_1_1_1_1_MAIDVISA_NOTIFICATION");
                        add("CM_SMS_3_1_1_1_2_MAIDVISA_NOTIFICATION");
                        add("CM_SMS_3_3_1_1_MAIDVISA_NOTIFICATION");
                        add("CM_SMS_3_3_2_1_MAIDVISA_NOTIFICATION");}};
                DisablePushNotificationRepository pushNotificationsRepository = Setup.getRepository(DisablePushNotificationRepository.class);
                List<PushNotification> notificationToUpdate = new ArrayList<>();
                List<PushNotification> pushNotifications = pushNotificationsRepository.
                        findByRecepientTypeAndOwnerIdAndOwnerTypeAndTypeIn(
                                contract.getClient().getId().toString(), "Client", contract.getId(), "Contract", types);
                for (PushNotification pushNotification : pushNotifications) {

                    pushNotification.setDisabled(true);
                    pushNotification.setReceived(true);
                    pushNotification.setLocation(NotificationLocation.INBOX);

                    notificationToUpdate.add(pushNotification);
                }
                if(!notificationToUpdate.isEmpty())
                    pushNotificationsRepository.save(notificationToUpdate);

            }
        }

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);

        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaidEndDate(entity.getContractPaymentTerm().getContract());
        return null;
    }

}